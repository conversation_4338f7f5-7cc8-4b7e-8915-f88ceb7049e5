vue
<template>
  <div class="min-h-screen bg-gray-50 py-8 px-4">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-8">
      <header class="mb-8">
        <h1 class="text-2xl font-medium mb-2">发布招募信息</h1>
        <p class="text-gray-500 text-sm"
          >项目发布成功后，招募类型、招募名称、招募截止日期不可修改</p
        >
      </header>
      <form @submit.prevent="handleSubmit">
        <div class="space-y-6">
          <!-- 招募类型 -->
          <div class="flex items-center gap-4">
            <span class="text-sm">招募类型</span>
            <el-radio-group v-model="formData.type">
              <el-radio label="市津正面奖" class="!rounded-button"
                >市津正面奖</el-radio
              >
              <el-radio label="黑奖" class="!rounded-button">黑奖</el-radio>
            </el-radio-group>
          </div>
          <!-- 项目招募名称 -->
          <div class="form-group">
            <label class="required">项目招募名称</label>
            <el-input
              v-model="formData.name"
              maxlength="100"
              show-word-limit
              placeholder="请输入项目招募名称"
            />
          </div>
          <!-- 招募中心和截止日期 -->
          <div class="grid grid-cols-2 gap-6">
            <div class="form-group">
              <label class="required">招募中心</label>
              <el-select v-model="formData.center" placeholder="请选择招募中心">
                <el-option
                  v-for="item in centers"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
            <div class="form-group">
              <label class="required">招募截止日期</label>
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                placeholder="选择招募截止日期"
              />
            </div>
          </div>
          <!-- 问题天数 -->
          <div class="form-group">
            <label class="required">问题天数</label>
            <div class="flex gap-4">
              <el-button
                :class="[
                  '!rounded-button whitespace-nowrap',
                  { '!bg-blue-500 !text-white': formData.days === 30 }
                ]"
                @click="formData.days = 30"
              >
                30天
              </el-button>
              <el-button
                :class="[
                  '!rounded-button whitespace-nowrap',
                  { '!bg-blue-500 !text-white': formData.days === 90 }
                ]"
                @click="formData.days = 90"
              >
                90天
              </el-button>
            </div>
          </div>
          <!-- 计划完成日期和药物名称 -->
          <div class="grid grid-cols-2 gap-6">
            <div class="form-group">
              <label class="required">计划完成日期</label>
              <el-date-picker
                v-model="formData.completionDate"
                type="date"
                placeholder="请选择计划完成日期"
              />
            </div>
            <div class="form-group">
              <label class="required">药物名称</label>
              <el-input
                v-model="formData.medicineName"
                maxlength="100"
                show-word-limit
                placeholder="请输入药物名称"
              />
            </div>
          </div>
          <!-- 达成匹和用药方式 -->
          <div class="grid grid-cols-2 gap-6">
            <div class="form-group">
              <label class="required">达成匹</label>
              <el-input
                v-model="formData.achievement"
                maxlength="100"
                show-word-limit
                placeholder="请输入达成匹"
              />
            </div>
            <div class="form-group">
              <label>用药方式</label>
              <el-input
                v-model="formData.medicineMethod"
                placeholder="请输入用药方式"
              />
            </div>
          </div>
          <!-- 项目介绍 -->
          <div class="form-group">
            <label>项目介绍</label>
            <el-input
              type="textarea"
              v-model="formData.introduction"
              :rows="4"
              placeholder="可以通过项目介绍向医生表明本项目的背景和研究内容"
            />
          </div>
          <!-- 项目准则 -->
          <div class="form-group">
            <label>项目准则</label>
            <el-input
              type="textarea"
              v-model="formData.guidelines"
              :rows="4"
              placeholder="请输入项目准则"
            />
          </div>
          <!-- 可能会出现的不良反应 -->
          <div class="form-group">
            <label>可能会出现的不良反应</label>
            <el-input
              type="textarea"
              v-model="formData.sideEffects"
              :rows="4"
              placeholder="请输入可能会出现的不良反应"
            />
          </div>
          <!-- 体检项目 -->
          <div class="form-group">
            <label>体检项目</label>
            <el-input
              type="textarea"
              v-model="formData.examination"
              :rows="4"
              placeholder="请输入体检项目"
            />
          </div>
          <!-- 性别要求 -->
          <div class="form-group">
            <label class="required">性别</label>
            <el-radio-group v-model="formData.gender">
              <el-radio label="male" class="!rounded-button">男性</el-radio>
              <el-radio label="female" class="!rounded-button">女性</el-radio>
              <el-radio label="both" class="!rounded-button">男女不限</el-radio>
            </el-radio-group>
          </div>
          <!-- 提交按钮 -->
          <div class="flex justify-center pt-6">
            <el-button
              type="primary"
              class="!rounded-button whitespace-nowrap px-8"
              @click="handleSubmit"
            >
              提交
            </el-button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>
<script setup>
  import { ref } from 'vue';
  import { ElMessage } from 'element-plus';
  const centers = [
    { value: 'center1', label: '第一招募中心' },
    { value: 'center2', label: '第二招募中心' },
    { value: 'center3', label: '第三招募中心' }
  ];
  const formData = ref({
    type: '市津正面奖',
    name: '',
    center: '',
    endDate: '',
    days: 30,
    completionDate: '',
    medicineName: '',
    achievement: '',
    medicineMethod: '',
    introduction: '',
    guidelines: '',
    sideEffects: '',
    examination: '',
    gender: 'both'
  });
  const handleSubmit = () => {
    if (
      !formData.value.name ||
      !formData.value.center ||
      !formData.value.endDate
    ) {
      ElMessage.error('请填写必填项');
      return;
    }
    ElMessage.success('提交成功');
  };
</script>
<style lang="scss" scoped>
  .form-group {
    margin-bottom: 1rem;
    label {
      display: block;
      font-size: 0.875rem; // 14px
      color: #4a5568; // text-gray-700
      &.required::before {
        content: '*';
        color: #e53e3e; // text-red-500
        margin-right: 0.25rem; // mr-1
      }
    }
  }
  .el-input__wrapper {
    box-shadow: none;
    border: 1px solid #cbd5e0; // border-gray-300
    transition: border-color 0.2s;
    &:hover {
      border-color: #a0aec0; // hover:border-gray-400
    }
  }
  .el-textarea__inner {
    box-shadow: none;
    border: 1px solid #cbd5e0; // border-gray-300
    transition: border-color 0.2s;
    &:hover {
      border-color: #a0aec0; // hover:border-gray-400
    }
  }
  .el-select {
    width: 100%;
  }
  .el-date-editor {
    width: 100%;
  }
  .el-radio__label {
    font-size: 0.875rem; // text-sm
  }
</style>
