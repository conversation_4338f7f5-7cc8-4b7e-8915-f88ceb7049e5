<template>
  <div>
    <ele-modal
      form
      width="500px"
      :model-value="modelValue"
      title="编辑"
      @update:modelValue="updateModelValue"
    >
      <el-form
        ref="formRef"
        label-position="left"
        label-width="90px"
        :model="form"
        :rules="rules"
      >
        <el-form-item label="姓名">
          <el-input
            placeholder="请输入姓名"
            v-model="subjectsName"
            :disabled="true"
            maxlength="99"
          />
        </el-form-item>
        <el-form-item label="有效期至" prop="indate">
          <el-date-picker
            type="date"
            v-model="form.indate"
            placeholder="请选择有效期"
            value-format="YYYY-MM-DD"
            clearable
            style="width: 100%"
            :disabled-date="disabledDate"
          />
        </el-form-item>
        <el-form-item label="标记原因" prop="explain">
          <el-input
            type="textarea"
            v-model="form.explain"
            placeholder="请输入标记原因"
            clearable
            show-word-limit
            :autosize="{
              minRows: 3,
              maxRows: 6
            }"
            maxlength="500"
          />
        </el-form-item>
        <el-form-item label="密码确认" prop="password">
          <el-input
            show-password
            type="password"
            autocomplete="new-password"
            v-model="form.password"
            placeholder="请输入密码"
            clearable
            maxlength="16"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="footer-btn">
          <el-button type="info" @click="updateModelValue(false)"
            >取 消</el-button
          >
          <el-button type="primary" :loading="loading" @click="save">
            确 定
          </el-button>
        </div>
      </template>
    </ele-modal>
  </div>
</template>

<script setup>
  import { subjectblackUpdate } from '@/api/subjects/unsuitPeople/index.js';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { ref, reactive, watch, nextTick } from 'vue';
  import { encrypt, decrypt } from '@/utils/jsencrypt';
  import { useFormData } from '@/utils/use-form-data';
  const emit = defineEmits(['done', 'update:modelValue']);
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    data: Object
  });
  // 表单实例
  const formRef = ref(null);
  const subjectsName = ref('');
  const isUpdate = ref(false);
  const loading = ref(false);
  // 表单数据
  const { form, resetFields, assignFields } = useFormData({
    guid: undefined,
    indate: undefined,
    password: undefined,
    explain: undefined
  });
  // 表单验证规则
  const rules = reactive({
    indate: [
      { required: true, message: '有效期至不能为空', trigger: 'change' }
    ],
    explain: [
      { required: true, message: '标记原因不能为空', trigger: 'change' }
    ],
    password: [
      {
        required: true,
        message: '密码确认不能为空',
        type: 'string',
        trigger: 'change'
      }
    ]
  });
const disabledDate = (time) => {
    const maxDate = new Date('9999-12-31');
    return time.getTime() < Date.now() || time.getTime() > maxDate.getTime(); // - 8.64e7是今天可以选
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      let params = {
        guid: form.guid,
        indate: form.indate,
        password: encrypt(form.password),
        explain: form.explain
      };
      subjectblackUpdate(params).then((res) => {
        if (res.code == 200) {
          EleMessage.success('操作成功');
          updateModelValue(false);
          emit('done');
          resetFields();
          loading.value = false;
        } else {
          EleMessage.error(res.msg);
          loading.value = false;
        }
      });
    });
  };
  nextTick(() => {
    form.guid = props.data.guid;
    subjectsName.value = props.data.subjectsName;
    assignFields(props.data);
  });
  watch(
    () => props.modelValue,
    async (modelValue) => {
      if (modelValue) {
        if (props.data) {
          form.guid = props.data.guid;
          subjectsName.value = props.data.subjectsName;
          assignFields(props.data);
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .footer-btn {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
