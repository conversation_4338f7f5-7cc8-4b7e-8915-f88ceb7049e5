<template>
  <ele-page flex-table :multi-card="false" hide-footer>
    <ele-card flex-table :body-style="{ padding: '24px' }"
      ><h1>网筛查询</h1>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        @submit.prevent=""
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input
            v-model="form.idCard"
            :maxlength="18"
            placeholder="请输入身份证号"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleOk()"
            >查询</el-button
          >
        </el-form-item>
      </el-form>
      <div v-if="result.days">
        <h2>查询结果</h2>
        <p style="font-size: 18px">姓名: {{ result.userName }}</p>
        <p style="font-size: 18px">身份证号: {{ result.idCard }}</p>
        <p style="font-size: 18px">间隔期天数: {{ result.days }} 天</p>
      </div>
      <div v-if="!result.days && result.idCard" style="color: red">
        <h2>查询结果： 查无记录</h2>
      </div>
    </ele-card>
  </ele-page>
</template>

<script setup name="screenQuery">
  import { ref, reactive } from 'vue';
  import { getUserIntervalDay } from '@/api/project/index';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { validIdCard } from '@/utils/common.js';

  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  const result = ref({});

  const form = ref({
    name: '',
    idCard: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    name: [
      {
        required: true,
        message: '请输入姓名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    idCard: [
      {
        required: true,
        message: '请输入身份证号',
        trigger: 'blur'
      }
    ]
  });

  /** 保存修改 */
  const handleOk = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      if (form.value.idCard) {
        form.value.idCard = form.value.idCard.replace(/\s+/g, '');
        if (!validIdCard(form.value.idCard)) {
          EleMessage.error('请输入完整的身份证号');
          return;
        }
      }
      try {
        loading.value = true;
        const params = { name: form.value?.name, idCard: form.value?.idCard };
        const res = await getUserIntervalDay(params);
        console.log(res);
        Object.assign(result.value, res);
        loading.value = false;
        EleMessage.success('查询成功');
      } catch (e) {
        loading.value = false;
        EleMessage.error(e.message);
      }
    });
  };
</script>
<style lang="scss" scoped></style>
