<template>
  <ele-card
    flex-table
    :body-style="{ padding: 0 }"
    style="height: calc(100vh - 81px - 14px); background: transparent"
  >
    <ele-card
      flex-table
      :body-style="{ padding: '20px 20px 20px 20px', overflow: 'hidden' }"
    >
      <div style="margin-bottom: 12px">
        <changeProject
          @done="projectChange"
          :projectInfo="trialsDataInfo"
          :orgDisabled="true"
        />
      </div>

      <div
        class="tableForm"
        :style="trialsDataInfo.guid ? '' : 'margin-bottom: 12px'"
        v-if="trialsDataInfo.status != 2"
      >
        <el-form inline :model="params">
          <el-form-item label="姓名：">
            <el-input
              v-model="params.name"
              placeholder="请输入姓名"
              clearable
              style="width: 200px"
              maxlength="99"
            />
          </el-form-item>
          <!-- <el-form-item label="身份证号：">
            <el-input
              v-model="params.idcard"
              placeholder="请输入身份证号"
              clearable
              style="width: 200px"
              maxlength="99"
            />
          </el-form-item> -->
          <el-form-item label="状态：">
            <el-select
              v-model="params.status"
              placeholder="请选择状态"
              clearable
              style="width: 200px"
            >
              <el-option label="初查不合格" value="0" />

              <el-option label="筛选" value="1" />
              <el-option label="入组前筛选不合格" value="8" />
              <el-option label="未入组" value="5" />
              <el-option label="待入组" value="7" />
              <el-option label="入组" value="2" />
              <el-option label="入组未给药" value="6" />
              <el-option label="出组" value="4" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="reload">搜索</el-button>
            <el-button type="primary" plain @click="showHighSearch = true"
              >高级检索</el-button
            >
            <el-button type="info" @click="(params = {}), reload()"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <ele-pro-table
        ref="tableRef"
        row-key="uuid"
        :columns="columns"
        :datasource="datasource"
        :toolbar="trialsDataInfo.guid ? true : false"
        :loadOnCreated="false"
        highlight-current-row
        :bottom-line="tableFullHeight"
        cache-key="workAuditCheckTable7"
        v-model:selections="selectionsList"
      >
        <template #toolbar>
          <div
            style="margin-bottom: 4px"
            v-if="trialsDataInfo.status != 2 && trialsDataInfo.guid"
          >
            <el-button
              type="primary"
              plain
              @click="toSigin()"
              style="
                background-color: #fff7e8;
                color: #ffb457;
                border-color: #ffb457;
              "
            >
              筛选
            </el-button>
            <el-button
              type="primary"
              plain
              @click="toBeforeSigin"
              style="
                background-color: #fff7e8;
                color: #ffb457;
                border-color: #ffb457;
              "
            >
              入组前筛选
            </el-button>
            <el-button
              type="primary"
              plain
              @click="(trialsType = '3'), (showTest = true)"
              style="
                background-color: #fff7e8;
                color: #ffb457;
                border-color: #ffb457;
              "
            >
              人员核对
            </el-button>
            <el-button type="primary" plain @click="markNotJoin">
              标记未入组
            </el-button>
            <el-button type="primary" plain @click="markJoin">
              标记入组
            </el-button>
            <el-button type="primary" plain @click="markOutOrEdit('out')">
              标记出组
            </el-button>
            <el-button type="primary" plain @click="markOutOrEdit('edit')">
              批量编辑
            </el-button>
            <el-button type="primary" plain @click="printCrad">
              打印卡牌
            </el-button>
          </div>
          <div
            class="tableForm"
            :style="trialsDataInfo.guid ? '' : 'margin-bottom: 12px'"
            v-else
          >
            <el-form inline :model="params">
              <el-form-item label="姓名：">
                <el-input
                  v-model="params.name"
                  placeholder="请输入姓名"
                  clearable
                  style="width: 200px"
                  maxlength="99"
                />
              </el-form-item>
              <!-- <el-form-item label="身份证号：">
                <el-input
                  v-model="params.idcard"
                  placeholder="请输入身份证号"
                  clearable
                  style="width: 200px"
                  maxlength="99"
                />
              </el-form-item> -->
              <el-form-item label="状态：">
                <el-select
                  v-model="params.status"
                  placeholder="请选择状态"
                  clearable
                  style="width: 200px"
                >
                  <el-option label="初查不合格" value="0" />
                  <el-option label="筛选" value="1" />
                  <el-option label="入组前筛选不合格" value="8" />
                  <el-option label="未入组" value="5" />
                  <el-option label="待入组" value="7" />
                  <el-option label="入组" value="2" />
                  <el-option label="入组未给药" value="6" />
                  <el-option label="出组" value="4" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" plain @click="reload">搜索</el-button>
                <el-button type="primary" plain @click="showHighSearch = true"
                  >高级检索</el-button
                >
                <el-button type="info" @click="(params = {}), reload()"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </template>
        <template #tools>
          <!-- <span style="margin-right: 10px">显示模式</span> -->
          <div class="ele-tool" @click="peopleHistoryChange">
            <el-tooltip
              effect="dark"
              content="人员核对历史记录"
              placement="top"
            >
              <img
                src="@/assets/renyuan-hedui.svg"
                alt=""
                style="width: 19px"
              />
            </el-tooltip>
          </div>
          <div class="ele-tool" @click="dialogVisible = true">
            <img src="@/assets/exportIcon.svg" alt="" style="width: 19px" />
          </div>
        </template>
        <template #status="{ row }">
          <span style="color: #c72727" v-if="row.status == 0">初查不合格</span>
          <span style="color: #67c32a" v-if="row.status == 1">筛选</span>
          <span style="color: #67c32a" v-if="row.status == 2">入组</span>
          <span style="color: #3ed47b" v-if="row.status == 3">完成</span>
          <span style="color: #999999" v-if="row.status == 4">出组</span>
          <span style="color: #999999" v-if="row.status == 5">未入组</span>
          <span style="color: #999999" v-if="row.status == 6">入组未给药</span>
          <span style="color: #67c32a" v-if="row.status == 7">待入组</span>
          <span style="color: #c72727" v-if="row.status == 8"
            >入组前筛查不合格
          </span>
        </template>
        <template #idcard="{ row }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1">{{
              $PhoneOrIdCrad(row.idcard, 'idCard')
            }}</div>
            <img
              src="@/assets/subjects/eyeOpen.png"
              style="
                transform: scale(1.28);
                cursor: pointer;
                margin: 0 5px;
                width: 14px;
                height: 14px;
              "
              v-if="row.showEye"
              @click="getShowEye(row)"
            />
            <img
              src="@/assets/subjects/eyeClose.png"
              style="
                transform: scale(1.28);
                cursor: pointer;
                margin: 0 5px;
                width: 14px;
                height: 14px;
              "
              v-if="!row.showEye"
              @click="getShowEye(row)"
            />
          </div>
        </template>
        <template #similaritytype="{ row }">
          <span
            >{{
              row.similaritytype == 1
                ? '高'
                : row.similaritytype == 2
                  ? '中'
                  : row.similaritytype == 3
                    ? '低'
                    : ''
            }}
            {{
              row.similaritytype2 == 1
                ? '/ 高'
                : row.similaritytype2 == 2
                  ? '/ 中'
                  : row.similaritytype2 == 3
                    ? '/ 低'
                    : ''
            }}</span
          >
        </template>
        <template #action="{ row }">
          <tableBtn
            :key="row.uuid"
            :items="tableBtnList(row)"
            @routeEvent="routeEvent"
            :operateItem="row"
            :maxLength="1"
          />
        </template>
      </ele-pro-table>
    </ele-card>
    <highSearchModal
      v-model="showHighSearch"
      :data="params"
      @done="highSearchLoad"
      v-if="showHighSearch"
    />
    <notJoinModal
      v-model="showNotJoin"
      :data="selectList"
      @done="reload"
      v-if="showNotJoin"
    />
    <joinModal
      v-model="showJoin"
      :data="selectList"
      @done="reload"
      v-if="showJoin"
    />
    <outOrEditModal
      v-model="showOutOrEdit"
      :data="selectList"
      :type="markOutOrEditType"
      @done="reload"
      v-if="showOutOrEdit"
    />
    <editUserInfoModal
      v-model="showEditUserInfo"
      :data="selectRow"
      @done="reload"
      v-if="showEditUserInfo"
    />
    <memoModal
      v-model="showMemo"
      :data="selectRow"
      @done="reload"
      v-if="showMemo"
    />
    <testRecordModal
      v-model="showTestRecord"
      :data="selectRow"
      @done="reload"
      v-if="showTestRecord"
    />
    <testInfoModal
      v-model="showTestInfo"
      :data="selectRow"
      @done="reload"
      v-if="showTestInfo"
    />

    <inaptitudeModal
      v-model="showInaptitude"
      :data="selectRow"
      @done="reload"
      v-if="showInaptitude"
    />
    <testModal
      v-model="showTest"
      :data="trialsDataInfo"
      :type="trialsType"
      @done="reload"
      v-if="showTest"
    />
    <printCradModal
      v-model="showPrintCrad"
      :data="selectionsList"
      @done="reload"
      v-if="showPrintCrad"
    />
    <el-dialog v-model="dialogVisible" title="导出" width="550">
      <el-form label-width="auto">
        <el-form-item label="筛选时间格式：">
          <el-radio-group v-model="exportType">
            <el-radio value="1">yyyy-MM-dd</el-radio>
            <el-radio value="2">yyyy-MM-dd HH:mm:ss</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="敏感信息脱敏：">
          <el-radio-group v-model="desensitizationType">
            <el-radio value="1">否</el-radio>
            <el-radio value="2">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer flexCenter">
          <el-button type="info" @click="dialogVisible = false"
            >取 消</el-button
          >
          <el-button
            type="primary"
            @click="sendSubjecttrialExport"
            :loading="loading"
          >
            {{ loading ? '导出中' : '导 出' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
    <peopleHistoryModal
      :data="HistoryInfo"
      v-model="showPeopleHistoryInfo"
      v-if="showPeopleHistoryInfo"
    />
    <el-dialog
      v-model="centerDialogVisible"
      title="重 要 消 息"
      width="1000"
      top="8vh"
      style="color: red !important"
      center
    >
      <div style="display: flex; justify-content: center; margin-top: 15px">
        <el-image
          style="width: 780px; height: 560px"
          :src="trialsType == 1 ? isSiginUrl1 : isSiginUrl"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="
            trialsType == 1 ? circleDetailsImages1 : circleDetailsImages
          "
          show-progress
          :initial-index="6"
          :url-list="
            trialsType == 1 ? circleDetailsImages1 : circleDetailsImages
          "
          fit="cover"
        />
      </div>
      <div v-if="trialsType == 1" style="display: flex; justify-content: center"
        ><el-checkbox v-model="remember">
          <span style="font-size: 18px; font-weight: 600"
            >我已知道，不再提醒</span
          >
        </el-checkbox>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="toDownload()"> 下载图片 </el-button>
          <el-button type="primary" @click="toSigin2()"> 开始筛选 </el-button>
        </div>
      </template>
    </el-dialog>
  </ele-card>
</template>
<script setup>
  import { Check, Close } from '@element-plus/icons-vue';
  import { ref, nextTick, computed, watch } from 'vue';
  import {
    trialsList,
    subjecttrialList,
    release,
    markFalloff,
    markSubjectTrialList,
    subjecttrialExport,
    getCompleteInfo
  } from '@/api/subjects/studyManage/index.js';
  import { validIdCard } from '@/utils/common.js';
  import { Search } from '@element-plus/icons-vue';
  import { Icon } from '@iconify/vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { ElMessageBox } from 'element-plus';
  import changeProject from '@/views/subjects/components/changeProject/index.vue';
  import highSearchModal from './components/highSearchModal.vue';
  import notJoinModal from './components/notJoinModal.vue';
  import joinModal from './components/joinModal.vue';
  import outOrEditModal from './components/outOrEditModal.vue';
  import editUserInfoModal from './components/editUserInfoModal.vue';
  import memoModal from './components/memoModal.vue';
  import inaptitudeModal from './components/inaptitudeModal.vue';
  import testRecordModal from './components/testRecordModal.vue';
  import testModal from './components/testModal.vue';
  import printCradModal from './components/printCradModal.vue';
  import testInfoModal from './components/testInfoModal.vue';
  import tableBtn from '@/views/subjects/components/tableBtn/index.vue';
  import peopleHistoryModal from './components/peopleHistoryModal.vue';

  import { debounce } from 'lodash';
  import { useRouter, useRoute } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';

  const isSiginUrl = ref('https://ibank.gcpcrc.com/zmt/warn-join-filter.png');

  const circleDetailsImages = ref([
    'https://ibank.gcpcrc.com/zmt/warn-join-filter.png'
  ]);

  const isSiginUrl1 = ref('https://ibank.gcpcrc.com/zmt/warn-filter.png');

  const circleDetailsImages1 = ref([
    'https://ibank.gcpcrc.com/zmt/warn-filter.png'
  ]);

  const userStore = useUserStore();
  const route = useRoute();
  const loginUser = computed(() => userStore.info ?? {});

  nextTick(() => {
    console.log('nextTick===========>', loginUser.value);
    if (loginUser.value.role != 99) {
      trialsDataInfo.value.projectId = loginUser.value.organizationGuid;
      trialsDataInfo.value.projectName = loginUser.value.organizationName;
    }
  });
  const remember = ref(false);
  const dialogVisible = ref(false);
  const centerDialogVisible = ref(false);

  const exportType = ref('1');
  const desensitizationType = ref('1');
  const tableBtnList = (row) => {
    const list = [
      {
        name: '详情',
        fun: 'testInfo',
        status: true,
        projectStatus: true
      },
      {
        name: '编辑',
        fun: 'editUserInfo',
        projectStatus: trialsDataInfo.value.status == '1',
        status:
          row.status == 1 ||
          row.status == 2 ||
          row.status == 7 ||
          row.status == 0
        //仅（筛选、入组、初查不合格、待入组）显示
      },
      {
        name: '备忘录',
        fun: 'handleCommand',
        key: 'a',
        projectStatus: trialsDataInfo.value.status == '1',
        status: row.status == 2
        //仅（入组）显示
      },
      {
        name: '试验记录',
        fun: 'handleCommand',
        key: 'b',
        projectStatus: trialsDataInfo.value.status == '1',
        status: row.trialFlag == 1
        //根据后台下发的字段判断 trialFlag==1显示 trialFlag==0不现实
      },
      {
        name: '放行',
        fun: 'handleCommand',
        key: 'c',
        projectStatus: trialsDataInfo.value.status == '1',
        status:
          (row.status == 0 || row.status == 8) &&
          row.releaseFlag == true
        //仅（初查不合格、入组前筛查不合格并且row.releaseFlag==true）显示
      },
      {
        name: '标记不适宜',
        fun: 'handleCommand',
        key: 'd',
        projectStatus: trialsDataInfo.value.status == '1',
        status: true
      },
      {
        name: '标记入组未给药',
        fun: 'handleCommand',
        key: 'e',
        projectStatus: trialsDataInfo.value.status == '1',
        status: row.status == 2
        //仅（入组）显示
      }
    ];
    return list.filter(
      (val) => val.status == true && val.projectStatus == true
    );
  };
  const toBeforeSigin = () => {
    trialsType.value = '2';
    // 判断项目的创建日期，是否小于指定日期，小于则不弹出
    if (trialsDataInfo.value.createDate) {
      const createDate = new Date(trialsDataInfo.value.createDate);
      const panDate = new Date('2025-07-25');
      if (createDate <= panDate) {
        centerDialogVisible.value = false;
        showTest.value = true;
        return;
      }
    }
    if (loginUser.value.filtersignin == 0) {
      centerDialogVisible.value = true;
    } else {
      showTest.value = true;
    }
  };
  const toSigin = () => {
    trialsType.value = '1';
    // 判断项目的创建日期，是否小于指定日期，小于则不弹出
    if (trialsDataInfo.value.createDate) {
      const createDate = new Date(trialsDataInfo.value.createDate);
      const panDate = new Date('2025-07-25');
      if (createDate <= panDate) {
        centerDialogVisible.value = false;
        showTest.value = true;
        return;
      }
    }
    //filterDialogVisible.value = true;
    //return;
    if (localStorage.getItem(trialsDataInfo.value.guid) == 1) {
      centerDialogVisible.value = false;
      showTest.value = true;
    } else {
      showTest.value = false;
      if (loginUser.value.filtersignin == 0) {
        centerDialogVisible.value = true;
      } else {
        showTest.value = true;
      }
    }
  };

  const toSigin2 = () => {
    if (remember.value) {
      localStorage.setItem(trialsDataInfo.value.guid, 1);
    }
    centerDialogVisible.value = false;
    showTest.value = true;
    remember.value = false;
  };

  const toDownload = () => {
    const link = document.createElement('a');
    link.href = isSiginUrl.value;
    link.download = '智募通签到指引.png';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const routeEvent = (row, item) => {
    if (item.fun == 'testInfo') {
      testInfo(row);
      return;
    }
    if (item.fun == 'editUserInfo') {
      editUserInfo(row);
      return;
    }
    if (item.fun == 'handleCommand') {
      handleCommand(item.key, row);
    }
  };
  //详情
  const showTestInfo = ref(false);
  //读取身份证
  const showTest = ref(false);
  //高级检索弹框
  const showHighSearch = ref(false);
  //标记未入组弹框
  const showNotJoin = ref(false);
  //标记未入组弹框
  const showJoin = ref(false);
  //标记出组弹框or批量编辑弹框
  const showOutOrEdit = ref(false);
  //编辑弹框
  const showEditUserInfo = ref(false);
  //备忘录弹框
  const showMemo = ref(false);
  //试验记录弹框
  const showTestRecord = ref(false);
  //标记不适宜弹框
  const showInaptitude = ref(false);
  //打印卡牌弹框
  const showPrintCrad = ref(false);
  const trialsType = ref('1');
  const projectStatus = ref('进行中');
  const projectKeywords = ref('');
  const params = ref({});
  const selectionsList = ref([]);
  const selectList = ref([]);
  //人员核对历史记录弹窗
  const showPeopleHistoryInfo = ref(false);
  const HistoryInfo = ref({});
  const peopleHistoryChange = () => {
    showPeopleHistoryInfo.value = true;
    HistoryInfo.value = {
      trialsGuid: trialsDataInfo.value.guid,
      organizationGuid: loginUser.value.organizationGuid
    };
  };
  const markNotJoin = async () => {
    /* 使用markSubjectTrialList接口or列表勾选的数据*/
    if (selectionsList.value.length != 0) {
      if (!checkStatusInArray(selectionsList.value, [1, 7], 'status')) {
        EleMessage.warning(
          '标记未入组，仅能选择【筛选（初查合格）、待入组】状态的数据！'
        );
        return;
      }
      selectList.value = selectionsList.value;
      showNotJoin.value = true;
      return;
    }

    selectList.value = await markSubjectTrialList({
      type: '1',
      trialsGuid: trialsDataInfo.value.guid
    });
    showNotJoin.value = true;
  };
  const markJoin = async () => {
    /* 使用markSubjectTrialList接口or列表勾选的数据*/
    if (selectionsList.value.length != 0) {
      if (!checkStatusInArray(selectionsList.value, [7], 'status')) {
        EleMessage.warning('标记入组，仅能选择【待入组】状态的数据！');
        return;
      }
      selectList.value = selectionsList.value;
      // ElNotification.closeAll();
      showJoin.value = true;
      return;
    }

    selectList.value = await markSubjectTrialList({
      type: '2',
      trialsGuid: trialsDataInfo.value.guid
    });
    // ElNotification.closeAll();

    showJoin.value = true;
  };
  const markOutOrEditType = ref('');
  const markOutOrEdit = async (type) => {
    /* 使用markSubjectTrialList接口获取数据*/
    if (selectionsList.value.length != 0) {
      if (type == 'edit') {
        if (!checkStatusInArray(selectionsList.value, [2], 'status')) {
          EleMessage.warning('批量编辑，仅能选择【入组】状态的数据！');
          return;
        }
        selectList.value = selectionsList.value;
        markOutOrEditType.value = type;
        showOutOrEdit.value = true;
        return;
      }
      if (type == 'out') {
        if (!checkStatusInArray(selectionsList.value, [2], 'status')) {
          EleMessage.warning('标记出组，仅能选择【入组】状态的数据！');
          return;
        }
        selectList.value = selectionsList.value;
        markOutOrEditType.value = type;
        showOutOrEdit.value = true;
        return;
      }
      return;
    }

    selectList.value = await markSubjectTrialList({
      type: type == 'edit' ? '4' : '3',
      trialsGuid: trialsDataInfo.value.guid
    });

    markOutOrEditType.value = type;
    showOutOrEdit.value = true;
  };
  const printCrad = () => {
    if (selectionsList.value.length == 0) {
      EleMessage.warning('请至少选择一项数据打印卡牌');
      return;
    }
    showPrintCrad.value = true;
  };
  const selectRow = ref({});
  const editUserInfo = (row) => {
    selectRow.value = row;
    showEditUserInfo.value = true;
  };
  const testInfo = (row) => {
    selectRow.value = row;
    showTestInfo.value = true;
  };
  const handleCommand = (command, row) => {
    selectRow.value = row;
    if (command == 'a') {
      showMemo.value = true;
      return;
    }
    if (command == 'b') {
      showTestRecord.value = true;
      return;
    }
    if (command == 'c') {
      ElMessageBox.confirm(
        row.sysremarks.replace(/\r\n/g, '<br/>'),
        '初查不合格，确定要进行放行操作吗？ ',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '是',
          cancelButtonText: '否',
          center: true,
          customStyle: {
            maxWidth: '600px'
          }
        }
      ).then(() => {
        release(row).then((res) => {
          if (res.code == 200) {
            EleMessage.success('操作成功');
            reload();
            return;
          }
          EleMessage.error(res.msg);
        });
      });
      return;
    }
    if (command == 'd') {
      showInaptitude.value = true;
      return;
    }

    if (command == 'e') {
      ElMessageBox.confirm(
        '（系统自动将清空“末次给药时间”“末次访视时间”“建议下次试验间隔期”为0天）',
        '是否确定标记入组未给药？',
        {
          confirmButtonText: '是',
          cancelButtonText: '否',
          center: true
        }
      ).then(() => {
        markFalloff({ guid: row.guid }).then((res) => {
          if (res.code == 200) {
            EleMessage.success('操作成功');
            reload();
            return;
          }
          EleMessage.error(res.msg);
        });
      });
      return;
    }
  };
  defineProps({
    // 表头背景
    tableHeader: String,
    // 表格高度
    tableFullHeight: Boolean
  });
  const tableRef = ref(null);
  const highSearchLoad = (value) => {
    params.value = value;
    reload();
  };
  /** 刷新表格 */
  const reload = () => {
    if (!trialsDataInfo.value.guid) {
      EleMessage.warning('请先选择试验项目！');
      return;
    }
    if (params.value.idcard) {
      params.value.idcard = params.value.idcard.replace(/\s+/g, '');
      if (!validIdCard(params.value.idCard)) {
        EleMessage.error('请输入完整的身份证号');
        return;
      }
    }
    tableRef.value?.reload?.({ where: params.value });
  };
  const loading = ref(false);
  const sendSubjecttrialExport = () => {
    loading.value = true;
    subjecttrialExport({
      ...params.value,
      pageNumber: tableRef.value.paginationProps.currentPage,
      pageSize: tableRef.value.paginationProps.pageSize,
      trialsGuid: trialsDataInfo.value.guid,
      exportType: exportType.value,
      desensitizationType: desensitizationType.value
    }).then(() => {
      loading.value = false;
      dialogVisible.value = false;
    });
  };
  const trialsDataList = ref([]);
  const trialsDataInfo = ref({});

  const page = ref({
    pageNumber: 1,
    pageSize: 50
  });
  const projcetGroupChange = debounce(() => {
    page.value.pageNumber = 1;
    trialsDataInfo.value = {};
    getProjectList('reset');
  }, 500);
  const inputChange = debounce(() => {
    page.value.pageNumber = 1;
    getProjectList('reset');
  }, 500);
  const getProjectList = (type) => {
    const data = {
      keywords: projectKeywords.value,
      pageNumber: page.value.pageNumber,
      pageSize: page.value.pageSize,
      status: projectStatus.value == '进行中' ? 1 : 2
    };
    page.value.pageNumber++;
    trialsList(data).then((res) => {
      if (res.code == 200 && res.data.list) {
        if (type == 'reset') {
          trialsDataList.value = JSON.parse(JSON.stringify(res.data.list));
        } else {
          trialsDataList.value = [...trialsDataList.value, ...res.data.list];
        }

        /*  if (!trialsDataInfo.value.guid && res.data.list.length > 0) {
                trialsDataInfo.value = JSON.parse(JSON.stringify(res.data.list[0]));
                reload();
              }*/
      }
    });
  };
  const projectChange = (item) => {
    console.log('projectChange====>', item);
    params.value = {};
    trialsDataInfo.value = item;
    nextTick(() => {
      reload();
    });
  };
  nextTick(() => {
    getProjectList('reset');
  });
  watch(
    () => route.query,
    () => {
      console.log('值变化了-----watch');
      initData();
    },
    { deep: true }
  );
  const initData = () => {
    console.log(
      'initData-----',
      route.query.projectId,
      route.query.projectName
    );
    if (route.query.projectName) {
      params.value = {};
      trialsDataInfo.value.projectName = route.query.projectName;
      trialsDataInfo.value.code = route.query.code;
      trialsDataInfo.value.status = route.query.status;
      trialsDataInfo.value.guid = route.query.projectId;
      trialsDataInfo.value.projectId = route.query.projectId;
      params.value = {};
      nextTick(() => {
        reload();
      });
    }
  };
  nextTick(() => {
    console.log('值变化了-----nextTick');
    initData();
  });
  const datasource = ({ page, limit, where, orders, filters }) => {
    return subjecttrialList({
      ...filters,
      ...where,
      orderBy: orders.sort,
      orderItem: orders.order,
      pageNumber: page,
      pageSize: limit,
      trialsGuid: trialsDataInfo.value.guid
    });
  };
  const getShowEye = (row) => {
    row.showEye = !row.showEye;
    getCompleteInfo({ guid: row.subjectsGuid, type: '1' }).then((res) => {
      row.idcard = res.msg;
      row.idcard = getCard(row);
    });
  };
  const getCard = (row) => {
    try {
      if (row.showEye) {
        return row.idcard;
      }
      return row.idcard.replace(/(\d{6})\d+(\d{4})/, '$1********$2');
    } catch (error) {
      console.log(error);
      return row.idcard;
    }
  };
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 48,
      align: 'left',
      fixed: 'left'
    },
    {
      type: 'index',
      columnKey: 'index',
      label: '序号',
      width: 65,
      align: 'center',
      showOverflowTooltip: true,
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '姓名',
      width: 120,
      fixed: 'left',
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'status',
      label: '状态',
      width: 170,
      fixed: 'left',
      showOverflowTooltip: true,
      slot: 'status',
      sortable: 'custom'
    },
    {
      prop: 'sex',
      label: '性别',
      minWidth: 90,
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'idcard',
      label: '身份证号',
      width: 240,
      slot: 'idcard',
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'age',
      label: '年龄',
      minWidth: 90,
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'similaritytype',
      slot: 'similaritytype',
      label: '(筛选/入组前)证照核对相似度',
      minWidth: 160,
      showOverflowTooltip: true
    },
    {
      prop: 'filterno',
      label: '筛选号',
      width: 150,
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'filterdate',
      label: '筛选时间',
      width: 150,
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'joinfilterdate',
      label: '入组前筛选时间',
      width: 150,
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'joinno',
      label: '入组号',
      width: 150,
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'joindate',
      label: '入组时间',
      width: 150,
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'lastdosedate',
      label: '末次给药时间',
      width: 160,
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'trialedate',
      label: '末次访视时间',
      width: 160,
      showOverflowTooltip: true,
      sortable: 'custom'
    },
    {
      prop: 'evaluate',
      label: '依从性评价',
      minWidth: 110,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'interval',
      label: '下次试验间隔天数',
      minWidth: 160,
      showOverflowTooltip: true
    },
    {
      prop: 'sysremarks',
      label: '筛查备注',
      minWidth: 160,
      showOverflowTooltip: true
    },
    {
      prop: 'remarks',
      label: '备注',
      slot: 'createName14',
      minWidth: 160,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      columnKey: 'action',
      label: '操作',
      hideInSetting: true,
      width: 120,
      slot: 'action',
      hideInPrint: true,
      hideInExport: true,
      fixed: 'right'
    }
  ]);
  // 检查所选项是否包含检验id
  const checkStatusInArray = (Array, checkArray, type) => {
    for (const obj of Array) {
      if (!checkArray.includes(obj[type])) {
        return false;
      }
    }
    return true;
  };
</script>
<script>
  export default {
    name: 'StudyManage'
  };
</script>
<style lang="scss" scoped>
  .project {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    padding: 6px 10px;
    color: #517bff;
    background: #f9faff;
    margin-bottom: 6px;
  }
  .selectIndex {
    background: #517bff;
    color: #ffffff;
  }
  .studyManageprojectName {
    /*width: 70%;*/
    height: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: bold;
    font-size: 18px;
    margin-top: 8px;
  }
  .iconHover:hover {
    color: #1677ff;
  }
  .result-icon {
    background: #1a9e34;
    width: 22px;
    height: 22px;
    border-radius: 50%;
  }
  .result-icon2 {
    background: #ff0000;
    width: 22px;
    height: 22px;
    border-radius: 50%;
  }
</style>
<style>
  .left .el-radio-button__inner {
    padding: 8px 23px !important;
  }
  .tableForm {
    margin-top: 8px;
  }

  .el-button:focus-visible {
    outline: none !important;
  }
  .left .el-tabs__nav-wrap::after {
    background-color: transparent;
  }

  .ele-split-panel > .ele-split-panel-wrap > .ele-split-panel-space {
    width: 8px;
  }
  .ele-split-panel > .ele-split-panel-wrap {
    /* width: 208px; */
  }
</style>
