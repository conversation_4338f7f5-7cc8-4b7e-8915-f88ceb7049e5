<template>
  <ele-page :flex-table="true" hide-footer style="min-width: 1300px">
    <div class="top-tab">
      <el-tabs v-model="currentStatus">
        <el-tab-pane label="筛查" name="shaicha" />
        <el-tab-pane label="招募" name="zhaomu" />
        <el-tab-pane v-if="isSite" label="机构招募统计" name="sitezhaomu" />
      </el-tabs>
    </div>
    <div v-if="currentStatus == 'sitezhaomu'" class="site-page">
      <ele-card>
        <span class="tableTitle mb-2"> </span>
        <el-row :gutter="10">
          <el-col :span="5">
            <div
              class="statifather cursorPointer"
              @click="toRecruitProjectList()"
            >
              <img :src="projectNum" alt="" />
              <div class="stati">
                <div class="statiNum">{{
                  siteProjectData.projectTotal
                    ? siteProjectData.projectTotal
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : 0
                }}</div>
                <span class="txt">招募项目数</span>
              </div>
            </div>
          </el-col>
          <el-col :span="5">
            <div
              class="statifather cursorPointer"
              @click="toRecruitProjectList(1)"
            >
              <img :src="followNum" alt="" />
              <div class="stati">
                <div class="statiNum">{{
                  siteProjectData.recruitNumber
                    ? siteProjectData.recruitNumber
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : 0
                }}</div>
                <span class="txt">招募进行中</span>
              </div>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="statifather cursorPointer" @click="toProjectList(2)">
              <img :src="filterSuccse" alt="" />
              <div class="stati">
                <div class="statiNum">{{
                  siteProjectData.thisYearAddProjectNumber
                    ? siteProjectData.thisYearAddProjectNumber
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : 0
                }}</div>
                <span class="txt">今年新增项目数</span>
              </div>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="statifather cursorPointer">
              <img :src="shaicha" alt="" />
              <div class="stati">
                <div class="statiNum">{{
                  siteProjectData.noticeNumber
                    ? siteProjectData.noticeNumber
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : 0
                }}</div>
                <span class="txt">累积通知人数</span>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="statifather cursorPointer">
              <img :src="ruzu" alt="" />
              <div class="stati">
                <div class="statiNum">{{
                  siteProjectData.applyNumber
                    ? siteProjectData.applyNumber
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : 0
                }}</div>
                <span class="txt">累积报名数</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </ele-card>
      <el-row :gutter="10">
        <el-col :span="18">
          <departmentStatistics />
        </el-col>
        <el-col :span="6">
          <newConsultation />
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="14">
          <departmentRecruitStatistics />
        </el-col>
        <el-col :span="10">
          <departRanking />
        </el-col>
      </el-row>
      <div>
        <volunteerStatistics />
      </div>
    </div>
    <div v-if="currentStatus == 'zhaomu'" class="index-page">
      <ele-card :body-style="{ padding: '0px' }">
        <template #header>
          <div class="d-flex">
            <div>我关注的招募中项目（{{ data.length || 0 }}）</div>
            <div style="display: flex; align-items: center">
              <el-image :src="recruitment" alt="" class="m-image" />
              <span class="s-title">招募中：</span>
              <span class="s-content"
                >{{ projectRcruitData?.recruit || 0 }}
              </span>
              <el-image :src="closed" alt="" class="m-image" />
              <span class="s-title">已关闭：</span
              ><span class="s-content">{{
                projectRcruitData?.closed || 0
              }}</span>
              <el-image :src="draft" alt="" class="m-image" />
              <span class="s-title">草稿：</span
              ><span class="s-content">{{
                projectRcruitData?.draft || 0
              }}</span>
              <el-image :src="takenDown" alt="" class="m-image" />
              <span class="s-title">平台下架：</span
              ><span class="s-content">{{
                projectRcruitData?.platformclose || 0
              }}</span>
              <span class="all-title" @click="toShowAllProject">全部项目></span>
            </div>
          </div>
        </template>
        <div v-for="item in data" :key="item.projectId" class="card">
          <div class="list-item">
            <div class="list-item-avatar">
              <div class="list-item-avatar-extra">
                <el-tooltip
                  v-if="item.projectRecruitName.length > 50"
                  class="item-tooltip"
                  effect="dark"
                  :content="item.projectRecruitName"
                  placement="top"
                >
                  <div
                    style="
                      margin-bottom: 2px;
                      margin-left: -8px;
                      font-size: 18px;
                    "
                  >
                    <span :style="getTextColor(item?.projectRecruitType)">{{
                      item?.projectRecruitType == 1 ? '【健康】' : '【患者】'
                    }}</span>
                    {{ item?.projectRecruitName.substring(0, 50) }}...
                  </div>
                </el-tooltip>
                <div
                  v-else
                  style="margin-bottom: 2px; margin-left: -8px; font-size: 18px"
                >
                  <span :style="getTextColor(item?.projectRecruitType)">{{
                    item?.projectRecruitType == 1 ? '【健康】' : '【患者】'
                  }}</span>
                  {{ item?.projectRecruitName }}
                </div>
                <el-row>
                  <el-col class="s-num" :span="4">
                    {{
                      item.notifyNumber
                        ? item.notifyNumber
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : 0
                    }}</el-col
                  >
                  <el-col class="s-num" :span="4">
                    {{
                      item.browseNumber
                        ? item.browseNumber
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : 0
                    }}</el-col
                  >
                  <el-col class="s-num" :span="4">
                    {{
                      item.shareNumber
                        ? item.shareNumber
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : 0
                    }}</el-col
                  >
                  <el-col class="s-num" :span="4">
                    {{
                      item.consultNumber
                        ? item.consultNumber
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : 0
                    }}</el-col
                  >
                  <el-col class="s-num" :span="4">
                    {{
                      item.applyNumber
                        ? item.applyNumber
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : 0
                    }}</el-col
                  >
                </el-row>
                <el-row>
                  <el-col class="t-title" :span="4">通知</el-col>
                  <el-col class="t-title" :span="4">浏览</el-col>
                  <el-col class="t-title" :span="4">分享</el-col>
                  <el-col class="t-title" :span="4">咨询</el-col>
                  <el-col class="t-title" :span="4">报名</el-col>
                </el-row>
              </div>
            </div>

            <div
              style="
                border: 0.5px solid #e6eff5;
                height: 68px;
                margin-left: 0px;
              "
            ></div>

            <div style="margin-left: 60px" class="list-item-owner">
              <div class="list-item-title"
                >待审核: {{ item?.pendingReviewNumber || 0 }}</div
              >
            </div>
            <div style="margin-left: 30px" class="list-item-owner">
              <div class="list-item-title"
                >入组：{{ item?.joinGroupNumber || 0 }}</div
              >
            </div>
            <div style="margin-left: 30px" class="list-item-owner">
              <div class="list-item-title"
                >出组：{{ item?.outGroupNumber || 0 }}</div
              >
            </div>
            <div style="margin-left: 30px" class="list-item-owner">
              <div class="list-item-title"
                >失败：{{ item?.failNumber || 0 }}</div
              >
            </div>
          </div>
        </div>
      </ele-card>
      <el-row :gutter="10">
        <el-col :span="18">
          <projectRecommendationRecord />
        </el-col>
        <el-col :span="6">
          <newConsultation />
        </el-col>
      </el-row>
    </div>
    <div v-if="currentStatus == 'shaicha'" class="index">
      <ele-card>
        <div class="d-flex">
          <div
            style="
              display: flex;
              justify-content: flex-end;
              align-items: center;
            "
          >
            <el-radio-group v-model="form.caseTimeRangeType" @change="change">
              <el-radio :value="1">全部</el-radio>
              <el-radio :value="2">本月</el-radio>
              <el-radio :value="3">本季度</el-radio>
              <el-radio :value="4">本年</el-radio>
              <el-radio :value="5">
                <div style="display: flex; align-items: center">
                  <span style="margin-right: 10px">自定义</span>
                  <el-date-picker
                    v-if="form.caseTimeRangeType === 5"
                    v-model="form.caseTimedateRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="YYYY-MM-DD"
                    style="width: 260px"
                    @change="change"
                  />
                </div>
              </el-radio>
            </el-radio-group>
          </div>
        </div>
        <span class="tableTitle mb-2"> </span>
        <el-row :gutter="10">
          <el-col :span="5">
            <div class="statifather cursorPointer" @click="toProjectList(null)">
              <img :src="projectNum" alt="" />
              <div class="stati">
                <div class="statiNum">{{
                  overviewInfo.projectCount
                    ? overviewInfo.projectCount
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : 0
                }}</div>
                <span class="txt">项目数</span>
              </div>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="statifather cursorPointer" @click="toProjectList(1)">
              <img :src="followNum" alt="" />
              <div class="stati">
                <div class="statiNum">{{
                  overviewInfo.joinProjectCount
                    ? overviewInfo.joinProjectCount
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : 0
                }}</div>
                <span class="txt">进行中项目</span>
              </div>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="statifather cursorPointer" @click="toProjectList(2)">
              <img :src="filterSuccse" alt="" />
              <div class="stati">
                <div class="statiNum">{{
                  overviewInfo.completedProjectCount
                    ? overviewInfo.completedProjectCount
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : 0
                }}</div>
                <span class="txt">已完成项目</span>
              </div>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="statifather cursorPointer" @click="onClick(1)">
              <img :src="shaicha" alt="" />
              <div class="stati">
                <div class="statiNum">{{
                  overviewInfo.filterCount
                    ? overviewInfo.filterCount
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : 0
                }}</div>
                <span class="txt">筛查人次</span>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="statifather cursorPointer" @click="onClick(2)">
              <img :src="ruzu" alt="" />
              <div class="stati">
                <div class="statiNum">{{
                  overviewInfo.joinCount
                    ? overviewInfo.joinCount
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                    : 0
                }}</div>
                <span class="txt">入组人次</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </ele-card>
      <el-row :gutter="10">
        <el-col :span="12">
          <ele-card style="height: calc(100vh - 272px - 52px)">
            <projectData />
          </ele-card>
        </el-col>
        <el-col :span="12">
          <ele-card style="height: calc(100vh - 272px - 52px)">
            <screenSituate />
          </ele-card>
        </el-col>
      </el-row>
    </div>
    <el-dialog
      v-model="listModalVisible"
      :title="
        listModalStatus == 1
          ? '筛查人次详情'
          : listModalStatus == 2
            ? '入组人次详情'
            : ''
      "
      width="1200"
      center
      class="custom-center-dialog"
    >
      <el-table :data="tableData" style="width: 100%" height="600">
        <el-table-column label="序号" fixed="left" width="65">
          <template #default="{ $index }">
            <!-- 计算当前页的起始序号，实现分页连续编号 -->
            {{
              (detailParams.pageNumber - 1) * detailParams.pageSize + $index + 1
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="trialsName"
          label="项目名称"
          show-overflow-tooltip="true"
        />
        <el-table-column prop="subjectsName" label="姓名" width="100" />
        <el-table-column prop="sex" label="性别" width="80" />
        <el-table-column
          prop="idCardNo"
          label="身份证号"
          width="240"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <span
                class="ellipsis"
                style="width: 220px; display: inline-block"
                >{{
                  $PhoneOrIdCrad(
                    row.showIdcardEye ? row.idCard : row.idCardNo,
                    'idCard'
                  )
                }}</span
              >
              <img
                :src="row.showIdcardEye ? eyeOpen : eyeClose"
                style="
                  transform: scale(1.28);
                  cursor: pointer;
                  margin: 0 5px;
                  width: 14px;
                  height: 14px;
                "
                @click="getShowEye(row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄" width="80" />
        <el-table-column
          v-if="listModalStatus == 1"
          prop="filterNo"
          label="筛选号"
          width="100"
        />
        <el-table-column
          v-if="listModalStatus == 1"
          prop="filterdate"
          label="筛选时间"
          width="140"
        />
        <el-table-column
          v-if="listModalStatus == 2"
          prop="joinFilterDate"
          label="入组前筛选时间"
          width="150"
        />
        <el-table-column
          v-if="listModalStatus == 2"
          prop="joinNo"
          label="入组号"
          width="100"
        />
        <el-table-column
          v-if="listModalStatus == 2"
          prop="joinDate"
          label="入组时间"
          width="140"
        />
      </el-table>
      <el-pagination
        class="flexCenter"
        v-model:current-page="detailParams.pageNumber"
        v-model:page-size="detailParams.pageSize"
        :page-sizes="[10, 20, 30, 40]"
        layout="total, sizes, prev, pager, next, jumper"
        :pager-count="4"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-dialog>
    <!-- 查看所有项目 -->
    <allProjectModal
      v-if="showAllProjectDialog"
      v-model="showAllProjectDialog"
    />
  </ele-page>
</template>

<script setup>
  import { computed, ref, nextTick, onMounted } from 'vue';
  import dayjs from 'dayjs';
  import { useUserStore } from '@/store/modules/user';
  import projectNum from '@/assets/index/projectNum.png';
  import followNum from '@/assets/index/projectDoing.png';
  import filterSuccse from '@/assets/index/projectFinish.png';
  import shaicha from '@/assets/index/shaicha.png';
  import ruzu from '@/assets/index/ruzu.png';
  import recruitment from '@/assets/index/recruitment.svg';
  import takenDown from '@/assets/index/takenDown.svg';
  import draft from '@/assets/index/draft.svg';
  import closed from '@/assets/index/closed.svg';

  import allProjectModal from './components/allProjectModal.vue';
  import projectData from './components/projectData.vue';
  import screenSituate from './components/screenSituate.vue';
  import newConsultation from './components/newConsultation.vue';
  import projectRecommendationRecord from './components/projectRecommendationRecord.vue';
  import departmentStatistics from './components/departmentStatistics.vue';
  import departmentRecruitStatistics from './components/departmentRecruitStatistics.vue';
  import departRanking from './components/departRanking.vue';
  import volunteerStatistics from './components/volunteerStatistics.vue';

  import {
    siteProjectStatistics,
    getFollowProjectStatistics,
    projectRcruitStatusStatistics
  } from '@/api/project/index';
  import {
    homeStatistic,
    filterSubjectsList,
    joinSubjectsList
  } from '@/api/subjects/home/<USER>';
  import { ElMessage } from 'element-plus';
  import eyeOpen from '@/assets/eyeOpen.png';
  import eyeClose from '@/assets/eyeClose.png';
  import { useRouter } from 'vue-router';
  import 'dayjs/locale/zh-cn';
  dayjs.locale('zh-cn');

  const { isSite } = useUserStore();

  const currentStatus = ref('shaicha');

  const showAllProjectDialog = ref(false);

  const userStore = useUserStore();
  /** 当前用户信息 */
  const loginUser = computed(() => userStore.info ?? {});

  const data = computed(() => {
    if (followProjectData.value && followProjectData.value.length > 0) {
      return followProjectData.value.slice(0, 3); // 取前三项
    }
    return []; // 如果没有值，返回空数组
  });

  const zmForm = ref({
    caseTimeRangeType: 4,
    caseTimedateRange: []
  });

  const form = ref({
    caseTimeRangeType: 4,
    caseTimedateRange: []
  });
  const change = (value) => {
    console.log('change===>', value);
    init();
  };
  const total = ref(0);
  const tableData = ref([]);
  const listModalVisible = ref(false);
  const listModalStatus = ref(null);
  const router = useRouter();
  const dataParams = ref({
    startDate: '',
    endDate: ''
  });
  const detailParams = ref({
    startDate: dataParams.value.startDate,
    endDate: dataParams.value.endDate,
    pageNumber: 1,
    pageSize: 10,
    organizationGuid: '',
    orderBy: '',
    orderItem: 'asc'
  });

  const toShowAllProject = () => {
    showAllProjectDialog.value = true;
  };

  const handleSizeChange = (size) => {
    detailParams.value.pageSize = size;
    getList();
  };
  const handleCurrentChange = (current) => {
    detailParams.value.pageNumber = current;
    getList();
  };

  const toRecruitProjectList = (status) => {
    let path = '/recruitProject';
    router.push({
      path,
      query: {
        status: status,
        currentStatus: 'zhaomu'
      }
    });
  };

  const toProjectList = (status) => {
    let path = '/recruitProject';
    if (loginUser.value.tenantId == '60AE2015C0A803333698AE263DE6088C') {
      path = '/screenProject';
    }
    router.push({
      path,
      query: {
        startDate: dataParams.value.startDate,
        endDate: dataParams.value.endDate,
        status: status,
        currentStatus: 'shaicha'
      }
    });
  };
  const overviewInfo = ref({});
  const props = defineProps({
    date: {
      type: Object,
      default: () => ({})
    }
  });
  const onClick = (type) => {
    console.log('type===>', type);
    detailParams.value.pageNumber = 1;
    listModalStatus.value = type;
    listModalVisible.value = true;
    getList();
  };
  const getList = () => {
    tableData.value = [];
    detailParams.value.startDate = dataParams.value.startDate;
    detailParams.value.endDate = dataParams.value.endDate;
    if (listModalStatus.value == 1) {
      filterSubjectsList(detailParams.value).then((res) => {
        console.log('res===>', res);
        tableData.value = res.list;
        total.value = res.totalCount;
      });
    }
    if (listModalStatus.value == 2) {
      joinSubjectsList(detailParams.value).then((res) => {
        tableData.value = res.list;
        total.value = res.totalCount;
      });
    }
  };
  const init = () => {
    const currentDate = dayjs();
    // 全部时间
    if (form.value.caseTimeRangeType === 1) {
      dataParams.value.startDate = '';
      dataParams.value.endDate = '';
    }
    // 本月
    if (form.value.caseTimeRangeType === 2) {
      dataParams.value.startDate = currentDate
        .startOf('month')
        .format('YYYY-MM-DD');
      dataParams.value.endDate = currentDate
        .endOf('month')
        .format('YYYY-MM-DD');
    }
    // 本季度
    if (form.value.caseTimeRangeType === 3) {
      const now = dayjs();
      const month = now.month();
      let startMonth;
      if (month < 3) {
        startMonth = 0; // 1月
      } else if (month < 6) {
        startMonth = 3; // 4月
      } else if (month < 9) {
        startMonth = 6; // 7月
      } else {
        startMonth = 9; // 10月
      }
      let firstDayOfQuarter = dayjs(
        now.year().toString() +
          '-' +
          (startMonth + 1).toString().padStart(2, '0') +
          '-01'
      ).format('YYYY-MM-DD');
      let endMonth;
      if (month < 3) {
        endMonth = 2; // 3月
      } else if (month < 6) {
        endMonth = 5; // 6月
      } else if (month < 9) {
        endMonth = 8; // 9月
      } else {
        endMonth = 11; // 12月
      }
      const year = now.year();
      const nextYear = month >= 9 ? year + 1 : year; // 如果当前月份大于或等于9月，则年份加1
      // 根据月份判断日期
      const endMonthStr = (endMonth + 1).toString().padStart(2, '0');
      const day = endMonthStr === '06' || endMonthStr === '09' ? '30' : '31';
      let lastDayOfQuarter = dayjs(
        nextYear.toString() + '-' + endMonthStr + '-' + day
      ).format('YYYY-MM-DD');
      dataParams.value.startDate = firstDayOfQuarter;
      dataParams.value.endDate = lastDayOfQuarter;
    }
    // 本年
    if (form.value.caseTimeRangeType === 4) {
      dataParams.value.startDate = currentDate
        .startOf('year')
        .format('YYYY-MM-DD');
      dataParams.value.endDate = currentDate.endOf('year').format('YYYY-MM-DD');
    }
    // 自定义
    if (form.value.caseTimeRangeType === 5) {
      if (form.value.caseTimedateRange && form.value.caseTimedateRange[0]) {
        dataParams.value.startDate = form.value.caseTimedateRange[0];
        dataParams.value.endDate = form.value.caseTimedateRange[1];
      } else {
        dataParams.value.startDate = '';
        dataParams.value.endDate = '';
      }
    }
    console.log('data===>', dataParams.value);
    initData();
  };

  defineExpose({
    init
  });
  const initData = async () => {
    await homeStatistic({
      startDate: dataParams.value.startDate || '',
      endDate: dataParams.value.endDate || ''
    }).then((res) => {
      console.log('res.data===》', res);
      overviewInfo.value = res;
    });
  };
  nextTick(() => {
    const currentDate = dayjs();
    if (form.value.caseTimeRangeType === 4) {
      dataParams.value.startDate = currentDate
        .startOf('year')
        .format('YYYY-MM-DD');
      dataParams.value.endDate = currentDate.endOf('year').format('YYYY-MM-DD');
    }
    initData();
  });
  const getShowEye = (row) => {
    row.showIdcardEye = !row.showIdcardEye;
  };

  // ------------------------------------------这是一条分界线

  const getTextColor = (type) => {
    if (type == 1) {
      return { color: '#209373' }; // 健康类型为绿色
    } else {
      return { color: '#F97316' }; // 患者类型为蓝色
    }
  };

  const followProjectData = ref([]);

  const getFollowProjectData = async () => {
    try {
      const params = {
        followFlag: 1
      };
      const res = await getFollowProjectStatistics(params);
      followProjectData.value = res?.records;
    } catch (e) {
      ElMessage.error(e);
    }
  };

  const projectRcruitData = ref({});
  const getProjectRcruitStatusStatistics = async () => {
    try {
      const params = {
        followFlag: 1
      };
      const res = await projectRcruitStatusStatistics(params);
      projectRcruitData.value = res?.data;
    } catch (e) {
      ElMessage.error(e);
    }
  };

  const siteProjectData = ref({});
  const getSiteProjectStatistics = async () => {
    try {
      const res = await siteProjectStatistics();
      siteProjectData.value = res?.data;
    } catch (e) {
      ElMessage.error(e);
    }
  };

  onMounted(() => {
    getFollowProjectData();
    getProjectRcruitStatusStatistics();
    if (isSite) {
      getSiteProjectStatistics();
    }
  });
</script>

<style lang="scss" scoped>
  .s-num {
    font-weight: 700;
    color: #3b426f;
    line-height: 29px;
    font-size: 22px;
  }
  .ellipsis {
    display: -webkit-box; /* 使用 flexbox 布局 */
    -webkit-box-orient: vertical; /* 垂直排列子元素 */
    -webkit-line-clamp: 1; /* 显示的行数 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
  .main-card {
    flex: 1;
    width: 100%;
    height: 0px;
    padding-top: 12px;
    box-sizing: border-box;
    overflow: auto;
  }
  .card {
    background: #ffffff;
    border-bottom: 1px solid #ecf2f9;
    // margin-bottom: 10px;
    transition: border 0.3s;
    // cursor: pointer;
    // &:hover {
    //   border-color: #0075ff;
    // }
  }
  /* 列表样式 */
  .list-item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px;
    .list-item-avatar {
      flex: 1;
      display: flex;
      align-items: center;
      :deep(.el-avatar) {
        flex-shrink: 0;
      }
      .list-item-avatar-extra {
        flex: 1;
        padding-left: 12px;
        box-sizing: border-box;
      }
    }
    & > div + div {
      margin-left: 20px;
      flex-shrink: 0;
    }
    .list-item-owner {
      width: 120px;
      color: #5b5b64;
      font-size: 16px;
    }
    .list-item-time {
      width: 160px;
    }
    .list-item-progress {
      width: 180px;
    }
    .list-item-title {
      color: #4b5563;
      font-size: 18px;
      margin-bottom: 4px;
    }
    .list-item-tools {
      display: flex;
      align-items: center;
    }
  }
  /* 响应式 */
  @media screen and (max-width: 1340px) {
    .list-item {
      & > div + div {
        margin-left: 10px;
      }
      .list-item-owner {
        width: 110px;
        color: #5b5b64;
        font-size: 16px;
      }
      .list-item-time {
        width: 140px;
      }
      .list-item-progress {
        width: 100px;
      }
    }
  }
  @media screen and (max-width: 1100px) {
    .list-item {
      display: block;
      .list-item-owner,
      .list-item-time,
      .list-item-progress {
        width: 100%;
        margin: 8px 0 0 0;
        display: flex;
        align-items: center;
      }
      .list-item-title {
        margin: 0;
        width: 80px;
        color: #4b5563;
        font-size: 18px;
      }
      .list-item-tools {
        margin-top: 8px;
        justify-content: flex-end;
      }
    }
  }
  .s-content {
    font-weight: 700;
    font-size: 18px;
    color: #3b426f;
    line-height: 25px;
    margin-right: 60px;
  }
  .m-image {
    margin-right: 6px;
    width: 22px;
    height: 16px;
  }

  .s-title {
    font-weight: 400;
    font-size: 18px;
    color: #718ebf;
    line-height: 25px;
  }

  .all-title {
    height: 22px;
    font-size: 16px;
    color: #718ebf;
    line-height: 22px;
    cursor: pointer;
  }
  .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .top-tab {
    height: 42px;
    position: relative;
    background: #ffffff;
    margin-bottom: 10px;
    padding: 10px 20px 0 20px;
    border-radius: 6px;
  }

  :deep(.el-pager) {
    margin: 0 4px !important;
  }
  :deep(.el-pager li) {
    border: 1px solid #94aed9;
    box-sizing: border-box;
    border-radius: 4px !important;
    margin: 4px !important;
  }
  :deep(.el-table--enable-row-transition .el-table__body td.el-table__cell) {
    transition: background-color 0.25s ease;
    border-right: 1px solid #f3f4f8 !important;
  }
  :deep(.index) {
    .el-table {
      --el-table-border-color: #f3f4f8 !important;
      color: #6c7f92 !important;
      td {
        border-bottom-color: #f3f4f8 !important;
      }

      th {
        border-bottom-color: #f3f4f8 !important;
      }
      tr {
        border-top-color: #f3f4f8 !important;
      }
    }
  }
  .header-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .welcome {
    font-size: 16px;
    font-weight: bold;
    color: #374151;
  }
  .index {
    overflow: hidden;
    height: calc(100vh - 147px);
  }

  .index-page {
    overflow: hidden;
  }
  .site-page {
    overflow-x: hidden;
  }

  .flexCenter {
    // text-align: center;
    display: flex;
    justify-content: center;
  }
  .mt10 {
    margin-top: 20px;
  }
  .t-title {
    color: #718ebf;
    font-size: 14px;
  }
</style>
<style lang="scss">
  .statifather {
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 62px;
      height: 62px;
    }
  }
  .stati {
    height: 48px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    margin-left: 10px;

    font-family:
      Microsoft YaHei,
      Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #6c7f92;
    .statiNum {
      font-weight: 700;
      font-size: 28px;
      color: #374151;
      line-height: normal;
      margin-top: -10px;
    }
    .txt {
      font-size: 18px;
    }
  }
  .tableTitle {
    font-family:
      Microsoft YaHei,
      Microsoft YaHei;
    font-weight: 700;
    font-size: 17px;
    color: #374151;
  }
  .cursorPointer {
    cursor: pointer;
  }
  .mb-2 {
    margin-bottom: 20px;
    display: inline-block;
  }
  /* 添加自定义样式 */
  .custom-center-dialog {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 80px !important; /* 水平居中 */
    max-height: calc(100vh - 30px); /* 避免模态框高度超出视口 */
  }
</style>
