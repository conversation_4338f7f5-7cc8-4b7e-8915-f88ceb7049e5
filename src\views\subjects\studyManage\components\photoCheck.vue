<!-- 编辑弹窗 -->
<template>
  <div
    class="testmodal-content"
    style="width: 100%"
    :style="type == 2 ? 'padding:20px 0 6px 0' : ''"
  >
    <div class="testmodal-title" v-if="type == 1">证件核对</div>
    <div style="display: flex; width: 100%">
      <div style="flex: 1; margin-right: 20px">
        <takePhoto
          :checkLoading="checkLoading"
          ref="takePhotoRef"
          :flag="flag"
          :isSign="isSign"
          :type="type"
          @done="getImgUrl"
          @doneSubmit="submit"
        />
      </div>
      <div :class="type == 2 ? 'shua-lian' : ''">
        <div
          class="modal-content-box"
          style="
            width: 130px;
            height: 172px;
            display: flex;
            flex-direction: column;
            margin-bottom: 22px;
          "
        >
          <div
            style="
              flex: 1;
              display: flex;
              justify-content: center;
              align-items: center;
            "
          >
            <img
              :src="filterRes.base64Photo"
              style="width: 130px"
              v-if="filterRes.base64Photo"
            />
          </div>
          <div
            style="
              height: 38px;
              font-family:
                Microsoft YaHei,
                Microsoft YaHei;
              font-size: 16px;
              color: #718ebf;
              line-height: 38px;
              text-align: center;
            "
            >现场照片</div
          >
        </div>
        <div
          class="modal-content-box"
          style="
            width: 130px;
            height: 172px;
            display: flex;
            flex-direction: column;
          "
        >
          <div
            style="
              flex: 1;
              display: flex;
              justify-content: center;
              align-items: center;
            "
          >
            <img
              :src="
                PicBase64String.includes('/portrait/')
                  ? PicBase64String
                  : 'data:image/png;base64,' + PicBase64String
              "
              style="width: 130px; height: 170px"
              v-if="PicBase64String"
            />
          </div>
          <div
            style="
              height: 38px;
              font-family:
                Microsoft YaHei,
                Microsoft YaHei;
              font-size: 16px;
              color: #718ebf;
              line-height: 38px;
              text-align: center;
            "
            >身份证照片</div
          >
        </div>

        <!-- <div>
          <el-image
            class="modal-content-box"
            :src="'data:image/png;base64,' + PicBase64String"
            alt="用户照片"
            style="width: 130px; height: 170px"
          >
            <template #error>
              <div class="image-slot">
                <img
                  src="@/assets/filter/defaultIdcard.svg"
                  alt=""
                  srcset=""
                  style="width: 77px; height: 77px"
                />
              </div>
            </template>
          </el-image>
        </div> -->
        <!-- <div
          style="
            text-align: center;
            margin: 20px 0;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: 400;
            font-size: 16px;
            color: #4c5c82;
          "
          >核对结果</div
        >
        <div
          class="modal-content-box"
          style="
            padding: 0 20px;
            height: 54px;
            font-size: 25px;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
          "
          :style="`${
            filterRes.similaritytype == 1
              ? 'border-color:#75A84C;background:#FBFFF8;'
              : filterRes.similaritytype == 2
              ? 'border-color:#5280FB;background:#EDF5FF;'
              : filterRes.similaritytype == 3
              ? 'border-color:#FFAF4D;background:#FFF7E8;'
              : 'border-color:#94aed9'
          };`"
        >
          <span style="color: #75a84c" v-if="filterRes.similaritytype == 1"
            >高相似度</span
          >
          <span style="color: #5280fb" v-if="filterRes.similaritytype == 2"
            >中相似度</span
          >
          <span style="color: #ffaf4d" v-if="filterRes.similaritytype == 3"
            >低相似度</span
          >
        </div> -->
        <!-- <div style="display: flex; justify-content: center; margin-top: 21px">
          <el-button
            type="primary"
            @click="submit"
            :disabled="flag || !base64Photo"
            :loading="checkLoading"
          >
            {{ checkLoading ? '证件核对中' : '证件核对' }}</el-button
          >
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
  import { nextTick, ref, watch } from 'vue';
  import takePhoto from './takePhoto.vue';
  import { throttle } from 'lodash';
  const ossBaseUrl = import.meta.env.VITE_APP_BASE_OSS;

  const emit = defineEmits(['done', 'base64PhotoChange']);
  const takePhotoRef = ref(null);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    checkLoading: Boolean,
    type: {
      type: String,
      default: '-1'
    },
    flag: Boolean,
    isSign: Boolean,
    filterRes: Object,
    PicBase64String: String
  });
  //拍照的base64信息
  const base64Photo = ref('');
  //获取子组件的拍照信息并通知父组件
  const getImgUrl = (value) => {
    base64Photo.value = value;
    emit('base64PhotoChange', value);
  };

  //通知父组件证件核对
  const submit = throttle(() => {
    emit('done', base64Photo.value.replace('data:image/png;base64,', ''));
  }, 1500);
  const closeCamera = () => {
    takePhotoRef.value.stop();
  };
  const clearPhoto = () => {
    base64Photo.value = '';
    takePhotoRef.value.clearPhoto();
    emit('base64PhotoChange', '');
  };
  defineExpose({
    closeCamera,
    clearPhoto
  });
  nextTick(() => {
    console.log(' props.PicBase64String', props.PicBase64String);
  });
</script>
<style>
  .testmodal-content {
    position: relative;
  }
  .modal-content-box {
    background: #ffffff;
    border: 1px solid #94aed9;
    border-radius: 4px;
    box-sizing: border-box;
    overflow: hidden;
  }
  .testmodal-title {
    margin-top: 10px;
    margin-bottom: 25px;
    color: #517bff;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei;
    font-weight: 400;
    font-size: 18px;
    color: #3b426f;
    text-align: left;
  }
  .shua-lian {
    margin-right: 118px;
  }
</style>
