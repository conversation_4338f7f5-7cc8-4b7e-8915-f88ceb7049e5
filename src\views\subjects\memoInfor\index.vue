<template>
  <div class="index">
    <ele-card
      flex-table
      :body-style="{ padding: '4px 0 20px 0', overflow: 'hidden' }"
      style="height: 100%"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        v-model:selections="selections"
        :columns="columns"
        :datasource="datasource"
        :toolbar="{ theme: tableHeader }"
        highlight-current-row
        :bottom-line="tableFullHeight"
        cache-key="workAuditCheckTable1"
      >
        <!-- <smart-form
          v-model="searchExpand"
          ref="searchRef"
          :entityFileds="searchConfig"
          @search="reload"
        /> -->
        <!-- 表头左侧功能按钮 -->
        <template #toolbar>
          <div class="tableForm">
            <!-- <div class="tableForm4"> -->
            <!-- <el-button
                type="warning"
                :icon="ArrowDown"
                plain
                @click="downLoad"
                style="margin-right: 20px"
              >
                导出
              </el-button> -->
            <el-form inline :model="params">
              <el-form-item label="所属机构：" v-if="loginUser.role == 99">
                <el-select
                  v-model="params.designativeOrganizationGuid"
                  placeholder="请选择或搜索所属机构"
                  filterable
                  clearable
                  remote
                  reserve-keyword
                  :remote-method="remoteMethod"
                  style="width: 200px"
                >
                  <el-option
                    v-for="(dict, index) in organizaList"
                    :key="index"
                    :value="dict.guid"
                    :label="dict.fullname"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="姓名：">
                <el-input
                  v-model="params.subjectsName"
                  placeholder="请输入姓名"
                  clearable
                  style="width: 200px"
                  maxlength="99"
                />
              </el-form-item>
              <el-form-item label="身份证号：">
                <el-input
                  v-model="params.idCard"
                  placeholder="请输入身份证号"
                  clearable
                  style="width: 200px"
                  maxlength="99"
                />
              </el-form-item>
              <!--<el-form-item label="备忘录：">
                <el-input
                  v-model="params.message"
                  placeholder="请输入备忘录"
                  clearable
                  style="width: 200px"
                  maxlength="500"
                />
              </el-form-item>-->
              <el-form-item>
                <el-button type="primary" plain @click="reload">搜索</el-button>
                <el-button type="primary" plain @click="showHighSearch = true">
                  高级检索</el-button
                >
                <el-button type="info" @click="(params = {}), reload()"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
            <!-- </div> -->
          </div>
        </template>

        <!-- 表头工具按钮 -->
        <!-- <template #tools>
          <el-space size="default" :wrap="true">
            <el-link type="primary" @click="searchExpand = !searchExpand">
              {{ searchExpand ? '普通搜索' : '高级搜索' }}
            </el-link>
            <el-divider direction="vertical" style="margin: 0" />
          </el-space>
        </template> -->
        <template #tools>
          <div class="ele-tool" @click="downLoad">
            <img
              src="@/assets/exportIcon.svg"
              alt=""
              style="width: 19px"
            />
          </div>
        </template>

        <template #jobGroup="{ row }">
          <dict-data type="tag" code="sys_job_group" v-model="row.jobGroup" />
        </template>
        <template #action="{ row }">
          <el-space>
            <el-link
              type="primary"
              :underline="false"
              @click="handleAddorEdit(row)"
              style="margin-right: 10px; color: #507aff"
            >
              编辑
            </el-link>
            <!-- <el-divider direction="vertical" /> -->
            <el-link
              type="primary"
              :underline="false"
              @click="handleDelete(row)"
              style="color: #507aff"
            >
              删除
            </el-link>
          </el-space>
        </template>
        <template #idCard="{ row }">
          <div style="display: flex; align-items: center">
            <span style="flex: 1">{{
              $PhoneOrIdCrad(row.idCard, 'idCard')
            }}</span>
            <img
              :src="row.isShow ? eyeOpen : eyeClose"
              style="
                transform: scale(1.28);
                margin: 0 5px;
                cursor: pointer;
                width: 14px;
                height: 14px;
              "
              @click="changeImg(row)"
              v-if="getIdCard(row)"
            />
          </div>
        </template>
        <template #telphone="{ row }">
          <div style="display: flex; align-items: center">
            <span style="flex: 1">{{
              $PhoneOrIdCrad(row.telephone, 'phone')
            }}</span>
            <img
              :src="row.isShow2 ? eyeOpen : eyeClose"
              style="
                transform: scale(1.28);
                cursor: pointer;
                margin: 0 5px;
                width: 14px;
                height: 14px;
              "
              @click="changeImg2(row)"
              v-if="getTelphone(row)"
            />
          </div>
        </template>
        <template #valid="{ row }">
          <el-space>
            <el-link
              type="info"
              v-if="row.valid == 0"
              :underline="false"
              style="color: #999999"
              >否</el-link
            >
            <el-link
              type="primary"
              v-if="row.valid == 1"
              :underline="false"
              style="color: #67c32a"
              >是</el-link
            >
          </el-space>
        </template>
        <template #ispublic="{ row }">
          <el-space>
            <el-link
              type="info"
              v-if="row.ispublic == 0"
              :underline="false"
              style="color: #999999"
              >否</el-link
            >
            <el-link
              type="primary"
              v-if="row.ispublic == 1"
              :underline="false"
              style="color: #67c32a"
              >是</el-link
            >
          </el-space>
        </template>
      </ele-pro-table>
      <!-- 弹窗 -->
    </ele-card>
    <editmemo v-model="showCreate" :data="current" @done="reload" />
    <highSearchModal
      v-model="showHighSearch"
      :data="params"
      :orgList="organizaList"
      @done="highSearchLoad"
      v-if="showHighSearch"
    />
  </div>
</template>
<script>
  export default {
    name: 'MemoInfor'
  };
</script>
<script setup>
  import { ref, computed, nextTick } from 'vue';
  import {
    sysMemoList,
    sysMemoDelete,
    exportSysmemo
  } from '@/api/subjects/memoInfor/index.js';
  import {
    validIdCard
  } from '@/utils/common.js';
  import { organizationList } from '@/api/subjects/unsuitPeople/index.js';
  import editmemo from './components/editmemo.vue';
  import highSearchModal from './components/highSearchModal.vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { ElMessageBox } from 'element-plus/es';
  import { Icon } from '@iconify/vue';
  import { useRouter } from 'vue-router';
  import { getCompleteInfo } from '@/api/subjects/studyManage/index.js';
  import { useUserStore } from '@/store/modules/user';
  import eyeOpen from '@/assets/eyeOpen.png';
  import eyeClose from '@/assets/eyeClose.png';
  const userStore = useUserStore();
  /** 当前用户信息 */
  const loginUser = computed(() => userStore.info ?? {});

  const router = useRouter();
  defineProps({
    // 表头背景
    tableHeader: String,
    // 表格高度
    tableFullHeight: Boolean
  });
  // 搜索表单是否展开
  const showHighSearch = ref(false);

  // 表格列配置
  const columns2 = ref([
    // {
    //   type: 'selection',
    //   columnKey: 'selection',
    //   width: 48,
    //   align: 'center',
    //   fixed: 'left'
    // },
    {
      type: 'index',
      columnKey: 'index',
      label: '序号',
      minWidth: 65,
      align: 'center',
      showOverflowTooltip: true,
      fixed: 'left',
      isShow: true
    },
    {
      prop: 'organizationName',
      label: '所属机构',
      minWidth: 220,
      showOverflowTooltip: true,
      isShow: loginUser.value.role == 99 ? true : false
    },
    {
      prop: 'subjectsName',
      label: '姓名',
      minWidth: 90,
      showOverflowTooltip: true,
      isShow: true
    },
    {
      prop: 'idCard',
      label: '身份证号',
      width: 240,
      showOverflowTooltip: true,
      slot: 'idCard',
      isShow: true
    },
    {
      prop: 'telephone',
      label: '电话号码',
      width: 170,
      showOverflowTooltip: true,
      slot: 'telphone',
      isShow: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'ispublic',
      label: '是否公开',
      minWidth: 100,
      showOverflowTooltip: true,
      slot: 'ispublic',
      isShow: true
    },
    {
      prop: 'usersName',
      label: '创建人',
      minWidth: 100,
      showOverflowTooltip: true,
      isShow: true
    },
    {
      prop: 'date',
      label: '创建时间',
      minWidth: 120,
      showOverflowTooltip: true,
      isShow: true
    },
    {
      prop: 'indate',
      label: '有效期至',
      minWidth: 120,
      showOverflowTooltip: true,
      isShow: true
    },
    {
      prop: 'valid',
      label: '是否有效',
      minWidth: 100,
      showOverflowTooltip: true,
      slot: 'valid',
      isShow: true
    },
    {
      prop: 'message',
      label: '备忘录信息',
      minWidth: 200,
      showOverflowTooltip: true,
      isShow: true
    },
    {
      columnKey: 'action',
      label: '操作',
      hideInSetting: true,
      width: 120,
      align: 'left',
      resizable: false,
      slot: 'action',
      fixed: 'right',
      isShow: true
    }
  ]);
  const columns = ref([]);
  columns.value = columns2.value.filter((s) => s.isShow);
  // 表格选中数据
  const selections = ref([]);
  // 新增修改弹框控制
  const showCreate = ref(false);
  // 当前选中数据
  const current = ref({});
  // 表格实例
  const tableRef = ref(null);
  const organizaList = ref([]);
  const organizaCache = ref([]);
  const params = ref({
    pageNumber: 1,
    pageSize: 15
  });
  const highSearchLoad = (value) => {
    params.value = value.p;
    organizaList.value = value.o;
    reload();
  };
  const getTelphone = (row) => {
    let idCard = row.telephone;
    if (idCard) {
      // 获取前3位
      const firstPart = idCard.slice(0, 3);
      // 生成中间4位的星号
      const middlePart = '*'.repeat(4);
      // 获取后4位
      const lastPart = idCard.slice(7);

      // 拼接成新的字符串
      return firstPart + middlePart + lastPart;
    } else {
      return '';
    }
  };
  const changeImg2 = (row) => {
    row.isShow2 = !row.isShow2;
    getCompleteInfo({ guid: row.subjectsGuid, type: '2' }).then((res) => {
      row.telephone = res.msg;
      row.telephone = getPhone(row);
    });
    return;
    /* let idCard = row.telephone;
    if (row.isShow2 && row.telephone) {
      // 获取前3位
      const firstPart = idCard.slice(0, 3);
      // 生成中间4位的星号
      const middlePart = '*'.repeat(4);
      // 获取后4位
      const lastPart = idCard.slice(7);

      // 拼接成新的字符串
      return firstPart + middlePart + lastPart;
    } else {
      return idCard;
    }*/
  };
  const getCard = (row) => {
    try {
      if (row.isShow) {
        return row.idCard;
      }
      return row.idCard.replace(/(\d{6})\d+(\d{4})/, '$1********$2');
    } catch (error) {
      console.log(error);
      return row.idCard;
    }
  };
  const getPhone = (row) => {
    try {
      if (row.isShow2) {
        return row.telephone;
      }
      return row.telephone.replace(/(\d{3})\d+(\d{4})/, '$1****$2');
    } catch (error) {
      console.log(error);
      return row.telephone;
    }
  };
  const changeImg = (row) => {
    row.isShow = !row.isShow;
    getCompleteInfo({ guid: row.subjectsGuid, type: '1' }).then((res) => {
      row.idCard = res.msg;
      row.idCard = getCard(row);
    });
    return;
    /* let idCard = row.idCard;
    if (row.isShow && idCard) {
      // 获取前6位
      const firstPart = idCard.slice(0, 6);
      // 生成中间8位的星号
      const middlePart = '*'.repeat(8);
      // 获取后4位
      const lastPart = idCard.slice(14);

      // 拼接成新的字符串
      return firstPart + middlePart + lastPart;
    } else {
      return idCard;
    }*/
  };
  const getIdCard = (row) => {
    let idCard = row.idCard;
    if (idCard) {
      // 获取前6位
      const firstPart = idCard.slice(0, 6);
      // 生成中间8位的星号
      const middlePart = '*'.repeat(8);
      // 获取后4位
      const lastPart = idCard.slice(14);

      // 拼接成新的字符串
      return firstPart + middlePart + lastPart;
    }
    return '';
  };
  // 表格数据源
  const datasource = ({ page, limit, where }) => {
    return sysMemoList({
      ...where,
      pageNumber: page,
      pageSize: limit
    });
  };

const reload = () => {
    if (params.value.idCard) {
      params.value.idCard = params.value.idCard.replace(/\s+/g, "");
      if (!validIdCard(params.value.idCard)) {
        EleMessage.error('请输入完整的身份证号');
        return;
      }
    }
    tableRef.value?.reload?.({ where: params.value });
  };
  const handleAddorEdit = (row = null) => {
    showCreate.value = true;
    current.value = row;
  };
  /** 删除按钮操作 */
  const handleDelete = (row) => {
    ElMessageBox.confirm(`确定要删除该备忘录信息吗？`, '提示', {
      confirmButtonText: '是',
      cancelButtonText: '否',
      center: true
    }).then(async () => {
      try {
        await sysMemoDelete([row.guid]).then((res) => {
          if (res.code == 200) {
            EleMessage.success(`操作成功！`);
            reload();
          } else {
            EleMessage.error(res.msg);
          }
        });
      } catch (e) {
        EleMessage.error(e);
      }
    });
  };
  /* 导出数据 */
const downLoad = () => {
    if (params.value.idCard) {
      params.value.idCard = params.value.idCard.replace(/\s+/g, "");
      if (!validIdCard(params.value.idCard)) {
        EleMessage.error('请输入完整的身份证号');
        return;
      }
    }
    const loading = EleMessage.loading('请求中..');
    // tableRef.value?.fetch?.(({ where }) => {
    if (!params.value.pageNumber) {
      params.value.pageNumber = 1;
      params.value.pageSize = 15;
    }
    exportSysmemo(params.value)
      .then(() => {
        loading.close();
      })
      .catch((e) => {
        loading.close();
        EleMessage.error(e.msg);
      });
    // });
  };
  const remoteMethod = (query) => {
    if (query) {
      organizationList({ keywords: query, pageNumber: 1, pageSize: 100 }).then(
        (res) => {
          organizaList.value = res.data.list;
        }
      );
    } else {
      organizaList.value = organizaCache.value;
    }
  };
  nextTick(() => {
    organizationList({
      pageNumber: 1,
      pageSize: 50,
      orderBy: '',
      orderItem: 'asc'
    }).then((res) => {
      console.log(res);
      organizaList.value = res.data.list;
      organizaCache.value = res.data.list;
    });
  });
</script>
<style lang="scss" scoped>
  .index {
    // width: 100%;
    height: calc(100vh - 81px - 14px);
    background: #ffffff;
    border-radius: 8px;
    padding: 0 20px;
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    overflow-x: hidden;
  }
  .tableForm4 {
    display: flex;
    flex-wrap: wrap;
  }
  .top-search4 {
    display: flex;
  }
  :deep(.ele-toolbar .ele-toolbar-tools) {
    position: relative;
    // top: -7px !important;
  }
</style>
<style></style>
