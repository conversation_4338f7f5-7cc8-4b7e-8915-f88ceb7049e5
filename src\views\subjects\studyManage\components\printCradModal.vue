<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="750"
    :model-value="modelValue"
    title="预览/打印卡牌"
    @update:modelValue="updateModelValue"
    class="simplebar-modal"
    align-center
  >
    <div v-if="loadingShow">
      <ele-loading :loading="loadingShow" text="加载中" background="red">
        <div style="width: 100%; height: 200px; background: #ffffff"></div>
      </ele-loading>
    </div>
    <div
      class="modal-dialog"
      data-simplebar
      style="padding: 0 15px; box-sizing: border-box;height: 60vh"
      v-else
    >
      <ele-printer
        v-model="printing"
        :direction="option.direction"
        :orientation="option.orientation"
        :margin="option.margin"
        :title="option.title"
        :target="option.target"
        :static="option.static"
        :body-style="{ overflow: 'hidden' }"
        @done="onPrintDone"
      >
        <div style="margin: 0 auto">
          <el-row :gutter="20">
            <el-col :span="12" v-for="(item, index) in list" :key="index">
              <div
                style="
                  width: 295px;
                  display: flex;
                  justify-content: center;
                  margin: 40px auto;
                "
              >
                <div
                  style="
                    width: 293px;
                    height: 423px;
                    border: 2px solid #141370;
                    padding: 20px 40px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    font-weight: bold;
                  "
                >
                  <div
                    style="text-align: center; margin: 20px 0; font-size: 16px"
                    >{{ item.fullname }}</div
                  >
                  <div style="flex: 1">
                    <img
                      :src="item.image"
                      alt="用户照片"
                      style="width: 180px; height: 200px; margin-bottom: 10px"
                    />
                  </div>
                  <div
                    style="
                      margin-bottom: 10px;
                      font-size: 14px;
                      display: flex;
                      width: 100%;
                    "
                    v-if="!item.joindate"
                  >
                    <div style="width: 70px; text-align: justify"
                      >筛 选 号 ：</div
                    >
                    <div
                      style="
                        border-bottom: 1px solid #000000;
                        flex: 1;
                        text-align: center;
                        white-space: pre-wrap;
                        word-break: break-all;
                      "
                    >
                      {{ item.filterno }}
                    </div>
                  </div>
                  <div
                    style="
                      margin-bottom: 10px;
                      font-size: 14px;
                      display: flex;
                      width: 100%;
                    "
                    v-if="item.joindate"
                  >
                    <div style="width: 70px; text-align: justify"
                      >入 组 号 ：</div
                    >
                    <div
                      style="
                        border-bottom: 1px solid #000000;
                        flex: 1;
                        text-align: center;
                        white-space: pre-wrap;
                        word-break: break-all;
                      "
                    >
                      {{ item.joinno }}
                    </div>
                  </div>
                  <div
                    style="
                      margin-bottom: 10px;
                      font-size: 14px;
                      display: flex;
                      width: 100%;
                    "
                  >
                    <span style="width: 70px; text-align: justify"
                      >姓名缩写：</span
                    >
                    <span
                      style="
                        border-bottom: 1px solid #000000;
                        flex: 1;
                        text-align: center;
                      "
                    >
                      {{ item.acronym }}
                    </span>
                  </div>
                  <div
                    style="
                      margin-bottom: 10px;
                      font-size: 14px;
                      display: flex;
                      width: 100%;
                    "
                  >
                    <span style="width: 70px; text-align: justify"
                      >项目编号：</span
                    >
                    <span
                      style="
                        border-bottom: 1px solid #000000;
                        flex: 1;
                        text-align: center;
                      "
                    >
                      {{ item.code }}
                    </span>
                  </div>
                  <div
                    style="
                      margin-bottom: 10px;
                      font-size: 14px;
                      display: flex;
                      width: 100%;
                    "
                    v-if="!item.joindate"
                  >
                    <span style="width: 70px; text-align: justify"
                      >筛选时间：</span
                    >
                    <span
                      style="
                        border-bottom: 1px solid #000000;
                        flex: 1;
                        text-align: center;
                      "
                    >
                      {{ item.filterdate }}
                    </span>
                  </div>
                  <div
                    style="
                      margin-bottom: 10px;
                      font-size: 14px;
                      display: flex;
                      width: 100%;
                    "
                    v-if="item.joindate"
                  >
                    <span style="width: 70px; text-align: justify"
                      >入组时间：</span
                    >
                    <span
                      style="
                        border-bottom: 1px solid #000000;
                        flex: 1;
                        text-align: center;
                      "
                    >
                      {{ item.joindate }}
                    </span>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </ele-printer>
    </div>
    <template #footer v-if="!loadingShow">
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
        <el-button type="primary" @click="printing = true">打 印</el-button>
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { getPrintCards } from '@/api/subjects/studyManage/index.js';
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';

  const printing = ref(false);
  /** 打印参数 */
  const option = reactive({
    direction: void 0,
    orientation: void 0,
    margin: 0,
    title: ' ',
    target: '_iframe',
    static: true
  });
  /** 打印结束事件 */
  const onPrintDone = () => {
    console.log('打印结束.');
  };
  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Array
  });
  const close = () => {
    updateModelValue(false);
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const list = ref([]);
  const loadingShow = ref(true);
  const ossBaseUrl = import.meta.env.VITE_APP_BASE_OSS;
  nextTick(async () => {
    getPrintCards(props.data.map((val) => val.guid)).then((res) => {
      loadingShow.value = false;
      if (res.code == 200) {
        res.data.forEach((el) => {
          if (!el.image.includes('http')) {
            el.image = ossBaseUrl + el.image;
          }
        });
        list.value = res.data;
        return;
      }
      EleMessage.error(res.msg);
    });
  });
</script>
<style lang="scss" scoped>
  .modal-dialog {
    width: 100%;
    height: 600px;
    overflow-y: auto;
    overflow-x: hidden;
  }
</style>
