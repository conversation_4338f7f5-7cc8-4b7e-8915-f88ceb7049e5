<template>
  <div>
    <ele-card class="volunteer-statistics-card" style="height: 410px" header="">
      <el-row>
        <el-col :span="8">
          <div class="title">{{ tableData?.area }}地区志愿者年龄分布</div>
          <div v-if="loading">
            <ele-loading :loading="loading" text="数据加载中" background="red">
              <div
                style="width: 100%; height: 500px; background: #ffffff"
              ></div>
            </ele-loading>
          </div>
          <div
            style="
              width: 100%;
              height: 340px;
              padding: 0 20px;
              box-sizing: border-box;
            "
          >
            <div ref="main" style="width: 100%; height: 100%"></div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="title">{{ tableData?.area }}地区志愿者类型分布</div>
          <div v-if="loading">
            <ele-loading :loading="loading" text="数据加载中" background="red">
              <div
                style="width: 100%; height: 500px; background: #ffffff"
              ></div>
            </ele-loading>
          </div>
          <div
            style="
              width: 100%;
              height: 340px;
              padding: 0 20px;
              box-sizing: border-box;
            "
          >
            <div ref="mainType" style="width: 100%; height: 100%"></div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="title">{{ tableData?.area }}地区志愿者性别分布</div>
          <div v-if="loading">
            <ele-loading :loading="loading" text="数据加载中" background="red">
              <div
                style="width: 100%; height: 500px; background: #ffffff"
              ></div>
            </ele-loading>
          </div>
          <div
            style="
              width: 100%;
              height: 340px;
              padding: 0 20px;
              box-sizing: border-box;
            "
          >
            <div ref="mainAge" style="width: 100%; height: 100%"></div>
          </div>
        </el-col>
      </el-row>
    </ele-card>
  </div>
</template>
<script setup name="projectRecommendationRecord">
  import { nextTick, onMounted, ref } from 'vue';
  import * as echarts from 'echarts';
  import { Icon } from '@iconify/vue';
  import { filterStatistic } from '@/api/subjects/home/<USER>';
  import { getSiteAreaUserData } from '@/api/project/index';
  import { getYAxisMaxValue } from '@/utils/common.js';
  const emit = defineEmits(['done']);
  const caseTimedateRange = ref([]);
  const monthType = ref(3);

  const main = ref(null);
  const mainType = ref(null);
  const mainAge = ref(null);
  const option = ref({});
  const optionType = ref({});
  const optionAge = ref({});
  const tableData = ref([]);
  const loading = ref(false);
  const chartInitData = async () => {
    const myChart = echarts.init(main.value);
    const myChartType = echarts.init(mainType.value);
    const myChartAge = echarts.init(mainAge.value);
    loading.value = true;

    await getSiteAreaUserData().then((res) => {
      tableData.value = res.data || [];
      console.log('##', res.data);
      loading.value = false;
    });
    emit('done', 'numchangeLoading');
    option.value = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        formatter: function (params) {}
      },
      legend: {
        data: tableData.value?.ageNames || []
      },
      xAxis: [
        {
          type: 'category',
          data: tableData.value?.ageNames || [],
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            interval: 0, // 显示所有标签
            rotate: 15, // 旋转标签
            formatter: function (value) {
              // 设置最大长度
              var maxLength = 8; // 最大字符数
              return value.length > maxLength
                ? value.substring(0, maxLength) + '...'
                : value;
            }
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          min: 0,
          axisLabel: {
            formatter: (value) => `${value}`
          },
          // name: '人次', // 左 Y 轴显示的文字
          nameLocation: 'end', // 文字位置
          nameGap: 30 // 文字与坐标轴的间距
        }
      ],
      series: [
        {
          name: '顺利',
          type: 'bar',
          data: tableData.value?.ageValue,
          barWidth: '25%',
          barCategoryGap: '30%',
          barGap: '20%',
          itemStyle: {
            color: '#5280FB'
          },
          label: {
            show: false,
            formatter: (params) => `${params.value}`
          },
          xAxisIndex: 0,
          z: 1
        }
      ]
    };

    optionType.value = {
      tooltip: {
        trigger: 'item',
        confine: true,
        borderWidth: 1
      },
      // legend: {
      //   top: '20%',
      //   orient: 'vertical',
      //   left: 'right',
      //   icon: 'circle'
      // },
      label: {
        alignTo: 'edge',
        formatter: '{name|{b}}\n{num|{c}}',
        rich: {
          num: {
            fontSize: 10,
            color: '#999'
          }
        }
      },
      series: [
        {
          type: 'pie',
          radius: '60%',
          data: [
            { value: tableData.value?.healthUserNumber, name: '健康' },
            { value: tableData.value?.patientUserNumber, name: '患者' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };

    optionAge.value = {
      tooltip: {
        trigger: 'item',
        confine: true,
        borderWidth: 1
      },
      // legend: {
      //   top: '20%',
      //   orient: 'vertical',
      //   left: 'right',
      //   icon: 'circle'
      // },
      label: {
        alignTo: 'edge',
        formatter: '{name|{b}}\n{num|{c}}',
        rich: {
          num: {
            fontSize: 10,
            color: '#999'
          }
        }
      },
      series: [
        {
          type: 'pie',
          // center: ['50%', '43%'],
          radius: '60%',
          // data: [tableData.value?.maleNumber, tableData.value?.femaleNumber],
          data: [
            { value: tableData.value?.maleNumber, name: '男' },
            { value: tableData.value?.femaleNumber, name: '女' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };

    option.value && myChart.setOption(option.value);
    option.value && myChartType.setOption(optionType.value);
    option.value && myChartAge.setOption(optionAge.value);
  };

  const initData = () => {
    nextTick(() => {
      chartInitData();
    });
  };

  const change = () => {
    initData();
  };

  onMounted(() => {
    initData();
  });
</script>
<style lang="scss" scoped>
  .title {
    text-align: center;
    // margin-left: 20px;
    margin-bottom: 20px;
    font-weight: 700;
    color: #000000eb;
    font-size: 16px;
  }

  .content {
    font-weight: 700;
    font-size: 18px;
    color: #5280fb;
  }

  .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .all-title {
    height: 22px;
    font-size: 16px;
    color: #718ebf;
    line-height: 22px;
    cursor: pointer;
  }

  .ele-page.is-multi-card.is-flex-table .volunteer-statistics-card {
    margin-bottom: 0;
    :deep(.ele-card-body) {
      padding-bottom: 0;
    }
  }

  :deep(.ele-card-header) {
    border: none;
  }
</style>
