<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ paddingBottom: '2px' }">
    <el-form label-width="88px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="公告标题">
            <el-input
              clearable
              v-model.trim="form.announcementTitle"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="公告描述">
            <el-input
              clearable
              v-model.trim="form.announcementDesc"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="生效时间">
            <el-date-picker
              unlink-panels
              type="daterange"
              v-model="dateRange"
              range-separator="-"
              value-format="YYYY-MM-DD"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 日期范围 */
  const dateRange = ref(['', '']);

  /** 表单数据 */
  const [form, resetForm] = useFormData({
    announcementTitle: '',
    announcementDesc: '',
    dateRange: ['', '']
  });

  /** 重置表单数据 */
  const resetFields = () => {
    resetForm();
    dateRange.value = ['', ''];
  };

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value || [];
    emit('search', {
      ...form,
      startTime: d1,
      endTime: d2
    });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };

  defineExpose({ resetFields });
</script>
