<template>
  <el-input
    :model-value="displayValue"
    :placeholder="placeholder"
    :clearable="clearable"
    :style="style"
    :maxlength="maxlength"
    @input="validateMobile"
    :disabled="disabled"
  />
</template>

<script setup>
  import { watch, ref } from 'vue';

  const props = defineProps({
    modelValue: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: '请输入手机号码'
    },
    maxlength: {
      type: [Number, String],
      default: 13
    },
    disabled: {
      type: Boolean,
      default: false
    },
    style: {
      type: Object,
      default: () => ({ width: '100%' })
    },
    clearable: {
      type: Boolean,
      default: true
    }
  });

  const emit = defineEmits(['update:modelValue']);

  // 格式化手机号
  const formatPhone = (val) => {
    if (!val) return '';
    if (val.includes('-')) return val; // -不处理
    const cleaned = val.replace(/\D/g, '');
    // 修改正则表达式，实现3-4-4格式
    return cleaned
      .replace(/(\d{3})(\d{0,4})(\d{0,4})/, (_, $1, $2, $3) => {
        let result = $1;
        if ($2) result += ` ${$2}`;
        if ($3) result += ` ${$3}`;
        return result;
      })
      .trim();
  };

  // 初始格式化
  const displayValue = ref(formatPhone(props.modelValue));

  // 监听父组件传入的值变化
  watch(
    () => props.modelValue,
    (newVal) => {
      displayValue.value = formatPhone(newVal);
    }
  );

  const validateMobile = (val) => {
    const formatted = formatPhone(val);
    displayValue.value = formatted;
    // 发送去除空格的值给父组件
    emit('update:modelValue', formatted.replace(/\s/g, ''));
  };
</script>
