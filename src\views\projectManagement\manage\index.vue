<template>
  <ele-page :flex-table="true" hide-footer>
    <recommender-search ref="searchRef" @search="reload" />
    <ele-card :flex-table="true" :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="projectId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        :footer-style="{ paddingBottom: '16px' }"
      >
        <template #recruitStatus="{ row }">
          <div
            v-if="row.platformCloseFlag == 1"
            style="display: flex; justify-content: center; align-items: center"
          >
            <span style="color: red"
              >{{ row?.recruitStatusDesc }}（平台下架）</span
            >
            <el-tooltip
              effect="dark"
              :content="row?.platformReason"
              placement="top"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
          <div v-else :class="getStatusClass(row.recruitStatus)">
            {{ getStatusText(row.recruitStatus) }}
          </div>
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="toSort(row)">
            排序
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            v-permission="'system:dict:edit'"
            type="primary"
            :underline="false"
            @click="toDetails(row)"
          >
            查看详情
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            v-if="row?.platformCloseFlag == 0"
            type="danger"
            :underline="false"
            @click="showforcedDelistingDialog(row)"
          >
            强制下架
          </el-link>
          <el-link
            v-if="row?.platformCloseFlag == 1"
            type="danger"
            :underline="false"
            @click="grounding(row)"
          >
            上架
          </el-link>
        </template>
      </ele-pro-table>
      <el-dialog
        v-model="dialogVisible"
        :title="title"
        width="600px"
        align-center
        :show-close="false"
        :modal-append-to-body="false"
      >
        <el-form ref="formRef" label-position="left" label-width="110px">
          <el-form-item label="下架原因：">
            <el-input
              type="textarea"
              v-model="platformreason"
              :rows="3"
              placeholder="请输入下架原因"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="forcedDelisting()">确 定</el-button>
        </template>
      </el-dialog>
    </ele-card>
  </ele-page>
</template>

<script setup name="RecommenderManage">
  import { ref, watch, nextTick } from 'vue';
  import { InfoFilled } from '@element-plus/icons-vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useDictData } from '@/utils/use-dict-data';
  import RecommenderSearch from '../components/recommender-search.vue';
  import { usePageTab } from '@/utils/use-page-tab';
  import { useUserStore } from '@/store/modules/user';
  import { useRouter } from 'vue-router';

  import {
    getAllProjectPageList,
    changePlatformStatus,
    setProjectSort
  } from '@/api/system/project';

  const { push } = useRouter();

  const { removePageTab } = usePageTab();

  /** 字典数据 */
  const [statusDicts] = useDictData(['sys_normal_disable']);

  const props = defineProps({
    /** 字典类型 */
    dictType: String
  });

  const userStore = useUserStore();

  const title = ref('强制下架');

  const currentData = ref({});

  const platformreason = ref('');

  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 60,
      align: 'center'
    },
    {
      prop: 'projectId',
      label: '项目ID',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'projectRecruitName',
      label: '项目名称',
      align: 'center',
      minWidth: 110,
      showOverflowTooltip: true
    },
    {
      prop: 'recruitStatus',
      label: '项目状态',
      width: 200,
      align: 'center',
      slot: 'recruitStatus'
    },
    {
      prop: 'projectRecruitTypeDesc',
      label: '项目类型',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'siteName',
      label: '招募中心',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'projectRecruitDeadline',
      label: '招募截止日期',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'publishOrgName',
      label: '发布组织',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'sort',
      label: '排序',
      align: 'center',
      width: 80
    },
    {
      prop: 'publishTime',
      label: '发布成功时间',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '创建时间',
      align: 'center',
      width: 180
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 260,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  const dialogVisible = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders }) => {
    return getAllProjectPageList({
      ...where,
      ...orders,
      ...pages
    });
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 1:
        return 'status-draft'; // 草稿
      case 2:
        return 'status-recruiting'; // 招募中
      case 3:
        return 'status-failed'; // 审核失败
      case 4:
        return 'status-closed'; // 已关闭
      default:
        return '';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 1:
        return '草稿';
      case 2:
        return '招募中';
      case 3:
        return '审核失败';
      case 4:
        return '已关闭';
      default:
        return '';
    }
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 排序 */
  const toSort = (row) => {
    ElMessageBox.prompt('请输入该项目的排序号', '排序', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputPattern: /^[1-9]\d{0,2}$/,
      inputErrorMessage: '序号必须为正整数且不能超出3位数'
    })
      .then(async ({ value }) => {
        const params = { projectId: row?.projectId, sort: value };
        await setProjectSort(params);
        EleMessage.success('设置成功');
        reload();
      })
      .catch((e) => {
        console.log(e);
      });
  };

  /** 查看详情 */
  const toDetails = async (row) => {
    const path = '/recruit/edit-project';
    removePageTab({ key: path });
    nextTick(() => {
      push({
        path,
        query: row ? { projectId: row.projectId, isReview: true } : void 0
      });
    });
  };

  const showforcedDelistingDialog = (row) => {
    platformreason.value = '';
    Object.assign(currentData.value, row);
    dialogVisible.value = true;
  };

  const forcedDelisting = async () => {
    try {
      const params = {
        projectId: currentData.value?.projectId,
        status: 1,
        platformreason: platformreason.value
      };
      const res = await changePlatformStatus(params);
      EleMessage.success('下架成功');
      dialogVisible.value = false;
      reload();
    } catch (e) {
      EleMessage.error(e?.message);
    }
  };

  const grounding = async (row) => {
    try {
      const params = {
        projectId: row?.projectId,
        status: 0
      };
      const res = await changePlatformStatus(params);
      EleMessage.success('上架成功');
      reload();
    } catch (e) {
      EleMessage.error(e?.message);
    }
  };

  // 监听字典id变化
  // watch(
  //   () => props.dictType,
  //   () => {
  //     searchRef.value?.resetFields?.();
  //     reload({});
  //   }
  // );
</script>

<style lang="scss" scoped>
  .status-draft {
    color: gray; /* 草稿颜色 */
  }
  .status-recruiting {
    color: green; /* 招募中颜色 */
  }
  .status-failed {
    color: red; /* 审核失败颜色 */
  }
  .status-closed {
    color: orange; /* 已关闭颜色 */
  }
</style>
