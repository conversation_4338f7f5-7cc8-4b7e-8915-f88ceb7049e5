import request from '@/utils/request-business';

/**
 * 机构科室分页
 *  @param {*} data
 *  @returns
 */
export async function getSiteDeptPageList(params) {
  const res = await request.get('/siteDeptInfo/queryPageList', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 查询科室信息
 * @param {*} data
 * @returns
 */
export async function searchSiteDeptInfo(id) {
  const res = await request.get(`/siteDeptInfo/${id}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 新增科室信息
 * @param {*} data
 * @returns
 */
export async function addSiteDeptInfo(data) {
  const res = await request.post(`/siteDeptInfo`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 查询科室信息列表
 * @param {*} data
 * @returns
 */
export async function searchSiteDeptInfoList(params) {
  const res = await request.get('/siteDeptInfo', { params });
  if (res.data.code === 200) {
    return res.data?.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 修改科室信息
 *  @param {*} data
 *  @returns
 */
export async function editSiteDeptInfo(data) {
  const res = await request.put('/siteDeptInfo', data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 删除科室信息
 *  @param {*} data
 *  @returns
 */
export async function deleteSiteDeptInfo(id) {
  const res = await request.delete(`/siteDeptInfo/${id}`);
  if (res.status === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
