vue vue vue
<template>
  <ele-page flex-table>
    <div class="container">
      <!-- 左侧边栏 -->
      <ele-card class="sidebar">
        <div>
          <el-select
            v-model="selectedProject"
            placeholder="请选择"
            @change="fetchData"
          >
            <el-option
              v-for="option in projectOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            >
            </el-option>
          </el-select>
        </div>
        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane label="单聊" name="single">
            <div class="chat-list">
              <div
                v-for="chat in singleChatList"
                :key="chat.sessionId"
                :class="[
                  'chat-item',
                  chat.sessionId === currentChat.sessionId ? 'active' : ''
                ]"
                @click="selectChat(chat)"
              >
                <div class="avatar">
                  <el-icon><UserFilled /></el-icon>
                  <span v-if="chat.unreadCount > 0" class="unread-count">{{
                    chat.unreadCount
                  }}</span>
                </div>
                <div class="chat-info">
                  <div class="chat-header">
                    <span class="chat-name">{{ chat.sessionName }}</span>
                    <span class="chat-time">{{ chat.lastTime }}</span>
                  </div>
                  <div class="chat-last-message">{{ chat.lastContent }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="群聊" name="double">
            <div class="chat-list">
              <div
                v-for="chat in groupChatList"
                :key="chat.sessionId"
                :class="[
                  'chat-item',
                  chat.sessionId === currentChat.sessionId ? 'active' : ''
                ]"
                @click="selectChat(chat)"
              >
                <div class="avatar">
                  <el-icon><UserFilled /></el-icon>
                  <span v-if="chat.unreadCount > 0" class="unread-count">{{
                    chat.unreadCount
                  }}</span>
                </div>
                <div class="chat-info">
                  <div class="chat-header">
                    <span class="chat-name">{{ chat.sessionName }}</span>
                    <span class="chat-time">{{ chat.lastTime }}</span>
                  </div>
                  <div class="chat-last-message">{{ chat.lastContent }}</div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </ele-card>
      <!-- 右侧聊天区域 -->
      <div class="chat-area">
        <div class="chat-header-r">
          <h2 v-if="currentChat.sessionName" class="chat-title">{{
            currentChat.sessionName
          }}</h2>
          <div v-if="currentChat.age" class="chat-details">
            <span>{{ currentChat.age }}岁 | {{ currentChat.sex }}</span>
            <!-- <span>网龄：{{ currentChat.onlineTime }}</span>
            <span>BMI: {{ currentChat.bmi }}</span> -->
          </div>
          <div v-if="activeName == 'single'" style="width: 100%">
            <template v-if="currentChat?.projectName">
              <span
                :class="
                  currentChat?.projectRecruitType == 1 ? 'r-title' : 's-title'
                "
              >
                {{
                  currentChat?.projectRecruitType == 1 ? '【健康】' : '【患者】'
                }}
              </span>
              <span class="my-title">{{ currentChat?.projectName }}</span>
            </template>
            <template v-if="currentChat?.applyStatusDesc">
              <span style="margin-left: 20px"
                ><span class="m-title">报名状态：</span
                >{{ currentChat?.applyStatusDesc }}
              </span>
            </template>
          </div>
        </div>
        <div class="messages" ref="messageContainer">
          <div v-for="msg in messages" :key="msg.messageId" class="message">
            <div :class="['message-row', msg.myMsg ? 'self' : '']">
              <div :class="['avatar', msg.myMsg ? 'myself' : '']">
                <el-icon :class="[msg.myMsg ? 'myself1' : '']"
                  ><UserFilled
                /></el-icon>
              </div>
              <div :class="['message-content', msg.myMsg ? 'self' : '']">
                <div>
                  <span>{{ msg.sendUserName }}</span>
                  <span class="message-time">{{ msg.sendTime }}</span>
                </div>
                <div class="message-text">{{ msg.content }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="input-area">
          <div class="input-container">
            <el-input
              v-model="inputMessage"
              placeholder="请输入消息"
              type="textarea"
              :rows="3"
              class="input"
              @keyup.enter="sendMessage"
            />
            <el-button type="primary" @click="sendMessage">发送</el-button>
          </div>
        </div>
      </div>
    </div>
  </ele-page>
</template>
<script setup name="chat">
  import { ref, onMounted, nextTick, computed } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { initSSE } from '@/utils/sse';
  import {
    getChatList,
    getSessionDetail,
    pushMessage,
    getImProjectSelects
  } from '@/api/im/index';
  import { UserFilled } from '@element-plus/icons-vue';
  import { useUserStore } from '@/store/modules/user';
  import { useRoute } from 'vue-router';
  const route = useRoute();

  const userStore = useUserStore();

  const inputMessage = ref('');

  const projectOptions = ref([]);

  const selectedProject = ref(null);

  const messageContainer = ref();

  const activeName = ref('single');

  const singleChatList = ref([]);
  const groupChatList = ref([]);

  const currentChat = ref({});

  const messages = ref([]);

  const currentSessionId = ref();

  const currentSessionData = ref({});

  const loginUser = computed(() => userStore.info ?? {});

  const scrollToBottom = () => {
    if (messageContainer.value) {
      messageContainer.value.scrollTop = messageContainer.value.scrollHeight;
    }
  };

  const selectChat = async (chat) => {
    try {
      const params = {
        sessionId: chat?.sessionId,
        page: 1,
        limit: 100
      };
      chat.unreadCount = 0;
      currentSessionData.value = chat;
      currentSessionId.value = chat?.sessionId;
      const data = await getSessionDetail(params);
      console.log(data);
      Object.assign(currentChat.value, data);

      messages.value = (data?.messages || []).reverse();
      nextTick(() => {
        scrollToBottom();
      });
    } catch (e) {
      EleMessage.error(e.message);
    }
  };

  const formatDateTime = (date) => {
    const year = date.getFullYear(); // 获取年份
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 获取月份（注意：月份从 0 开始）
    const day = String(date.getDate()).padStart(2, '0'); // 获取日期
    const hours = String(date.getHours()).padStart(2, '0'); // 获取小时
    const minutes = String(date.getMinutes()).padStart(2, '0'); // 获取分钟
    const seconds = String(date.getSeconds()).padStart(2, '0'); // 获取秒钟
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`; // 返回格式化的日期时间字符串
  };

  const sendMessage = async () => {
    console.log(inputMessage.value);
    let sendMessage = inputMessage.value.trim();
    if (!sendMessage || !currentChat.value?.sessionName) return;
    try {
      const params = {
        sessionId: currentSessionId.value,
        content: sendMessage
      };
      await pushMessage(params);
      messages.value.push({
        messageId: Date.now(),
        content: inputMessage.value,
        sendTime: formatDateTime(new Date()),
        myMsg: true,
        sendUserName: loginUser.value.nickName
      });
      inputMessage.value = '';
      nextTick(() => {
        scrollToBottom();
      });
    } catch (e) {
      EleMessage.error(e);
    }
  };

  const initData = async () => {
    try {
      const projectId = route?.query?.projectId;
      const options = await getImProjectSelects();
      projectOptions.value = options;
      if (projectId) {
        selectedProject.value = projectId;
      } else {
        if (options.length > 0) {
          selectedProject.value = options[0]?.value;
        }
      }
      const data = await getChatList(selectedProject.value);
      singleChatList.value =
        data.filter((session) => session.targetType == 1) || [];
      groupChatList.value =
        data.filter((session) => session.targetType == 2) || [];
    } catch (e) {
      EleMessage.error(e.message);
    }
  };

  const fetchData = async (projectId) => {
    try {
      if (!projectId) return;
      const data = await getChatList(projectId);
      singleChatList.value =
        data.filter((session) => session.targetType == 1) || [];
      groupChatList.value =
        data.filter((session) => session.targetType == 2) || [];
    } catch (e) {
      EleMessage.error(e.message);
    }
  };

  onMounted(() => {
    // initSSE(import.meta.env.VITE_APP_BASE_API_URL + '/resource/sse');
    initSSE(import.meta.env.VITE_APP_BASE_API_URL + '/resource/sse', (res) => {
      try {
        console.log('Received data:', res);
        if (!res) return;
        let data = JSON.parse(res);
        if (data && currentSessionId.value == data?.sessionId) {
          const { content, messageId } = data;
          messages.value.push({
            messageId,
            content,
            sendUserName: currentSessionData.value?.sessionName,
            sendTime: formatDateTime(new Date()),
            myMsg: false
          });
        }
      } catch (e) {
        EleMessage.error(e.message);
      }
    });
    initData();
  });
</script>
<style scoped lang="scss">
  .container {
    display: flex;
    height: 100%;
  }
  .sidebar {
    height: 100%;
    width: 320px;
    margin-right: 8px;
    margin-bottom: 0px;
  }
  .tabs {
    display: flex;
    margin-bottom: 16px;
  }
  .tab-button {
    flex: 1;
    padding: 12px 0;
    font-weight: 500;
    border: none;
    background: none;
    cursor: pointer;
    color: #6b7280;
    transition:
      color 0.3s,
      border-bottom 0.3s;
    border-bottom: 2px solid transparent;
    &.active {
      border-bottom: 2px solid #3b82f6;
      color: #3b82f6;
    }
    &:hover {
      color: #3b82f6;
    }
  }
  .search-container {
    margin-bottom: 16px;
  }
  .input {
    border-radius: 0.375rem;
    border: 1px solid #e5e7eb;
    background-color: #f3f4f6;
    font-size: 14px;
  }
  .chat-list {
    flex-grow: 1;
    overflow-y: auto;
  }
  .chat-item {
    display: flex;
    cursor: pointer;
    align-items: flex-start;
    padding: 12px 16px;
    gap: 12px;
    transition: background-color 0.3s;
    &:hover {
      background-color: #f9fafb;
    }
    &.active {
      background-color: #eff6ff;
    }
  }
  .avatar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f3f4f6;
  }
  .avatar-img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
  .chat-info {
    flex: 1;
    min-width: 0;
  }
  .chat-name {
    font-weight: 500;
  }
  .chat-time {
    font-size: 0.75rem;
    color: #9ca3af;
  }
  .chat-last-message {
    margin-top: 4px;
    font-size: 14px;
    color: #6b7280;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .m-title {
    font-size: 16px;
    color: #374151;
    font-weight: 500;
  }

  .my-title {
    font-weight: 500;
    font-size: 16px;
    color: #374151;
  }

  .s-title {
    color: #ee530c;
    font-weight: 500;
    font-size: 16px;
  }

  .r-title {
    color: #209373;
    font-weight: 500;
    font-size: 16px;
  }

  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
  }

  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chat-header-r {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    padding: 16px;
    flex-wrap: wrap;
    align-items: center;
  }
  .chat-title {
    margin: 10px 0;
    font-size: 18px;
    color: #3d3d3d;
    font-weight: 500;
  }
  .chat-details {
    display: flex;
    margin-left: 24px;
    font-size: 16px;
    color: #6b7280;
  }
  .messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
  }
  .message {
    margin-bottom: 24px;
  }
  .message-row {
    display: flex;
    gap: 16px;
  }
  .message-row.self {
    flex-direction: row-reverse;
  }

  .myself {
    background-color: #dbeafe !important;
  }

  .myself1 {
    color: #2563eb;
  }

  .message-content {
    max-width: 70%;
  }
  .message-time {
    margin-left: 8px;
    margin-bottom: 4px;
    font-size: 14px;
    color: #6b7280;
  }
  .message-text {
    border-radius: 0.375rem;
    padding: 8px 16px;
    font-size: 14px;
    background-color: #f3f4f6;
    color: #000;
    &.self {
      background-color: #3b82f6;
      color: white;
    }
  }
  .input-area {
    border-top: 1px solid #e5e7eb;
    padding: 16px;
  }
  .input-container {
    display: flex;
    gap: 16px;
  }

  .avatar {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    overflow: visible;
    background-color: #f3f4f6;
    position: relative; /* 使徽章能够绝对定位 */
  }
  .unread-count {
    z-index: 1000;
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: #ff4d4f; /* 红色背景 */
    color: white; /* 白色字体 */
    border-radius: 50%; /* 圆形 */
    padding: 2px 6px; /* 内边距 */
    font-size: 12px; /* 字体大小 */
    min-width: 10px; /* 最小宽度 */
    text-align: center; /* 文本居中 */
  }
</style>
