/**
 * 登录用户状态管理
 */
import { defineStore } from 'pinia';
import { toTree, mapTree, isExternalLink } from '@hnjing/zxzy-admin-plus/es';
import { getUserInfo, getUserMenu } from '@/api/layout';
import { fastLogin } from '@/api/subjects/studyManage/index';
import { getSsoToken } from '@/utils/token-util';

import menuList from '../../router/modules/index';
import { stringify } from 'postcss';

export const useUserStore = defineStore('user', {
  state: () => ({
    initShow: 0,
    /** 当前登录用户的信息 */
    info: null,
    /** 当前登录用户的菜单 */
    menus: null,
    /** 当前登录用户的权限 */
    authorities: [],
    /** 当前登录用户的角色 */
    roles: [],
    /** 字典数据缓存 */
    dicts: {},
    /** 是否是机构 */
    isSite: false,
    backlogList: []
  }),
  actions: {
    /**
     * 请求登录用户的个人信息/权限/角色/菜单
     */
    async fetchUserInfo() {
      // todo:模版使用的是固定菜单，项目使用时注意使用接口数据
      // const { menus, homePath } = formatMenus(menuList);
      // this.setMenus(menus);

      const result = await getUserInfo().catch((e) => console.error(e));
      if (!result) {
        return {};
      }
      if (
        result.user.tenantId &&
        result.user.tenantName &&
        result.user.tenantId != '000000'
      ) {
        let roleKey = '';
        // 获取当前用户的角色
        const roles = result.roles;
        roles.forEach((role) => {
          if (roleKey != '') {
            roleKey += ',';
          }
          roleKey += role;
        });

        const fastLoginData = {
          token: getSsoToken(),
          userName: result.user.userName,
          name: result.user.nickName,
          telephone: result.user.phonenumber,
          tenantId: result.user.tenantId,
          tenantName: result.user.tenantName,
          contactUserName: result.user.tenantContactUserName,
          contactPhone: result.user.tenantContactPhone,
          licenseNumber: result.user.tenantLicenseNumber,
          address: result.user.tenantAddress,
          domain: result.user.tenantDomain,
          roleKey: roleKey,
          method: localStorage.getItem('method') || ''
        };
        const fastLoginRes = await fastLogin(fastLoginData).catch((e) =>
          console.error('fastLoginRes===>', e)
        );
        result.user = { ...result.user, ...fastLoginRes?.data };
      }

      // 用户信息
      this.setInfo(result.user);
      // 用户权限
      this.authorities = result.permissions;
      // 用户角色
      this.roles = result.roles;
      // 是否是机构
      this.isSite = result.roles.some((role) => role.startsWith('site'));
      // 用户菜单
      const userMenu = await getUserMenu().catch((e) => console.error(e));
      var userMenu2;
      if (this.roles[0] == 'superadmin') {
        userMenu2 = this.filterVolunteerScreeningMenus(userMenu);
      } else {
        userMenu2 = userMenu;
      }
      if (!userMenu2) {
        return {};
      }
      let { menus, homePath } = formatMenus(userMenu2);
      // menus = [menus[0]] // 临时添加
      this.setMenus(menus);
      return { menus, homePath };
    },
    filterVolunteerScreeningMenus(menus) {
      return menus.filter((menu) => {
        if (!menu.meta?.title?.includes('志愿者筛查')) {
          return true;
        }
        if (menu.children && menu.children.length > 0) {
          const filteredChildren = this.filterVolunteerScreeningMenus(
            menu.children
          );
          if (filteredChildren.length > 0) {
            return { ...menu, children: filteredChildren };
          }
        }
        return false;
      });
    },
    /**
     * 更新用户信息
     */
    setInfo(value) {
      this.info = value;
    },
    /**
     * 更新菜单数据
     */
    setMenus(menus) {
      this.menus = menus;
    },
    /**
     * 更新菜单的徽章
     * @param path 菜单地址
     * @param value 徽章值
     * @param type 徽章类型
     */
    setMenuBadge(path, value, type) {
      this.menus = mapTree(this.menus, (m) => {
        if (path === m.path) {
          const meta = m.meta || {};
          return {
            ...m,
            meta: {
              ...meta,
              props: {
                ...meta.props,
                badge: value == null ? void 0 : { value, type }
              }
            }
          };
        }
        return m;
      });
    },
    /**
     * 更新字典数据
     */
    setDicts(value, code) {
      if (code == null) {
        this.dicts = value;
        return;
      }
      this.dicts[code] = value;
    }
  }
});

/**
 * 菜单数据处理为EleProLayout所需要的格式
 * @param data 菜单数据
 * @param childField 子级的字段名称
 */
// function formatMenus(data, childField = 'children') {
//   let homePath;
//   let homeTitle;
//   const menus = mapTree(
//     data,
//     (item) => {
//       const meta =
//         typeof item.meta === 'string'
//           ? JSON.parse(item.meta || '{}')
//           : item.meta;
//       const menu = {
//         path: item.path,
//         component: item.component,
//         meta: { title: item.title, icon: item.icon, hide: !!item.hide, ...meta }
//       };
//       const children = item[childField]
//         ? item[childField].filter((d) => !(d.meta?.hide ?? d.hide))
//         : void 0;
//       if (!children?.length) {
//         if (!homePath && menu.path && !isExternalLink(menu.path)) {
//           homePath = menu.path;
//           homeTitle = menu.meta?.title;
//         }
//       } else {
//         const childPath = children[0].path;
//         if (childPath) {
//           if (!menu.redirect) {
//             menu.redirect = childPath;
//           }
//           if (!menu.path) {
//             menu.path = childPath.substring(0, childPath.lastIndexOf('/'));
//           }
//         }
//       }
//       if (!menu.path) {
//         console.error('菜单path不能为空且要唯一:', item);
//         return;
//       }
//       return menu;
//     },
//     childField
//   );
//   return { menus, homePath, homeTitle };
// }

function formatMenus(data, childField = 'children') {
  let homePath;
  let homeTitle;
  const menus = mapTree(
    data,
    (item, _index, parent) => {
      const meta = item.meta;
      const { path, rPath } = formatPath(item.path, parent?.path, item.query);
      const menu = {
        path: path,
        component: formatComponent(item.component),
        meta: {
          hide: !!item.hidden,
          keepAlive: !meta.noCache,
          routePath: rPath,
          ...meta
        }
      };
      const children = item[childField]
        ? item[childField].filter((d) => !(d.meta?.hide ?? d.hide))
        : void 0;
      if (!children?.length) {
        if (!homePath && menu.path && !isExternalLink(menu.path)) {
          homePath = menu.path;
          homeTitle = menu.meta?.title;
        }
      } else {
        const childPath = children[0].path;
        if (childPath) {
          if (!menu.redirect) {
            menu.redirect = childPath;
          }
          if (!menu.path) {
            menu.path = childPath.substring(0, childPath.lastIndexOf('/'));
          }
        }
      }
      if (!menu.path) {
        console.error('菜单path不能为空且要唯一:', item);
        return;
      }
      return menu;
    },
    childField
  );
  return { menus, homePath, homeTitle };
}

/**
 * 组件路径处理以兼容若依默认数据
 * @param component 组件路径
 */
function formatComponent(component) {
  if (!component || component === 'Layout') {
    return;
  }
  if (isExternalLink(component)) {
    return component;
  }
  return component.startsWith('/') ? component : `/${component}`;
}

/**
 * 菜单地址处理以兼容若依
 * @param mPath 菜单地址
 * @param pPath 父级菜单地址
 * @param query 路由参数
 */
function formatPath(mPath, pPath, query) {
  if (!mPath || isExternalLink(mPath)) {
    return { path: mPath };
  }
  const path = !pPath || mPath.startsWith('/') ? mPath : `${pPath}/${mPath}`;
  if (query) {
    try {
      const params = new URLSearchParams(JSON.parse(query)).toString();
      if (params) {
        return { path: `${path}?${params}`, rPath: path };
      }
    } catch (e) {
      console.error(e);
    }
  }
  return { path };
}
