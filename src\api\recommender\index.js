import request from '@/utils/request-business';

/**
 * 分页查询推荐官申请记录
 * @param {*} data userId
 * @returns
 */
export async function recommendApplyPage(params) {
  const res = await request.get('/recommend/recommendApplyPage', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 审核推荐官申请
 * @param {*} data id
 * @returns
 */
export async function auditRecommendApply(data) {
  const res = await request.post(`/recommend/auditRecommendApply`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 编辑推荐官申请
 * @param {*} data id
 * @returns
 */
export async function updateRecommendApply(data) {
  const res = await request.post(`/recommend/updateRecommendApply`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}
