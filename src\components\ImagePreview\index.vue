<template>
  <el-image-viewer
    v-if="show"
    v-bind="$attrs"
    hide-on-click-modal
    @close="show = false"
  />
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { ElImageViewer } from 'element-plus'; //自定义函数组件无法使用全局组件，需要单独引入

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    remove: {
      type: Function, //传入createApp中移除节点的方法
      default: null
    }
  });

  const show = ref(props.visible);
  // 监听显示的消失，需要移除dom
  watch(
    () => show.value,
    (val) => {
      !val && props.remove();
    }
  );
</script>
