import { createApp } from 'vue';
import App from './App.vue';
import store from './store';
import router from './router';
import permission from './utils/permission';
import DictData from '@/components/DictData/index.vue';
import i18n from './i18n';
import installer from './as-needed';
import { initBrowserDetector } from './utils/browser-detector';
import 'element-plus/theme-chalk/display.css';
import '@hnjing/zxzy-admin-plus/es/style/nprogress.scss';
import './styles/themes/rounded.scss';
import './styles/themes/dark.scss';
import './styles/index.scss';

const app = createApp(App);

// 挂载全局变量
// app.config.globalProperties.$baseImg = import.meta.env.VITE_APP_BASE_IMG;
// app.config.globalProperties.$baseApiImg = import.meta.env.VITE_APP_BASE_API_URL;
// loginEnvironmentConfig().then((res) => {
//   if (res.code == 200) {
//     console.log(res);
//     // app.config.globalProperties.$baseImg = res.ossUrl;
//     // app.config.globalProperties.$baseApiImg = res.apiUrl;
//     app.config.globalProperties.$apiActive = res.apiActive;
//     app.config.globalProperties.$baseImg = res.ossUrl + '/';
//     app.config.globalProperties.$baseApiImg = res.apiUrl + '/';
//     localStorage.setItem('baseApiImg', res.apiUrl + '/');
//   }
// });

app.use(store);
app.use(router);
app.use(permission);
app.use(i18n);
app.use(installer);
app.component('DictData', DictData);

app.mount('#app');

// 初始化浏览器检测
initBrowserDetector();

app.config.globalProperties.$PhoneOrIdCrad = function (text, type) {
  if (!text || !type) return text;
  text = text.trim(); // 去掉左右空格
  if (text.includes('*')) return text; // *已脱敏的不处理
  if (text.includes(' ')) return text; // -不处理

  try {
    const lowerType = String(type).toLowerCase(); // 转换为小写比较
    if (lowerType === 'phone') {
      // 手机号格式化为 XXX-XXXX-XXXX
      return text.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
    } else if (lowerType === 'idcard') {
      // 身份证号格式化为 XXXXXX-XXXXXXXX-XXXX
      return text.replace(/(\d{6})(\d{8})([0-9Xx]{4})/, '$1 $2 $3');
    }
    return text;
  } catch (e) {
    console.error('格式化出错:', e);
    return text;
  }
};
