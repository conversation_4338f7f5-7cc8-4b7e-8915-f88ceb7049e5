import request from '@/utils/subjectsrequest';
import { download, toFormData } from '@/utils/common';
import dayjs from 'dayjs';
let unique = 0;

function uuid(prefix) {
  const time = Date.now();
  const random = Math.floor(Math.random() * 1000000000);
  unique++;
  return prefix + '_' + random + unique + String(time);
}
/**
 * 查询试验信息列表
 */
export async function trialsProjectList(data) {
  const res = await request.post(`/sys/trials/list`, data);
  if (res.data.code === 200) {
    res.data.data.list.forEach((item) => {
      item.uuid = uuid('uid');
    });
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出试验项目
 */
export async function exportProject(params) {
  const res = await request({
    url: '/sys/trials/export',
    method: 'POST',
    data: params,
    responseType: 'blob'
  });
  download(
    res.data,
    `试验项目_${dayjs(new Date()).format('YYYYMMDDHHMMssSSS')}.xlsx`
  );
}

/**
 * 保存试验信息
 */
export async function trialsSave(data) {
  const res = await request.post(`/sys/trials/save`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 修改试验信息
 */
export async function trialsUpdate(data) {
  const res = await request.post(`/sys/trials/update`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 查询试验信息
 */
export async function trialsProjectInfo(guid) {
  const res = await request.get(`/sys/trials/info/${guid}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 标记结题
 */
export async function trialsMarkConclusion(guid) {
  const res = await request.get(`/sys/trials/markConclusion/${guid}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 查询申办方信息列表
 */
export async function sponsorList(data) {
  const res = await request.post(`/sys/sponsor/list`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 查询本机构专业
 */
export async function professionalList(data) {
  const res = await request.get(`/sys/trials/getProfessional`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
