<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 16px 0px 16px', overflow: 'hidden' }">
    <el-form label-width="100px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="页面名称">
            <el-input clearable v-model.trim="form.name" placeholder="请输入" />
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="是否登录">
            <dict-data
              code="whether_an_employee"
              v-model="form.isLogin"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="用户来源">
            <el-input
              clearable
              v-model.trim="form.resource"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="类型">
            <dict-data
              code="operation_type"
              v-model="form.type"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>

        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="时间范围">
            <el-date-picker
              unlink-panels
              type="daterange"
              v-model="dateRange"
              range-separator="-"
              value-format="YYYY-MM-DD"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetForm] = useFormData({
    name: '',
    resource: '',
    isLogin: null,
    type: null,
    dateRange: ['', '']
  });

  /** 日期范围 */
  const dateRange = ref(['', '']);

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value || [];
    emit('search', {
      ...form,
      startDate: d1,
      endDate: d2
    });
  };

  /** 重置表单数据 */
  const resetFields = () => {
    resetForm();
    dateRange.value = ['', ''];
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };

  defineExpose({ resetFields });
</script>
