<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="680"
    position="center"
    :model-value="modelValue"
    :body-style="{ paddingLeft: '0px' }"
    title="编辑"
    @update:modelValue="updateModelValue"
  >
    <el-form ref="formRef" :model="form" label-width="108px">
      <el-form-item label="电话" prop="telephone">
        <el-input
          v-model="form.telephone"
          clearable
          :maxlength="20"
          placeholder="请输入电话"
        />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model="form.email"
          clearable
          type="email"
          placeholder="请输入机构邮箱"
        />
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input
          v-model="form.address"
          clearable
          maxlength="99"
          placeholder="请输入机构地址"
        />
      </el-form-item>
      <el-form-item label="门头照" prop="file">
        <ele-upload-list
          v-model="form.file"
          :limit="1"
          :drag="true"
          accept="image/png,image/jpeg"
          @upload="onUpload"
          @remove="onRemove"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import FileUpload from '@/components/FileUpload/index.vue';
  import { ref, watch } from 'vue';
  import { updateBasicInfo } from '@/api/site';
  import { uploadFile } from '@/api/system/file';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object
  });

  // 提交状态
  const loading = ref(false);

  // 表单实例
  const formRef = ref(null);

  // 表单数据
  const { form, resetFields, assignFields } = useFormData({
    telephone: '',
    email: '',
    address: '',
    file: [],
    ossId: null
  });

  /* 上传事件 */
  const onUpload = (v) => {
    uploadFile(v.file, {
      onUploadProgress: (e) => {
        console.log('onUploadProgress', e);
        assignFields({
          ...form,
          file: [
            {
              key: 1,
              name: `${v.name}`,
              status: 'uploading',
              progress: (e.loaded / e.total) * 100
            }
          ]
        });
      }
    })
      .then((data) => {
        console.log('#', data);
        assignFields({
          ...form,
          file: [
            {
              key: 1,
              name: data?.name,
              status: 'done',
              url: data?.url
            }
          ],
          ossId: data?.ossId
        });
      })
      .catch(() => {
        EleMessage.error('上传文件失败，请稍后重试');
        assignFields({
          ...form,
          file: [
            {
              key: 1,
              name: `${v.name}`,
              status: 'exception'
            }
          ]
        });
      });
  };

  /* 删除事件 */
  const onRemove = () => {
    assignFields({ ...form, file: [], ossId: null });
  };

  /* 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      try {
        const doorOssId = form?.ossId;
        const params = { ...form, doorOssId: doorOssId };
        await updateBasicInfo(params);
        loading.value = false;
        EleMessage.success('修改成功');
        updateModelValue(false);
        emit('done');
      } catch (e) {
        loading.value = false;
        EleMessage.error(e?.message);
      }
    });
  };

  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      // 打开面板进行赋值
      if (modelValue) {
        const data = props.data ?? {};
        const formValues = {
          telephone: data.telephone,
          email: data.email,
          address: data.address,
          file: []
        };
        if (data.doorOssId) {
          const fileObject = {
            key: 1,
            name: data?.doorFileName,
            url: data?.doorUrl,
            ossId: data?.doorOssId
          };
          formValues.file = [fileObject];
        }
        assignFields(formValues);
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .role-menu-tree {
    box-sizing: border-box;
    width: 100%;
    padding: 6px 0;
    overflow: hidden;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
  }
</style>
