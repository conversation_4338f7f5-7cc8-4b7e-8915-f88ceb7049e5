<template>
  <ele-page :flex-table="true" hide-footer>
    <circle-search ref="searchRef" @search="reload" />
    <ele-card :flex-table="true" :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="postId"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        highlight-current-row
        :footer-style="{ paddingBottom: '16px' }"
        cache-key="circleDataTable"
      >
        <template #status="{ row }"
          ><dict-data code="circle_status" type="tag" v-model="row.status" />
        </template>

        <template #firstAuditStatus="{ row }"
          ><dict-data
            code="circle_review_status"
            type="tag"
            v-model="row.firstAuditStatus"
          />
        </template>

        <template #secondAuditStatus="{ row }"
          ><dict-data
            code="circle_recheck_status"
            type="tag"
            v-model="row.secondAuditStatus"
          />
        </template>

        <template #action="{ row }">
          <el-link
            v-if="row.status == 0"
            v-permission="'circle:manage:view'"
            type="primary"
            :underline="false"
            @click="toAuditDialog(row, 1)"
          >
            初审
          </el-link>
          <el-link
            v-if="row.status == 1"
            v-permission="'circle:manage:review'"
            type="primary"
            :underline="false"
            @click="toAuditDialog(row, 2)"
          >
            复审
          </el-link>
          <el-divider v-if="[0, 1].includes(row.status)" direction="vertical" />
          <el-link
            v-if="row.offFlag == 1"
            v-permission="'circle:manage:online'"
            type="primary"
            :underline="false"
            @click="offLine(row)"
          >
            上架
          </el-link>
          <el-link
            v-if="row.offFlag == 0"
            v-permission="'circle:manage:offline'"
            type="primary"
            :underline="false"
            @click="offLine(row)"
          >
            下架
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            type="primary"
            :underline="false"
            @click="showCircleDetails(row)"
          >
            详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <AuditModal
      v-model="showAuditDialog"
      :isFirstAudit="isFirstAudit"
      :currnetID="currnetID"
      :currentData="currentData"
      @done="reload"
    />
    <CircleDetailsModal
      v-if="showCircleDetailsDialog"
      v-model="showCircleDetailsDialog"
      :currnetID="currnetID"
      :currentData="currentData"
      @done="reload"
    />
  </ele-page>
</template>

<script setup name=" CircleManage">
  import { ref, watch } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import CircleSearch from '../components/circle-search.vue';
  import AuditModal from '../components/AuditModal.vue';
  import CircleDetailsModal from '../components/CircleDetailsModal.vue';

  import { getManagerPageList, postOffLine } from '@/api/circle/index';

  const props = defineProps({
    /** 字典类型 */
    dictType: String
  });

  const currnetID = ref('');

  const currentData = ref({});

  /** 搜索栏实例 */
  const searchRef = ref(null);

  const isFirstAudit = ref(false);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center'
    },
    {
      prop: 'title',
      label: '标题',
      align: 'center',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      prop: 'userName',
      label: '发帖人',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'status',
      label: '帖子状态',
      width: 160,
      align: 'center',
      slot: 'status'
    },
    {
      prop: 'offFlag',
      label: '下架标识',
      minWidth: 110,
      align: 'center',
      formatter: (row) => (row.offFlag == 1 ? '是' : '否')
    },
    {
      prop: 'firstAuditStatus',
      label: '初审状态',
      minWidth: 110,
      align: 'center',
      slot: 'firstAuditStatus'
    },
    {
      prop: 'secondAuditStatus',
      label: '复审状态',
      minWidth: 110,
      align: 'center',
      slot: 'secondAuditStatus'
    },
    {
      prop: 'commentCount',
      label: '评论数',
      minWidth: 110,
      align: 'center'
    },
    {
      prop: 'likeCount',
      label: '点赞数',
      minWidth: 110,
      align: 'center',
      showOverflowTooltip: true
    },
    {
      prop: 'rejectReason',
      label: '拒绝原因',
      minWidth: 110,
      align: 'center',
      showOverflowTooltip: true
    },
    {
      prop: 'createTime',
      label: '发帖时间',
      minWidth: 180,
      align: 'center'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 200,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  /** 是否显示审核弹窗 */
  const showAuditDialog = ref(false);

  const showCircleDetailsDialog = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders }) => {
    return getManagerPageList({
      ...where,
      ...orders,
      ...pages
    });
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  const offLine = async (row) => {
    const title =
      row?.offFlag == 0
        ? `确认是否下架 ‘${row?.title}’ 帖子吗？`
        : `确认是否上架 ‘${row?.title}’ 帖子吗？`;
    try {
      await ElMessageBox.confirm(title, '提示', {
        type: 'warning',
        draggable: true,
        customStyle: {
          maxWidth: '600px'
        }
      });
      // const params = {
      //   postId: row?.postId,
      //   offFlag: row?.offFlag == 0 ? 1 : 0
      // };
      const formData = new FormData();
      formData.append('postId', row?.postId);
      formData.append('offFlag', row?.offFlag == 0 ? 1 : 0);
      await postOffLine(formData);
      EleMessage.success('操作成功');
      reload();
    } catch (e) {
      if (e != 'cancel') {
        EleMessage.error(e?.message);
      }
    }
  };

  const showCircleDetails = (row) => {
    currnetID.value = row?.postId ?? null;
    Object.assign(currentData.value, row);
    showCircleDetailsDialog.value = true;
  };

  const toAuditDialog = (row, audit) => {
    showAuditDialog.value = true;
    currnetID.value = row?.postId + '' ?? null;
    isFirstAudit.value = audit == 1 ? true : false;
  };
</script>
