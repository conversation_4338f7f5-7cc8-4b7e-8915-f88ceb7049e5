import { getToken } from '@/utils/token-util';
import { ElNotification } from 'element-plus';
import { useNoticeStore } from '@/utils/notice';
import { watch } from 'vue'; // 确保引入 watch
import { CLIENT_ID } from '@/config/setting';
import { useEventSource } from '@vueuse/core';

// 初始化

let sseConnected = false;
export const initSSE = (baseUrl, onDataReceived) => {
  if (import.meta.env.VITE_APP_SSE === 'false') {
    return;
  }

  // 检查是否已经初始化过
  if (sseConnected) {
    console.warn('SSE 已经初始化，忽略重复初始化请求');
    return;
  }

  let url = `${baseUrl}?Authorization=${getToken()}&clientid=${CLIENT_ID}`;
  const { data, error } = useEventSource(url, [], {
    autoReconnect: {
      retries: 10,
      delay: 3000,
      onFailed() {
        console.log('Failed to connect after 10 retries');
        sseConnected = false;
      }
    }
  });

  watch(error, () => {
    console.log('SSE connection error:', error.value);
    sseConnected = false;
    error.value = null;
  });

  watch(data, () => {
    if (!data.value) return;

    // 调用回调函数并传递数据
    onDataReceived(data.value);

    useNoticeStore().addNotice({
      message: data.value,
      read: false,
      time: new Date().toLocaleString()
    });
    let mesObj = null;
    if (data.value) {
      mesObj = JSON.parse(data.value);
    }
    ElNotification({
      title: mesObj?.sessionName,
      message: mesObj?.content,
      type: 'success',
      duration: 10000
    });
    data.value = null;
    sseConnected = true;
  });
  sseConnected = true;
};

// 提供一个函数来获取当前的 SSE 状态
export const getSSEStatus = () => {
  return {
    connected: sseConnected
  };
};
