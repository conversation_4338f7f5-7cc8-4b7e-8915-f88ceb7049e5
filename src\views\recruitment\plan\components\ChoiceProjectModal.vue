<!-- 修改密码弹窗 -->
<template>
  <ele-modal
    v-model="visible"
    form
    :width="1200"
    title="选择项目"
    :append-to-body="true"
    :body-style="{ paddingTop: '6px' }"
    @closed="handleClosed"
  >
    <el-tabs v-model="currentStatus" class="demo-tabs" @tab-click="changeTab">
      <el-tab-pane label="招募中" name="coming" />
      <el-tab-pane label="已关闭" name="closed" />
    </el-tabs>
    <div class="tableForm">
      <el-form inline :model="formSearch">
        <el-form-item label="关键字：">
          <el-input
            v-model="formSearch.keywords"
            placeholder="请输入关键字"
            clearable
            style="width: 200px"
            maxlength="99"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="reload">搜索</el-button>
          <el-button type="info" @click="(formSearch.keywords = ''), reload()"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <div>
        <!-- 数据列表 -->
        <ele-loading
          v-if="data.length > 0"
          :loading="loading"
          class="main-card"
        >
          <div v-for="item in data" :key="item.projectId" class="card">
            <div class="list-item" @click="toChange(item)">
              <div class="list-item-avatar">
                <div class="list-item-avatar-extra">
                  <el-tooltip
                    v-if="item.projectRecruitName.length > 30"
                    class="item-tooltip"
                    effect="dark"
                    :content="item.projectRecruitName"
                    placement="top"
                  >
                    <div style="margin-bottom: 2px; margin-left: -8px">
                      <span :style="getTextColor(item?.projectRecruitType)">{{
                        item?.projectRecruitType == 1 ? '【健康】' : '【患者】'
                      }}</span>
                      {{ item?.projectRecruitName.substring(0, 30) }}...
                    </div>
                  </el-tooltip>
                  <div v-else style="margin-bottom: 2px; margin-left: -8px">
                    <span :style="getTextColor(item?.projectRecruitType)">{{
                      item?.projectRecruitType == 1 ? '【健康】' : '【患者】'
                    }}</span>
                    {{ item?.projectRecruitName }}
                  </div>
                  <el-row>
                    <el-tooltip
                      v-if="item.indications.length > 30"
                      class="item-tooltip"
                      effect="dark"
                      :content="item.indications"
                      placement="top"
                    >
                      <el-col class="ellipsis t-title" :span="24">
                        适应症：{{ item?.indications.substring(0, 30) }}...
                      </el-col>
                    </el-tooltip>
                    <el-col class="t-title" v-else :span="24">
                      适应症：{{ item?.indications }}
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col class="t-title" :span="8"
                      >适应用药：{{ item.drugName }}</el-col
                    >
                    <el-col class="ellipsis t-title" :span="8"
                      >招募中心：{{
                        item?.projectRecruitType == 2
                          ? item.siteNumber
                          : item.siteNames
                      }}</el-col
                    >
                    <el-col class="t-title" :span="8"
                      >招募截止：{{ item.projectRecruitDeadline }}
                    </el-col>
                    <!-- <el-col class="t-title" :span="8"
                    >推荐奖励：{{ item.referralReward }}</el-col
                  >
                  <el-col class="t-title" :span="8"
                    >试验补助：{{ getSubsidyAmount(item) }}</el-col
                  > -->
                  </el-row>
                </div>
                <!-- <div>
                <span :style="getTextColor(item?.projectRecruitType)">{{
                  item?.projectRecruitType == 1 ? '【健康】' : '【患者】'
                }}</span>
                {{ item?.projectRecruitName }}
              </div> -->
              </div>
              <div class="list-item-owner">
                {{ item?.applyAudiNumber }}
                <div class="list-item-title">报名审核</div>
              </div>
              <div class="list-item-owner">
                {{ item?.applyNumber }}
                <div class="list-item-title">已报名</div>
              </div>
              <div class="list-item-owner">
                {{
                  item?.joinGroupNumber != 0 && item?.joinGroupNumber
                    ? item?.joinGroupNumber +
                      '/' +
                      (item.planJoinGroupTotalPeople || '未设置')
                    : 0
                }}
                <div class="list-item-title">入组进度</div>
              </div>
              <!-- <div class="list-item-progress" style="display: block">
                <ele-dot
                  v-if="item?.recruitStatus == 1"
                  style="color: #f97316; font-size: 16px"
                  text="草稿"
                  type="warning"
                />
                <ele-dot
                  v-if="item?.recruitStatus == 2"
                  style="color: #209373; font-size: 16px"
                  text="招募中"
                  type="success"
                />
                <ele-dot
                  v-if="item?.recruitStatus == 3"
                  style="color: #9ca3af; font-size: 16px"
                  text="审核失败"
                  type="info"
                />
                <ele-dot
                  v-if="item?.recruitStatus == 4"
                  style="color: #9ca3af; font-size: 16px"
                  text="已关闭"
                  type="info"
                />
              </div> -->
              <div class="list-item-tools">
                <el-link
                  v-if="item.projectId != props.projectId"
                  type="primary"
                  :underline="false"
                  @click.stop="toChange(item)"
                >
                  切换
                </el-link>
                <span style="color: #1677ff" v-else>当前选择</span>
              </div>
            </div>
          </div>
        </ele-loading>
        <el-empty v-else description="暂无数据" />
      </div>
    </div>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { getOrgPageList } from '@/api/project/index';
  import { ElMessageBox } from 'element-plus/es';

  const currentStatus = ref('coming');

  const formSearch = ref({ status: '1' });

  const emit = defineEmits(['update:modelValue', 'done']);

  const props = defineProps({
    modelValue: Boolean,
    projectId: String,
    model: String
  });

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交loading */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  const centers = ref([]);

  const data = ref([]);
  /** 第几页 */
  const page = ref(1);
  /** 每页多少条 */
  const limit = ref(100);
  /** 总数量 */
  const count = ref(0);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    siteId: null
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    updateModelValue(false);
  };

  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 刷新表格 */
  // const reload = () => {
  //   tableRef.value?.reload?.({ where: params.value });
  // };

  /** 切换操作中兴 */
  const toChange = (row) => {
    emit('done', row.projectId);
    handleCancel();
    // ElMessageBox.confirm(`确定要切换当前项目吗？`, '提示', {
    //   confirmButtonText: '是',
    //   cancelButtonText: '否',
    //   center: true,
    //   customStyle: {
    //     fontSize: '16px'
    //   }
    // }).then(async () => {
    //   try {
    //     emit('done', row.projectId);
    //     handleCancel();
    //   } catch (e) {
    //     EleMessage.error(e);
    //   }
    // });
  };

  const changeTab = (type) => {
    currentStatus.value = type.props.name;
    if (currentStatus.value == 'coming') {
      formSearch.value.status = 1;
    }
    if (currentStatus.value == 'closed') {
      formSearch.value.status = 2;
    }
    reload();
  };

  const getTextColor = (type) => {
    if (type == 1) {
      return { color: '#209373' }; // 健康类型为绿色
    } else {
      return { color: '#F97316' }; // 患者类型为蓝色
    }
  };

  /**
   * 获取试验补助
   * @param {Object} data - 包含补助信息的对象
   * @returns {number | string} - 返回相应的补助金额
   */
  const getSubsidyAmount = (data) => {
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid data');
    }
    const {
      trialSubsidyType,
      subsidyAmount,
      maleSubsidyAmount,
      femaleSubsidyAmount
    } = data;
    switch (trialSubsidyType) {
      case '1':
        return subsidyAmount + '元'; // 当 trialSubsidyType 为 1 时，返回 subsidyAmount
      case '2':
        return `男：${maleSubsidyAmount} 元，女：${femaleSubsidyAmount} 元`; // 当 trialSubsidyType 为 2 时，返回男和女的补助金额
      default:
        return null; // 处理其他类型或未定义的情况
    }
  };

  /** 弹窗关闭事件 */
  const handleClosed = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    loading.value = false;
  };

  const fetchData = async () => {
    loading.value = true;
    try {
      let response;
      if (props.model == 1) {
        response = await getOrgPageList({
          recruitName: formSearch.value.keywords,
          recruitStatus: currentStatus.value == 'coming' ? 2 : 4,
          recruitType: props.recruitType || '',
          page: page.value,
          limit: limit.value
        });
      } else {
      }
      data.value = response.records;
      count.value = response.total;
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const reload = () => {
    fetchData();
  };

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        fetchData();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .ellipsis {
    display: -webkit-box; /* 使用 flexbox 布局 */
    -webkit-box-orient: vertical; /* 垂直排列子元素 */
    -webkit-line-clamp: 1; /* 显示的行数 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
    max-width: 600px;
  }
  .main-card {
    height: 466px;
    overflow: auto;
  }
  .card {
    background: #ffffff;
    box-shadow: 0px 4px 6px 0px #e5e7eb;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #ecf2f9;
    margin-bottom: 10px;
    transition: border 0.3s;
    cursor: pointer; /* 更改鼠标指针为手形 */
    &:hover {
      border-color: #0075ff;
    }
  }
  /* 列表样式 */
  .list-item {
    display: flex;
    align-items: center;
    padding: 16px 8px;
    .list-item-avatar {
      flex: 1;
      display: flex;
      align-items: center;
      :deep(.el-avatar) {
        flex-shrink: 0;
      }
      .list-item-avatar-extra {
        flex: 1;
        padding-left: 12px;
        box-sizing: border-box;
      }
    }
    & > div + div {
      margin-left: 20px;
      flex-shrink: 0;
    }
    .list-item-owner {
      width: 80px;
      color: #5b5b64;
      font-size: 16px;
    }
    .list-item-time {
      width: 160px;
    }
    .list-item-progress {
      width: 80px;
    }
    .list-item-title {
      margin-bottom: 4px;
    }
    .list-item-tools {
      display: flex;
      align-items: center;
    }
  }
  /* 响应式 */
  @media screen and (max-width: 1340px) {
    .list-item {
      & > div + div {
        margin-left: 10px;
      }
      .list-item-owner {
        width: 70px;
        color: #5b5b64;
        font-size: 16px;
      }
      .list-item-time {
        width: 140px;
      }
      .list-item-progress {
        width: 100px;
      }
    }
  }
  @media screen and (max-width: 1100px) {
    .list-item {
      display: block;
      .list-item-owner,
      .list-item-time,
      .list-item-progress {
        width: 100%;
        margin: 8px 0 0 0;
        display: flex;
        align-items: center;
      }
      .list-item-title {
        margin: 0;
        width: 80px;
      }
      .list-item-tools {
        margin-top: 8px;
        justify-content: flex-end;
      }
    }
  }

  :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
  }

  .t-title {
    color: #5b5b64;
    font-size: 15px;
  }

  .tt-title {
    color: #5b5b64;
    font-size: 16px;
  }
</style>
