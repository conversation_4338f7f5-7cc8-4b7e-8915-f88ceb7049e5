export default [
  {
    path: '/index',
    component: '/workplace/index',
    meta: { title: '工作台', icon: 'HomePage' }
  },
  {
    path: '/icon-demo',
    component: '/icon-demo',
    meta: {
      title: 'icon示例',
      icon: 'ApprovalManagement',
      hide: false,
      lang: {
        zh_TW: 'icon示例',
        en: 'icon Demo'
      }
    }
  },
  {
    path: '/table',
    meta: {
      title: '列表页面',
      icon: 'SystemMonitoring',
      hide: false,
      props: {
        hideTimeout: 450
      },
      lang: {
        zh_TW: '清單頁面',
        en: 'List'
      }
    },
    children: [
      {
        path: '/table/normal-table',
        component: '/table/normal-table',
        meta: {
          title: '基础表格',
          icon: 'TableOutlined',
          hide: false,
          lang: {
            zh_TW: '基礎表格',
            en: 'Basic Table'
          }
        }
      },
      {
        path: '/table/normal-table-demo',
        component: '/table/normal-table-demo',
        meta: {
          title: '表格示例',
          icon: 'DatabaseOutlined',
          hide: false,
          lang: {
            zh_TW: '表格示例',
            en: 'Table Demo'
          }
        }
      }
    ]
  },
  {
    path: '/result',
    meta: {
      title: '结果页面',
      icon: 'SystemTool',
      hide: false,
      lang: {
        zh_TW: '結果頁面',
        en: 'Result'
      }
    },
    children: [
      {
        path: '/result/success',
        component: '/result/success',
        meta: {
          title: '成功页',
          icon: 'IconProLinkOutlined',
          hide: false,
          lang: {
            zh_TW: '成功頁',
            en: 'Success'
          }
        }
      },
      {
        path: '/result/fail',
        component: '/result/fail',
        meta: {
          title: '失败页',
          icon: 'IconProLinkOutlined',
          hide: false,
          lang: {
            zh_TW: '失敗頁',
            en: 'Fail'
          }
        }
      }
    ]
  },
  {
    path: '/exception',
    meta: {
      title: '异常页面',
      icon: 'ProjectManagement',
      hide: false,
      lang: {
        zh_TW: '异常頁面',
        en: 'Exception'
      }
    },
    redirect: '/exception/403',
    children: [
      {
        path: '/exception/403',
        component: '/exception/403',
        meta: {
          title: '403',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/exception/404',
        component: '/exception/404',
        meta: {
          title: '404',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      },
      {
        path: '/exception/500',
        component: '/exception/500',
        meta: {
          title: '500',
          icon: 'IconProLinkOutlined',
          hide: false
        }
      }
    ]
  },
  {
    path: '/system',
    meta: {
      hide: false,
      keepAlive: true,
      title: '系统管理',
      icon: 'SettingOutlined',
      noCache: false,
      link: null
    },
    redirect: 'user',
    children: [
      {
        path: '/system/user',
        component: '/system/user/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '用户管理',
          icon: 'UserOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/role',
        component: '/system/role/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '角色管理',
          icon: 'IdcardOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/menu',
        component: '/system/menu/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '菜单管理',
          icon: 'AppstoreOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/dept',
        component: '/system/dept/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '部门管理',
          icon: 'CityOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/post',
        component: '/system/post/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '岗位管理',
          icon: 'SuitcaseOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/dict',
        component: '/system/dict/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '字典管理',
          icon: 'BookOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/config',
        component: '/system/config/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '参数设置',
          icon: 'ControlOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/notice',
        component: '/system/notice/index',
        meta: {
          hide: false,
          keepAlive: true,
          title: '通知公告',
          icon: 'MessageOutlined',
          noCache: false,
          link: null
        }
      },
      {
        path: '/system/log',
        component: '/ParentView',
        meta: {
          hide: false,
          keepAlive: true,
          title: '日志管理',
          icon: 'LogOutlined',
          noCache: false,
          link: null
        },
        redirect: 'operlog',
        children: [
          {
            path: '/system/log/operlog',
            component: '/monitor/operlog/index',
            meta: {
              hide: false,
              keepAlive: true,
              title: '操作日志',
              icon: 'FileOutlined',
              noCache: false,
              link: null
            }
          },
          {
            path: '/system/log/logininfor',
            component: '/monitor/logininfor/index',
            meta: {
              hide: false,
              keepAlive: true,
              title: '登录日志',
              icon: 'CalendarOutlined',
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  }
];
