<template>
  <ele-card
    flex-table
    :body-style="{ padding: 0 }"
    style="height: calc(100vh - 81px - 14px); background: transparent"
  >
    <ele-card
      flex-table
      :body-style="{ padding: '4px 20px 20px 20px', overflow: 'hidden' }"
    >
      <ele-pro-table
        ref="tableRef"
        row-key="testListuuid"
        :columns="columns"
        :datasource="datasource"
        :toolbar="true"
        :loadOnCreated="false"
        highlight-current-row
        :bottom-line="tableFullHeight"
        cache-key="testListTable18"
        v-model:selections="selectionsList"
      >
        <template #toolbar>
          <div class="tableForm">
            <el-form inline :model="params">
              <el-form-item label="姓名：">
                <el-input
                  v-model="params.name"
                  placeholder="请输入完整的姓名"
                  clearable
                  style="width: 200px"
                  maxlength="99"
                />
              </el-form-item>
              <el-form-item label="身份证号：">
                <el-input
                  v-model="params.idcard"
                  placeholder="请输入完整的身份证号"
                  clearable
                  style="width: 200px"
                  maxlength="99"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" plain @click="reload('load')"
                  >搜索</el-button
                >
                <el-button type="info" @click="(params = {}), reload('reset')"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </template>
        <template #tools>
          <!-- <span style="margin-right: 10px">显示模式</span> -->

          <div class="ele-tool" @click="sendExport">
            <img src="@/assets/exportIcon.svg" alt="" style="width: 19px" />
          </div>
        </template>
        <template #idcard="{ row }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1">{{
              $PhoneOrIdCrad(row.idcard, 'idCard')
            }}</div>
            <img
              src="@/assets/eyeOpen.png"
              style="
                transform: scale(1.28);
                cursor: pointer;
                margin: 0 5px;
                width: 14px;
                height: 14px;
              "
              v-if="row.showEye"
              @click="getShowEye(row)"
            />
            <img
              src="@/assets/eyeClose.png"
              style="
                transform: scale(1.28);
                cursor: pointer;
                margin: 0 5px;
                width: 14px;
                height: 14px;
              "
              v-if="!row.showEye"
              @click="getShowEye(row)"
            />
          </div>
        </template>
        <template #similaritytype="{ row }">
          <span v-if="row.similaritytype == 1"> 高 </span>
          <span v-if="row.similaritytype == 2"> 中 </span>
          <span v-if="row.similaritytype == 3"> 低 </span>
          <span v-if="row.similaritytype2 == 1"> / 高 </span>
          <span v-if="row.similaritytype2 == 2"> / 中 </span>
          <span v-if="row.similaritytype2 == 3"> / 低 </span>
        </template>
        <template #status="{ row }">
          <span style="color: #c72727" v-if="row.status == 0">初查不合格</span>
          <span style="color: #67c32a" v-if="row.status == 1">筛选</span>
          <span style="color: #67c32a" v-if="row.status == 2">入组</span>
          <span style="color: #3ed47b" v-if="row.status == 3">完成</span>
          <span style="color: #999999" v-if="row.status == 4">出组</span>
          <span style="color: #999999" v-if="row.status == 5">未入组</span>
          <span style="color: #999999" v-if="row.status == 6">入组未给药</span>
          <span style="color: #67c32a" v-if="row.status == 7">待入组</span>
          <span style="color: #c72727" v-if="row.status == 8"
            >入组前筛查不合格
          </span>
        </template>
        <template #action="{ row }">
          <el-space>
            <el-link
              type="primary"
              @click="(selectRow = row), (showInfo = true)"
              style="color: #507aff"
              >详情</el-link
            >
          </el-space>
        </template>
      </ele-pro-table>
      <info v-model="showInfo" :data="selectRow" v-if="showInfo" />
      <el-dialog v-model="dialogVisible" title="导出" width="550">
        <el-form label-width="auto">
          <el-form-item label="筛选时间格式：">
            <el-radio-group v-model="exportType">
              <el-radio value="1">yyyy-MM-dd</el-radio>
              <el-radio value="2">yyyy-MM-dd HH:mm:ss</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer flexCenter">
            <el-button type="info" @click="dialogVisible = false"
              >取 消</el-button
            >
            <el-button type="primary" @click="reqExport" :loading="loading">
              {{ loading ? '导出中' : '导 出' }}
            </el-button>
          </div>
        </template>
      </el-dialog>
    </ele-card>
  </ele-card>
</template>
<script>
  export default {
    name: 'TestList'
  };
</script>
<script setup>
  import { ref } from 'vue';
  import {
    joinSubjectList,
    joinExport
  } from '@/api/subjects/testList/index.js';
  import {
    validIdCard
  } from '@/utils/common.js';
  import { getCompleteInfo } from '@/api/subjects/studyManage/index.js';
  import { Icon } from '@iconify/vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import info from './components/info.vue';
  import { useUserStore } from '@/store/modules/user';
  const userStore = useUserStore();
  const params = ref({});
  const selectionsList = ref([]);
  const selectRow = ref({});
  const showInfo = ref(false);
  const dialogVisible = ref(false);
  const loading = ref(false);
  defineProps({
    // 表头背景
    tableHeader: String,
    // 表格高度
    tableFullHeight: Boolean
  });
  const tableRef = ref(null);

  /** 刷新表格 */
  const reload = (type) => {
    if (params.value.idcard) {
      params.value.idcard = params.value.idcard.replace(/\s+/g, '');
      if (!validIdCard(params.value.idcard)) {
        EleMessage.error('请输入完整的身份证号');
        return;
      }
    }
    tableRef.value?.reload?.({ where: { type: type, ...params.value } });
  };
  const exportType = ref('1');
  const sendExport = () => {
    if (!params.value.name && !params.value.idcard) {
      EleMessage.warning('查询条件 姓名、身份证号 至少一项不能为空！');
      return;
    }
    if (params.value.idcard) {
      params.value.idcard = params.value.idcard.replace(/\s+/g, '');
      if (!validIdCard(params.value.idcard)) {
        EleMessage.error('请输入完整的身份证号');
        return;
      }
    }
    dialogVisible.value = true;
  };
  const reqExport = () => {
    loading.value = true;
    joinExport({
      ...params.value,
      pageNumber: tableRef.value.paginationProps.currentPage,
      pageSize: tableRef.value.paginationProps.pageSize,
      organizationGuid: userStore.info.organizationGuid,
      exportType: exportType.value
    }).then(() => {
      loading.value = false;
      dialogVisible.value = false;
    });
  };
  const datasource = async ({ page, limit, where, orders, filters }) => {
    const res = await joinSubjectList({
      ...filters,
      ...where,
      orderBy: orders.sort,
      orderItem: orders.order,
      pageNumber: page,
      organizationGuid: userStore.info.organizationGuid,
      pageSize: limit
    });
    if (res.code == 500) {
      if (where.type == 'reset') {
        return {
          list: [],
          totalCount: 0
        };
      }
      EleMessage.warning(res.msg);
      return;
    }
    return res;
  };

  const columns = ref([
    /*{
      type: 'selection',
      columnKey: 'selection',
      width: 48,
      align: 'left',
      fixed: 'left'
    },*/
    {
      type: 'index',
      columnKey: 'index',
      label: '序号',
      minWidth: 65,
      align: 'center',
      showOverflowTooltip: true,
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '姓名',
      minWidth: 90,
      fixed: 'left',
      showOverflowTooltip: true
    },
    {
      prop: 'idcard',
      label: '身份证号',
      width: 240,
      slot: 'idcard',
      fixed: 'left',
      showOverflowTooltip: true
    },
    {
      prop: 'sex',
      label: '性别',
      minWidth: 90,
      showOverflowTooltip: true
    },
    {
      prop: 'trialsname',
      label: '试验名称',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      prop: 'trialscode',
      label: '试验编号',
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'status',
      label: '试验状态',
      slot: 'status',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'similaritytype',
      label: '(筛选/入组前)证照核对相似度',
      minWidth: 160,
      slot: 'similaritytype',
      showOverflowTooltip: true
    },
    {
      prop: 'filterno',
      label: '筛选号',
      minWidth: 100,
      showOverflowTooltip: true
    },
    {
      prop: 'filterdateStr',
      label: '筛选时间',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'joinfilterdateStr',
      label: '入组前筛选时间',
      minWidth: 120,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'joinno',
      label: '入组号',
      minWidth: 100,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'joindate',
      label: '入组时间',
      minWidth: 120,
      sortable: 'custom',
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'lastdosedate',
      label: '末次给药时间',
      minWidth: 140,
      sortable: 'custom',
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'trialedate',
      label: '末次访视时间',
      minWidth: 140,
      sortable: 'custom',
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'interval',
      label: '建议间隔天数',
      minWidth: 140,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'suggestfilterdate',
      label: '上家机构建议下次筛选日期',
      minWidth: 160,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'markdate',
      label: '标记时间',
      minWidth: 120,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'sysremarks',
      label: '筛查备注 ',
      minWidth: 160,
      showOverflowTooltip: true
    },
    {
      prop: 'remarks',
      label: '备注',
      slot: 'createName14',
      minWidth: 160,
      showOverflowTooltip: true
    },
    {
      columnKey: 'action',
      label: '操作',
      hideInSetting: true,
      width: 80,
      slot: 'action',
      hideInPrint: true,
      hideInExport: true,
      fixed: 'right'
    }
  ]);
  const getShowEye = (row) => {
    row.showEye = !row.showEye;
    getCompleteInfo({ guid: row.subjectsGuid, type: '1' }).then((res) => {
      row.idcard = res.msg;
      row.idcard = getCard(row);
    });
  };
  const getCard = (row) => {
    try {
      if (row.showEye) {
        return row.idcard;
      }
      return row.idcard.replace(/(\d{6})\d+(\d{4})/, '$1********$2');
    } catch (error) {
      console.log(error);
      return row.idcard;
    }
  };
</script>

<style lang="scss" scoped>
  .project {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    padding: 6px 10px;
    color: #517bff;
    background: #f9faff;
    margin-bottom: 6px;
  }
  .selectIndex {
    background: #517bff;
    color: #ffffff;
  }
  .projectName {
    width: 70%;
    height: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: bold;
    font-size: 18px;
    margin-top: 8px;
  }
  .iconHover:hover {
    color: #1677ff;
  }
</style>
<style>
  .left .el-radio-button__inner {
    padding: 8px 23px !important;
  }
  .tableForm {
    margin-top: 8px;
  }

  .el-button:focus-visible {
    outline: none !important;
  }
  .left .el-tabs__nav-wrap::after {
    background-color: transparent;
  }

  .ele-split-panel > .ele-split-panel-wrap > .ele-split-panel-space {
    width: 8px;
  }
  .ele-split-panel > .ele-split-panel-wrap {
    /* width: 208px; */
  }
</style>
