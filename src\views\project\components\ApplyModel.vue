<template>
  <ele-modal
    form
    :width="800"
    title="后台报名"
    :model-value="modelValue"
    :append-to-body="true"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="90px"
      label-position="left"
      @submit.prevent=""
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="姓名" prop="name">
            <el-input
              v-model="form.name"
              :maxlength="100"
              placeholder="请输入姓名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证号" prop="idCard">
            <el-input
              v-model="form.idCard"
              :maxlength="100"
              clearable
              placeholder="请输入身份证号"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input
              v-model="form.phone"
              :maxlength="100"
              placeholder="请输入手机号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="民族" prop="nation">
            <el-select v-model="form.nation" placeholder="请选择民族">
              <el-option
                v-for="item in nationList"
                :key="item.code"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="form.sex" placeholder="请选择性别">
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="居住城市" props="areaData">
        <el-cascader
          v-model="form.areaData"
          :options="areaOptions"
          style="width: 100%"
          @change="onChangeArea"
        />
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="身高" prop="height">
            <el-input
              v-model="form.height"
              :maxlength="100"
              placeholder="请输入身高"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="体重" prop="weight">
            <el-input
              v-model="form.weight"
              :maxlength="100"
              placeholder="请输入体重"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div>
      <el-button type="primary" :loading="loading" @click="search">
        查询
      </el-button>
    </div>
    <div style="margin-top: 14px">
      <el-row v-if="!lodash.isEmpty(isPass)">
        <el-col :span="6">
          性别是否满足：<span style="font-size: 18px; color: red"
            >{{ isPass.sexTest ? '否' : '是' }}
          </span>
        </el-col>
        <el-col :span="6">
          年龄是否满足：<span style="font-size: 18px; color: red"
            >{{ isPass.ageTest ? '否' : '是' }}
          </span>
        </el-col>
        <el-col :span="6">
          BMI是否满足：<span style="font-size: 18px; color: red"
            >{{ isPass.bmiTest ? '否' : '是' }}
          </span>
        </el-col>
        <el-col :span="6">
          网筛是否满足：<span style="font-size: 18px; color: red"
            >{{ isPass.meshScreenTest ? '否' : '是' }}
          </span>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <el-button
        type="primary"
        :loading="otherLoading"
        :disabled="!canClick"
        @click="handleOk"
      >
        确认报名
      </el-button>
      <el-button @click="handleCancel">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script setup name="ApplyModel">
  import { ref, reactive, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import lodash from 'lodash';

  import {
    confirmPromotion,
    getAllAreaInfo,
    userIntervalCheck,
    managerApplyProject
  } from '@/api/project/index';

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交loading */
  const loading = ref(false);

  const otherLoading = ref(false);

  const isPass = ref({});

  /** 表单实例 */
  const formRef = ref(null);

  const planId = ref(null);

  const canClick = ref(false);

  const areaOptions = ref([]);

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    modelValue: Boolean,
    projectId: String
  });

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    name: '',
    sex: '',
    intervalDays: null,
    startAge: null,
    endAge: null,
    yearRange: null,
    areaData: ''
  });

  const nationList = ref([
    { code: '01', name: '汉族' },
    { code: '02', name: '蒙古族' },
    { code: '03', name: '回族' },
    { code: '04', name: '藏族' },
    { code: '05', name: '维吾尔族' },
    { code: '06', name: '苗族' },
    { code: '07', name: '彝族' },
    { code: '08', name: '壮族' },
    { code: '09', name: '布依族' },
    { code: '10', name: '朝鲜族' },
    { code: '11', name: '满族' },
    { code: '12', name: '侗族' },
    { code: '13', name: '瑶族' },
    { code: '14', name: '白族' },
    { code: '15', name: '土家族' },
    { code: '16', name: '哈尼族' },
    { code: '17', name: '哈萨克族' },
    { code: '18', name: '傣族' },
    { code: '19', name: '黎族' },
    { code: '20', name: '傈僳族' },
    { code: '21', name: '佤族' },
    { code: '22', name: '畲族' },
    { code: '23', name: '高山族' },
    { code: '24', name: '拉祜族' },
    { code: '25', name: '水族' },
    { code: '26', name: '东乡族' },
    { code: '27', name: '纳西族' },
    { code: '28', name: '景颇族' },
    { code: '29', name: '柯尔克孜族' },
    { code: '30', name: '土族' },
    { code: '31', name: '达斡尔族' },
    { code: '32', name: '仫佬族' },
    { code: '33', name: '羌族' },
    { code: '34', name: '布朗族' },
    { code: '35', name: '撒拉族' },
    { code: '36', name: '毛南族' },
    { code: '37', name: '仡佬族' },
    { code: '38', name: '锡伯族' },
    { code: '39', name: '阿昌族' },
    { code: '40', name: '普米族' },
    { code: '41', name: '塔吉克族' },
    { code: '42', name: '怒族' },
    { code: '43', name: '乌孜别克族' },
    { code: '44', name: '俄罗斯族' },
    { code: '45', name: '鄂温克族' },
    { code: '46', name: '德昂族' },
    { code: '47', name: '保安族' },
    { code: '48', name: '裕固族' },
    { code: '49', name: '京族' },
    { code: '50', name: '塔塔尔族' },
    { code: '51', name: '独龙族' },
    { code: '52', name: '鄂伦春族' },
    { code: '53', name: '赫哲族' },
    { code: '54', name: '门巴族' },
    { code: '55', name: '珞巴族' },
    { code: '56', name: '基诺族' }
  ]);

  const typeList = ref([
    {
      label: '请选择',
      value: ''
    },
    {
      label: '男',
      value: '0'
    },
    {
      label: '女',
      value: '1'
    }
    // {
    //   label: '未知',
    //   value: '2'
    // }
  ]);

  /** 表单验证规则 */
  const rules = reactive({
    areaData: [
      {
        required: true,
        message: '请选择所在城市',
        type: 'Object',
        trigger: 'blur'
      }
    ],
    sex: {
      required: true,
      message: '请选择性别',
      trigger: ['change', 'blur']
    },
    nation: {
      required: true,
      message: '请选择民族',
      trigger: ['change', 'blur']
    },
    weight: [
      {
        required: true,
        message: '请输入体重',
        trigger: 'blur'
      }
    ],
    height: [
      {
        required: true,
        message: '请输入身高',
        trigger: 'blur'
      }
    ],

    phone: [
      {
        required: true,
        message: '请输入手机号',
        trigger: 'blur'
      }
    ],
    idCard: [
      {
        required: true,
        message: '请输入身份证号',
        trigger: 'blur'
      }
    ],
    name: [
      {
        required: true,
        message: '请输入姓名',
        trigger: 'blur'
      }
    ]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    emit('update:modelValue', false);
    loading.value = false;
  };

  const onChangeArea = (val) => {
    if (typeof val == 'object' && val.length > 1) {
      form.provCode = val[0];
      const index = areaOptions.value.findIndex(
        (t) => t.value == form.provCode
      );
      form.provName = areaOptions.value[index].label;

      form.cityCode = val[1];
      const city = areaOptions.value[index].children.find(
        (t) => t.value == form.cityCode
      );
      form.cityName = city.label;
    }
  };

  const search = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      try {
        loading.value = true;
        const params = {
          ...form,
          livingCity: form.provName + form.cityName,
          projectId: props?.projectId
        };
        const res = await userIntervalCheck(params);
        Object.assign(isPass.value, res?.data);
        canClick.value = true;
        loading.value = false;
      } catch (e) {
        loading.value = false;
        canClick.value = false;
        EleMessage.error(e);
      }
    });
  };

  /** 保存修改 */
  const handleOk = async () => {
    try {
      otherLoading.value = true;
      const params = {
        ...form,
        livingCity: form.provName + form.cityName,
        projectId: props?.projectId
      };
      const res = await managerApplyProject(params);
      otherLoading.value = false;
      EleMessage.success('报名成功!');
      canClick.value = false;
      handleClosed();
      handleCancel();
    } catch (e) {
      otherLoading.value = false;
      EleMessage.error(e?.message);
    }
  };

  /** 弹窗关闭事件 */
  const handleClosed = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    loading.value = false;
  };

  const getAreaList = async () => {
    const data = await getAllAreaInfo();
    areaOptions.value = removeSecondLevelChildren(data);
  };

  const removeSecondLevelChildren = (data) => {
    // 遍历顶级元素（在这个例子中只有一个）
    data.forEach((topLevelItem) => {
      // 遍历第一层的children
      if (topLevelItem.children && Array.isArray(topLevelItem.children)) {
        topLevelItem.children.forEach((firstLevelItem) => {
          // 删除第二层的children属性
          delete firstLevelItem.children;
          // 如果还有options属性，也可以选择删除（根据你的需求）
          // delete firstLevelItem.options;
        });
      }
    });
    return data; // 返回修改后的数据（虽然在这个例子中直接修改了原数组）
  };

  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        getAreaList();
        // if (props.data) {
        //   assignFields(props.data);
        //   loadData(props.data.siteId);
        //   console.log(props.data.siteId, 'props.data.siteId');
        // }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
