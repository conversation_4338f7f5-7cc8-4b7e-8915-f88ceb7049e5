# 生产环境接口地址
VITE_APP_PRODUCT = true

# 生产环境配置
NODE_ENV = 'production'
ENV = 'production'

# 本地运行地址
VITE_APP_BASE_API = '/admin-api'
VITE_APP_BASE_API_URL = 'https://www.gcpdata.cn/admin-api'

# 登录相关接口
# VITE_APP_ADMIN_BASE_API = '/admin-api'
# VITE_APP_ADMIN_BASE_API_URL = 'http://recruit-dev.frp.520gcp.com/admin-api'

# business相关接口
VITE_APP_BUSINESS_BASE_API = '/business-api'
VITE_APP_BUSINESS_BASE_API_URL = 'https://www.gcpdata.cn/business-api'
VITE_APP_BASE_OSS = 'https://iscreen.gcpdata.cn'

# sse 开关
VITE_APP_SSE = true
# 志愿者数据库筛查相关地址
VITE_APP_SUBJECTS_BASE_API = 'https://screen.gcpdata.cn/subjects/api'
VITE_APP_SUBJECTS_BASE_API2 ='https://screen.gcpdata.cn/logger'




