<template>
  <div class="camera-select">
    <span style="font-size:16px;color:#4c5c82;">摄像头选择</span>
    <el-select
      v-model="camera"
      placeholder="请选择摄像头"
      @change="changeCamera"
    >
      <el-option
        v-for="item in cameraList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref, nextTick } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  const camera = ref('');
  const emit = defineEmits(['changeCamera']);
  onMounted(() => {
    getCameraList();
  });
  const cameraList = ref([]);
  // 切换摄像头
  const changeCamera = async (value) => {
    console.log(value);
    const stream = await navigator.mediaDevices.getUserMedia({
      audio: false, // 不需要音频
      video: { deviceId: { exact: value } } // selectedDeviceId 为用户选择的设备ID
    });
    // 数据存储，保证用户下次选择的设备是同一个
    localStorage.setItem('AIMatchCameraId', value);
    // 播放视频流
    emit('changeCamera', stream);
  };
  const getCameraList = () => {
    if (!navigator.mediaDevices) {
      EleMessage.error('该浏览器不支持开启摄像头，请更换最新版浏览器！');
      return;
    }
    navigator.mediaDevices.getUserMedia({ video: true }).then((stream) => {
      navigator.mediaDevices
        .enumerateDevices()
        .then((devices) => {
          devices.forEach((device) => {
            const label =
              device.label || '摄像头' + (cameraList.value.length + 1);
            if (device.kind === 'videoinput') {
              cameraList.value.push({
                label,
                value: device.deviceId
              });
            }
          });
          nextTick(() => {
            // 初始化摄像头
            if (
              camera.value &&
              cameraList.value.some((item) => item.value === camera.value)
            ) {
              camera.value = localStorage.getItem('AIMatchCameraId');
              changeCamera(camera.value);
            } else if (cameraList.value.length > 0) {
              camera.value = cameraList.value[0].value;
              changeCamera(camera.value);
            } else {
              EleMessage.error(
                '未检测到摄像头开启，请检查摄像头是否插好，或者摄像头被占用！'
              );
            }
          });
        })
        .catch((err) => {
          console.log(err.name + ': ' + err.message);
        });
    });
  };
</script>
<style scoped lang="scss">
  .camera-select {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei;
    font-weight: 400;
    font-size: 18px;
    color: #3b426f;
    margin: 0 0 5px 0;
    & > span {
      width: 95px;
    }
    .el-select {
      // margin-left: 20px;
      width: 340px;
    }
  }
</style>
