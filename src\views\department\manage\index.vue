<template>
  <ele-page :flex-table="true" hide-footer>
    <department-search ref="searchRef" @search="reload" @add="openEdit()" />
    <ele-card :flex-table="true" :body-style="{ paddingTop: '8px' }">
      <ele-pro-table
        ref="tableRef"
        row-key="userId"
        :columns="columns"
        :datasource="datasource"
        :bottom-line="tableFullHeight"
        highlight-current-row
        cache-key="spoTable"
      >
        <!-- 高级搜索条件 -->
        <!-- <smart-form ref="searchRef" @search="reload" /> -->
        <!-- 关键字搜索 -->
        <!-- <template #toolbar>
          <el-space>
            <el-button
              v-permission="`site:set:professional:add`"
              type="primary"
              class="ele-btn-icon"
              @click="openEdit()"
            >
              新建
            </el-button>
           <el-button
              v-permission="`site:set:professional:import`"
              type="primary"
              plain
              class="ele-btn-icon"
              @click="importFile"
            >
              导入
          </el-button>
            <smart-search @search="reloadSearch" />
          </el-space>
        </template> -->
        <!-- 表头工具按钮 -->
        <!-- <template #tools>
          <el-space size="default" :wrap="true" />
          <el-tooltip effect="dark" content="导出" placement="top">
            <el-icon @click="exportDept()">
              <Download />
            </el-icon>
          </el-tooltip>
        </template> -->
        <template #PI="{ row }">
          {{ getPIs(row) }}
        </template>
        <template #action="{ row }">
          <el-space>
            <el-link type="primary" :underline="false" @click="openEdit(row)">
              编辑
            </el-link>
            <el-divider direction="vertical" style="margin: 0" />
            <el-link type="primary" :underline="false" @click="deleteDep(row)">
              删除
            </el-link>
          </el-space>
        </template>
      </ele-pro-table>
    </ele-card>
    <AddorEditDialog
      v-model="showAddorEdit"
      :data="currentRow"
      @success="reload"
    />
    <!-- <importConfig v-model="showImport" :data="currentRow" @success="reload" />
    <importConfig
      v-model="showImport"
      :config-import-url="configImportUrl"
      :config-download-url="configDownloadUrl"
      @success="reload"
    /> -->
  </ele-page>
</template>

<script setup name="Professional">
  import { ref, computed } from 'vue';
  import { useThemeStore } from '@/store/modules/theme';
  import { storeToRefs } from 'pinia';
  import _ from 'lodash';
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { getSiteDeptPageList, deleteSiteDeptInfo } from '@/api/department';
  import AddorEditDialog from '../components/AddorEditDialog.vue';
  import DepartmentSearch from '../components/department-search.vue';
  // import importConfig from './components/importConfig.vue';

  //#region 表格配置

  //公共主题
  const { tableFullHeight } = storeToRefs(useThemeStore());

  // 表格列配置
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        label: `序号`,
        width: 65,
        align: 'center',
        fixed: 'left'
      },
      {
        label: `科室名称`,
        prop: 'deptName',
        width: 300,
        showOverflowTooltip: true
      },
      {
        label: `PI`,
        prop: 'deptPi',
        showOverflowTooltip: true
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 140,
        align: 'left',
        resizable: false,
        fixed: 'right',
        slot: 'action'
      }
    ];
  });

  // 表格数据源
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getSiteDeptPageList({
      ...where,
      ...orders,
      ...filters,
      page: page,
      limit: limit
    });
  };

  /* 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };
  /* 搜索 */
  const reloadSearch = (where) => {
    searchRef.value?.onSearchFilterSubmit(where);
  };

  //#endregion

  const configImportUrl = '/site/set/professional/importProfessional';
  const configDownloadUrl =
    '/site/set/professional/downloadProfessionalTemplate';

  // 搜索组件实例
  const searchRef = ref();

  // 表格实例
  const tableRef = ref(null);

  const showAddorEdit = ref(false);
  const showImport = ref(false);
  const currentRow = ref({});

  /* 格式化PI */
  const getPIs = (row) => {
    let reslut = '';
    const items = row.piList ?? [];
    items.forEach((item) => {
      reslut += `${item.piName} ${item.jobNumber ?? ''},`;
    });
    return _.trimEnd(reslut, ',');
  };

  /* 删除PI*/
  const deleteDep = (row) => {
    ElMessageBox.confirm(`确认是否删除${row.deptName}是否继续?`, '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(async () => {
        try {
          const loading = EleMessage.loading('请求中..');
          const res = await deleteSiteDeptInfo(row.id);
          if (res?.code == 200) {
            EleMessage.success('删除成功');
            reload();
          } else {
            EleMessage.error(res?.msg);
          }
          loading.close();
        } catch (e) {
          loading.close();
          EleMessage.error(e.message);
        }
      })
      .catch(() => {});
  };

  /* 新增PI */
  const openEdit = (row = null) => {
    showAddorEdit.value = true;
    currentRow.value = row;
  };

  /* 导入PI */
  const importFile = () => {
    showImport.value = true;
  };
</script>
