vue
<template>
  <ele-modal
    resizable
    :width="600"
    :model-value="modelValue"
    :title="title"
    center
    @update:modelValue="updateModelValue"
  >
    <div class="line" />
    <div class="scrollable-content">
      <div v-for="(item, index) in list" :key="index" class="question-item">
        <label>{{ item.title }}</label>
        <div v-if="item.type === 'radio'" class="radio-group">
          <div v-for="(option, idx) in item.option" :key="idx">
            <el-button
              :type="item.value === option.value ? 'primary' : 'default'"
              @click="item.value = option.value"
              class="radio-button"
            >
              {{ option.label }}
            </el-button>
          </div>
        </div>
        <div v-else-if="item.type === 'input'">
          <el-input v-model="item.value" :placeholder="item.placeholder" />
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <el-button @click="handleCancel">淘汰</el-button>
      <el-button type="primary" @click="handleConfirm">通过</el-button>
    </div>
  </ele-modal>
</template>
<script setup>
  import { ref, watch } from 'vue';
  import { ElInput } from 'element-plus';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { updateUserApplyStatus } from '@/api/recruitment/index';

  const emit = defineEmits(['update:modelValue', 'done']);

  const props = defineProps({
    modelValue: Boolean,
    data: Object
  });
  const list = ref([]);
  const title = ref('问卷查看');
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  watch(
    () => props.modelValue,
    (val) => {
      const { userName, questionnaireContent } = props.data;
      title.value = `问卷查看 - ${userName}`;
      list.value = JSON.parse(questionnaireContent);
      console.log('##', list.value);
    }
  );

  const handleCancel = async () => {
    try {
      const params = {
        applyId: [props.data?.applyId],
        applyStatus: 2
      };
      await updateUserApplyStatus(params);
      emit('done');
      updateModelValue(false); // 关闭弹窗
    } catch (e) {
      EleMessage.success(e?.message);
    }
  };

  const handleConfirm = async () => {
    try {
      const params = {
        applyId: [props.data?.applyId],
        applyStatus: 1
      };
      await updateUserApplyStatus(params);
      emit('done');
      updateModelValue(false); // 关闭弹窗
    } catch (e) {
      EleMessage.success(e?.message);
    }
  };
</script>
<style lang="scss" scoped>
  .line {
    width: 100%;
    height: 1px;
    margin-top: -10px;
    border-bottom: 1px solid #e5e7eb;
  }
  .scrollable-content {
    max-height: 600px; /* 设置固定高度 */
    overflow-y: auto; /* 启用垂直滚动条 */
    margin: 20px 0px; /* 内容与底部按钮之间的间距 */
  }
  .question-item {
    margin-bottom: 20px;
  }
  .radio-group {
    display: flex;
    flex-direction: row; /* 将选项设置为在同一行 */
    gap: 10px; /* 选项之间的间距 */
  }
  .modal-footer {
    display: flex;
    justify-content: center; /* 将按钮靠右对齐 */
  }
</style>
