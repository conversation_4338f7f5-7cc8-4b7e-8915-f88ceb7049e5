import request from '@/utils/subjectsrequest';
import { download, toFormData } from '@/utils/common';
import dayjs from 'dayjs';

/**
 * 查询备忘录列表
 */
export async function sysMemoList(data) {
  const res = await request.post(`/sys/memo/list`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出备忘录
 */
export async function exportSysmemo(params) {
  const res = await request({
    url: '/sys/memo/export',
    method: 'POST',
    data: params,
    responseType: 'blob'
  });
  download(
    res.data,
    `备忘录_${dayjs(new Date()).format('YYYYMMDDHHMMssSSS')}.xlsx`
  );
}

/**
 * 修改备忘录
 */
export async function sysMemoUpdate(data) {
  const res = await request.post(`/sys/memo/update`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 删除备忘录
 */
export async function sysMemoDelete(data) {
  const res = await request.post(`/sys/memo/delete`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
