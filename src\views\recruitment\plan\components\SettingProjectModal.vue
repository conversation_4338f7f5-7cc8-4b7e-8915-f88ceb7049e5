<!-- 修改密码弹窗 -->
<template>
  <ele-modal
    v-model="visible"
    form
    :width="1000"
    title="设置计划入组人数"
    :append-to-body="true"
    :body-style="{ paddingTop: '6px' }"
    @closed="handleClosed"
  >
    <div style="font-size: 14px; color: #f97316">
      设置后，我们可以更好的对您的招募进行进度提醒，以便您更好的管理招募项目
    </div>
    <!-- Table Content -->
    <div class="modal-body">
      <div class="table-container">
        <el-table v-if="!isHealthProject" :data="tableData" border>
          <el-table-column label="中心" prop="center" width="450px" />
          <el-table-column label="计划入组例数" width="450px">
            <template #default="scope">
              <el-input
                v-model="scope.row.planJoinGroupTotalPeople"
                placeholder="请输入"
                type="number"
                class="input-field"
                :min="0"
                @input="calculateTotal(scope.row)"
              />
            </template>
          </el-table-column>
        </el-table>
        <div v-else>
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-position="top"
            label-width="160px"
            @submit.prevent=""
          >
            <el-form-item label="计划入组人数" prop="planJoinGroupTotalPeople">
              <el-input
                type="number"
                :maxlength="20"
                v-model="form.planJoinGroupTotalPeople"
                placeholder="请输入计划入组人数"
              />
            </el-form-item>
            <el-form-item label="男性计划报名人数" prop="maleApplyPeople">
              <el-input
                type="number"
                :maxlength="20"
                v-model="form.maleApplyPeople"
                placeholder="请输入男性计划报名人数"
              />
            </el-form-item>
            <el-form-item label="女性计划报名人数" prop="femaleApplyPeople">
              <el-input
                type="number"
                :maxlength="20"
                v-model="form.femaleApplyPeople"
                placeholder="请输入女性计划报名人数"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <!-- Footer -->
    <template #footer>
      <div class="d-flex">
        <div v-if="!props.isHealthProject" class="summary">
          汇总项目计划入组例数：{{ totalCount }}
        </div>
        <div v-else></div>
        <div class="button-group">
          <el-button @click="handleCancel" class="cancel-button"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="!props.isHealthProject ? handleSave() : save()"
            class="save-button"
            >保存</el-button
          >
        </div>
      </div>
    </template>
  </ele-modal>
</template>
<script setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { setPlanJoinGroupAndApplyNumber } from '@/api/recruitment/index';

  const emit = defineEmits(['update:modelValue', 'done']);

  const props = defineProps({
    modelValue: Boolean,
    projectId: String,
    model: String,
    projectData: Object,
    isHealthProject: Boolean
  });

  /** 表单验证规则 */
  const rules = reactive({
    planJoinGroupTotalPeople: [
      {
        required: true,
        message: '请输入计划入组人数',
        trigger: 'blur'
      }
    ]
  });

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    projectId: '',
    siteId: '',
    planJoinGroupTotalPeople: null,
    maleApplyPeople: null,
    femaleApplyPeople: null
  });

  const tableData = ref([]);

  const totalCount = computed(() => {
    return tableData.value.reduce((sum, item) => {
      const count = Number(item.planJoinGroupTotalPeople) || 0;
      return sum + count;
    }, 0);
  });
  const calculateTotal = (row) => {
    // const value = row.planJoinGroupTotalPeople;
    // if (value < 0) {
    //   row.planJoinGroupTotalPeople = '';
    // } else if (!/^\d*$/.test(value)) {
    //   row.planJoinGroupTotalPeople = Math.floor(row.planJoinGroupTotalPeople);
    // }
    row.planJoinGroupTotalPeople = parseInt(row.planJoinGroupTotalPeople);
    tableData.value.forEach((item) => {
      if (item.planJoinGroupTotalPeople < 0) {
        item.planJoinGroupTotalPeople = 0;
      }
    });
  };

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交loading */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 关闭弹窗 */
  const handleCancel = () => {
    updateModelValue(false);
  };

  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 弹窗关闭事件 */
  const handleClosed = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    loading.value = false;
  };

  const handleSave = async () => {
    try {
      const params = tableData.value;
      await setPlanJoinGroupAndApplyNumber(params);
      EleMessage.success('设置成功');
      emit('done', props.projectId);
      handleCancel();
    } catch (e) {
      EleMessage.error(e);
    }
  };

  const save = () => {
    try {
      formRef.value?.validate?.(async (valid) => {
        if (!valid) {
          return;
        }
        loading.value = true;
        const params = [
          {
            planJoinGroupTotalPeople:
              Number(form.planJoinGroupTotalPeople) || 0,
            maleApplyPeople: Number(form.maleApplyPeople) || 0,
            femaleApplyPeople: Number(form.femaleApplyPeople) || 0,
            projectId: props?.projectId
          }
        ];
        await setPlanJoinGroupAndApplyNumber(params);
        EleMessage.success('设置成功');
        emit('done', props.projectId);
        handleCancel();
      });
    } catch (e) {
      loading.value = false;
      EleMessage.error(e);
    }
  };

  const fetchData = async () => {
    loading.value = true;
    try {
      console.log(props.projectData);
      if (props.isHealthProject) {
        Object.assign(form, props.projectData);
        form.maleApplyPeople = props.projectData?.malePlanApplyNumber;
        form.femaleApplyPeople = props.projectData?.femalePlanApplyNumber;
      } else {
        tableData.value = props.projectData?.multiSiteJoinGroupDto
          .filter((item) => item.siteId !== null)
          .map((item) => ({
            center: item.siteName,
            planJoinGroupTotalPeople: item?.planJoinGroupTotalPeople,
            siteId: item?.siteId,
            projectId: props.projectId
          }));
      }
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        fetchData();
      }
    },
    { immediate: true }
  );
</script>

<style lang="scss" scoped>
  .modal-body {
    padding: 16px 0px;
  }
  .table-container {
    max-height: 400px; /* 设置表格最大高度 */
    overflow-y: auto; /* 允许垂直滚动 */
    overflow-x: hidden;
  }
  .modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .summary {
    font-size: 16px;
    font-weight: 500;
    color: #000;
  }
  .button-group {
    display: flex;
    gap: 16px;
  }
  .cancel-button,
  .save-button {
    border-radius: 4px;
  }
  .input-field {
    width: 100%;
  }
  .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    box-sizing: border-box;
  }
</style>
