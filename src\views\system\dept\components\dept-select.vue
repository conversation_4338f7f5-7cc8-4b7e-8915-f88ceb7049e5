<!-- 部门选择下拉框 -->
<template>
  <el-select
    clearable
    filterable
    v-model="model"
    :placeholder="placeholder"
    class="ele-fluid"
  >
    <el-option
      v-for="item in data"
      :key="item.siteId"
      :value="item.siteId"
      :label="item.siteName"
    />
  </el-select>
  <!-- <el-select
    clearable
    check-strictly
    default-expand-all
    :data="data"
    node-key="siteId"
    :props="{ label: 'siteName' }"
    v-model="model"
    :placeholder="placeholder"
    class="ele-fluid"
    :popper-options="{ strategy: 'fixed' }"
  /> -->
</template>

<script setup>
  import { ref } from 'vue';
  import { EleMessage, toTree } from '@hnjing/zxzy-admin-plus/es';
  import { getSiteSelectList } from '@/api/recruitment/index';

  defineProps({
    /** 提示信息 */
    placeholder: {
      type: String,
      default: '请选择归属部门'
    }
  });

  /** 选中的部门 */
  const model = defineModel({ type: [Number, String] });

  /** 部门数据 */
  const data = ref([]);

  /** 获取部门数据 */

  /** 查询 */
  const query = () => {
    getSiteSelectList()
      .then((list) => {
        // const totalNum = list.reduce((prev, cur) => prev + cur.projectNum, 0);
        // data.value =
        //   [{ siteId: '', siteName: '全部', projectNum: totalNum }, ...list] ??
        //   [];
        data.value = [...list];
        // nextTick(() => {
        //   handleNodeClick(data.value[0]);
        // });
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  query();
  // listDepts()
  //   .then((list) => {
  //     data.value = toTree({
  //       data: list,
  //       idField: 'deptId',
  //       parentIdField: 'parentId'
  //     });
  //   })
  //   .catch((e) => {
  //     EleMessage.error(e.message);
  //   });
</script>
