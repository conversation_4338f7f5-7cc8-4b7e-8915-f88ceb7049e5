<!-- 修改密码弹窗 -->
<template>
  <ele-modal
    form
    :width="1200"
    title="帖子详情"
    :append-to-body="true"
    v-model="visible"
    @closed="handleClosed"
  >
    <el-descriptions v-if="circleDetails" column="1" :border="true">
      <el-descriptions-item label="标题">
        <div>{{ circleDetails.title }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="内容">
        <div>{{ circleDetails.content }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="图片内容">
        <div>
          <!-- <img
            v-for="(image, index) in circleDetails.images"
            :key="index"
            :src="image?.url"
            alt="图片内容"
            style="width: 100px; height: 100px; margin-right: 5px"
          /> -->
          <el-image
            style="width: 100px; height: 100px"
            :src="url"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="circleDetailsImages"
            show-progress
            :initial-index="6"
            :url-list="circleDetailsImages"
            fit="cover"
          />
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <comment-search ref="searchRef" @search="reload" />
    <ele-pro-table
      ref="tableRef"
      row-key="commentId"
      :columns="columns"
      :datasource="datasource"
      :show-overflow-tooltip="true"
      highlight-current-row
      :footer-style="{ paddingBottom: '16px' }"
      cache-key="circleDataTable"
    >
      <template #status="{ row }"
        ><dict-data code="circle_status" type="tag" v-model="row.status" />
      </template>

      <template #action="{ row }">
        <el-link
          v-if="row.offFlag == 0"
          type="primary"
          :underline="false"
          @click="offLine(row)"
        >
          下架
        </el-link>
        <el-link
          v-if="row.offFlag == 1"
          type="primary"
          :underline="false"
          @click="offLine(row)"
        >
          上架
        </el-link>
      </template>
    </ele-pro-table>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch, computed } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import {
    queryCommentManagerPageList,
    getPostManagerDetail,
    commentOffLine
  } from '@/api/circle/index';
  import CommentSearch from './comment-search.vue';

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 搜索栏实例 */
  const searchRef = ref(null);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 提交loading */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  const circleDetailsImages = ref([]);

  const url = ref('');

  const props = defineProps({
    currnetID: String,
    model: String,
    isFirstAudit: Boolean,
    modelValue: Boolean,
    currentData: Object
  });

  // const circleDetailsImages = computed(() => {
  //   return circleDetails.value?.images || [];
  // });

  // const url = computed(() => {
  //   if (circleDetails.value?.images?.length > 0) {
  //     return circleDetails.value?.images[0].url;
  //   }
  //   return '';
  // });

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    status: 1,
    rejectReason: ''
  });

  /** 表格数据源 */
  const datasource = ({ pages, where, orders }) => {
    return queryCommentManagerPageList({
      ...where,
      ...orders,
      ...pages,
      postId: props?.currnetID
    });
  };

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 55,
      align: 'center'
    },
    {
      prop: 'content',
      label: '评论内容',
      align: 'center',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      prop: 'userName',
      label: '用户姓名',
      align: 'center',
      minWidth: 160
    },
    {
      prop: 'phonenumber',
      label: '手机号',
      width: 140,
      align: 'center'
    },
    {
      prop: 'likeCount',
      label: '点赞数',
      minWidth: 80,
      align: 'center'
    },
    {
      prop: 'status',
      label: '评论状态',
      minWidth: 110,
      align: 'center',
      formatter: (row) => (row.status == 0 ? '正常' : '删除')
    },
    {
      prop: 'offFlag',
      label: '是否下架',
      minWidth: 110,
      align: 'center',
      formatter: (row) => (row.offFlag == 1 ? '是' : '否')
    },
    {
      prop: 'userProvince',
      label: '省份',
      minWidth: 110,
      align: 'center'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 120,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  const offLine = async (row) => {
    const title =
      row?.offFlag == 0
        ? `确认是否下架 ‘${row?.content}’ 评论吗？`
        : `确认是否上架 ‘${row?.content}’ 评论吗？`;
    try {
      await ElMessageBox.confirm(title, '提示', {
        type: 'warning',
        draggable: true,
        customStyle: {
          maxWidth: '600px'
        }
      });
      const formData = new FormData();
      formData.append('commentId', row?.commentId);
      formData.append('offFlag', row?.offFlag == 0 ? 1 : 0);
      await commentOffLine(formData);
      EleMessage.success('操作成功');
      reloadAll();
    } catch (e) {
      if (e != 'cancel') {
        EleMessage.error(e?.message);
      }
    }
  };

  /** 刷新表格 */
  const reloadAll = (where) => {
    tableRef.value?.reload?.({});
  };

  /* 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭弹窗 */
  const handleCancel = () => {
    updateModelValue(false);
  };

  /** 弹窗关闭事件 */
  const handleClosed = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    loading.value = false;
  };

  const circleDetails = ref({});

  const initData = async () => {
    const params = { postId: props?.currnetID };
    const res = await getPostManagerDetail(params);
    Object.assign(circleDetails.value, res);
    circleDetailsImages.value = res?.images?.map((image) => image.url) || [];
    url.value = res?.images[0]?.url;
  };

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        initData();
      }
    },
    { immediate: true }
  );
</script>
