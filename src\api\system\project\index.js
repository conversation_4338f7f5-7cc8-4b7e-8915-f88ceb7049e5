import request from '@/utils/request-business';

/**
 * 查询字典数据列表
 */
// export async function listDictDatas(type) {
//   const res = await request.get('/system/dict/data/type/' + type);
//   if (res.data.code === 200 && res.data.data) {
//     return res.data.data;
//   }
//   return Promise.reject(new Error(res.data.msg));
// }

/**
 * 查询所有的项目
 * @param {*} data
 * @returns
 */
export async function getAllProjectPageList(params) {
  const res = await request.get('/manage/project/getAllProjectPageList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 设置项目的排序
 * @param {*} data
 * @returns
 */
export async function setProjectSort(data) {
  const res = await request.post('/manage/project/setProjectSort', data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 运维端上下架项目
 * @param {*} data
 * @returns
 */
export async function changePlatformStatus(data) {
  const res = await request.post('/manage/project/changePlatformStatus', data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
