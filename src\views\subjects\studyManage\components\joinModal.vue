<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="730"
    :model-value="modelValue"
    title="标记入组"
    @update:modelValue="updateModelValue"
    align-center
  >
    <template #header>
      <div class="dialog-header" style="position: relative">
        <span>标记入组</span>
        <!-- <span
          style="
            font-size: 14px;
            color: #faad14;
            font-weight: 500;
            position: absolute;
            bottom: 0;
            right: 100px;
          "
        >
          仅支持标记“待入组”的数据
        </span> -->
      </div>
    </template>
    <div class="modal-table-dialog error">
      <div style="display: flex; align-items: center; margin-bottom: 8px">
        <div style="font-size: 16px">批量填充</div>
        <el-date-picker
          v-model="joindate"
          type="date"
          placeholder="请选择入组时间"
          :disabled-date="disableddate"
          clearable
          value-format="YYYY-MM-DD"
          style="flex: 1; margin: 0 10px"
        />
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
      <el-form :model="ruleForm" ref="formRule">
        <el-table
          :data="ruleForm.tableData"
          :header-cell-style="{ background: '#eeeeee' }"
          height="50vh"
          @selection-change="handleSelectionChange"
          ref="tableRef"
        >
          <template #empty>
            <el-empty description="暂无数据" />
          </template>
          <el-table-column type="selection" width="65" />
          <el-table-column
            prop="name"
            label="（筛选号）姓名"
            width="200"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div
                style="
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
              >
                <span v-if="row.filterno">（{{ row.filterno }}）</span
                >{{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="joindate" label="入组时间" width="160">
            <template #header>
              <span style="color: #b90000; margin-right: 3px">*</span>入组时间
            </template>
            <template #default="{ row, $index }">
              <el-form-item
                :prop="`tableData.${$index}.joindate`"
                :rules="[
                  {
                    required: true,
                    validator: validatorRule,
                    trigger: ['blur', 'change']
                  }
                ]"
              >
                <el-date-picker
                  v-model="row.joindate"
                  type="date"
                  placeholder="请选择入组时间"
                  :disabled-date="disableddate"
                  clearable
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="joinno" label="入组号" width="200">
            <template #default="{ row }">
              <el-form-item>
                <el-input
                  v-model="row.joinno"
                  placeholder="请输入入组号"
                  clearable
                  show-word-limit
                  maxlength="20"
                />
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit"
          >确 定</el-button
        >
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref, computed } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import {
    markJoinGroup,
    selectWaitJoinStatistic
  } from '@/api/subjects/studyManage/index.js';
  import { ElMessageBox } from 'element-plus';
  import { useUserStore } from '@/store/modules/user';
  import dayjs from 'dayjs';
  import { Warning } from '@element-plus/icons-vue';
  const userStore = useUserStore();
  const userInfo = computed(() => userStore.info ?? {});
  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Array
  });
  const disableddate = (time) => {
    const nowDate = dayjs(time);
    const currentDate = dayjs(userInfo.value.currentTime);
    return !(currentDate >= nowDate);
  };
  const loading = ref(false);

  const close = () => {
    updateModelValue(false);
  };
  const formRule = ref(null);
  const validatorRule = (rule, value, callback) => {
    const parts = rule.field.split('.');
    const index = parts[1];
    if (
      !value &&
      selectionsList.value.some(
        (val) => val.guid == ruleForm.value.tableData[index].guid
      )
    ) {
      callback(new Error('请选择入组时间'));
      return;
    }
    callback();
  };
  const submit = () => {
    if (selectionsList.value.length == 0) {
      EleMessage.warning('请至少勾选一条数据再确认');
      return;
    }
    formRule.value.validate((valid) => {
      if (valid) {
        const data = selectionsList.value.map((val) => {
          return {
            guid: val.guid,
            filterno: ruleForm.value.tableData.find(
              (item) => item.guid == val.guid
            ).filterno,
            joinno: ruleForm.value.tableData.find(
              (item) => item.guid == val.guid
            ).joinno,
            joindate: ruleForm.value.tableData.find(
              (item) => item.guid == val.guid
            ).joindate
          };
        });
        ElMessageBox.confirm(
          `是否确定将【${selectionsList.value
            .map((val) => val.name)
            .join()}】标记为入组?`,
          '提示',
          {
            confirmButtonText: '确 定',
            cancelButtonText: '取 消',
            center: true
          }
        ).then(() => {
          loading.value = true;
          markJoinGroup(data).then((res) => {
            loading.value = false;
            if (res.code == 200) {
              getBacklog();
              EleMessage.success('标记成功');
              emit('done');
              updateModelValue(false);
              return;
            }
            EleMessage.error(res.msg);
          });
        });

        return;
      }
    });
  };
  const getBacklog = () => {
    selectWaitJoinStatistic({}).then((res) => {
      userStore.backlogList = res.data;
    });
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const ruleForm = ref({ tableData: [] });
  const selectionsList = ref([]);
  const tableRef = ref(null);
  nextTick(async () => {
    ruleForm.value.tableData = JSON.parse(JSON.stringify(props.data));
    const today = dayjs().format('YYYY-MM-DD');
    ruleForm.value.tableData.forEach((item) => {
      item.joindate = today;
      setTimeout(() => {
        tableRef.value.toggleRowSelection(item, true);
      });
    });
  });
  const handleSelectionChange = (val) => {
    selectionsList.value = val;
    ruleForm.value.tableData.forEach((item, index) => {
      if (!selectionsList.value.some((val) => val.guid == item.guid)) {
        formRule.value.validateField(`tableData.${index}.joindate`);
      }
    });
  };
  const joindate = ref('');
  const save = () => {
    ruleForm.value.tableData.forEach((item, index) => {
      if (selectionsList.value.some((val) => val.guid == item.guid)) {
        item.joindate = joindate.value;
      }
      if (selectionsList.value.some((val) => val.guid == item.guid)) {
        formRule.value.validateField(`tableData.${index}.joindate`);
      }
    });
  };
</script>
<style lang="scss">
  .modal-table-dialog .el-table .cell {
    overflow: initial !important;
    .el-input .el-input__wrapper,
    .el-textarea .el-textarea__inner,
    .el-select .el-select__wrapper,
    .el-date-editor.el-input,
    .el-date-editor.el-range-editor.el-input__wrapper {
      min-height: 26px !important;
      height: 26px !important;
    }
    .el-form-item {
      margin-bottom: 2px;
    }
  }
</style>
