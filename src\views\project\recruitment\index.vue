vue
<template>
  <ele-page
    flex-table
    :multi-card="false"
    hide-footer
    style="min-height: 420px; background-color: #ecf2f9"
  >
    <ele-card flex-table :body-style="{ padding: '16px', overflow: 'auto' }">
      <div>
        <div>
          <div class="title" style="margin-left: 110px"
            >发布招募信息
            <span class="sub-title">
              项目发布成功后，招募类型、招募名称、招募截止日期不可修改
            </span>
          </div>
          <div style="margin: 0px 0px 10px 110px">
            <div style="margin-bottom: 16px">
              <!-- <el-button
                v-permission="'project:ai:agent'"
                type="primary"
                @click="AIAgentText()"
              >
                AI文本识别
              </el-button> -->
              <img
                :src="AIAgent"
                alt="AI Agent"
                style="width: 65px; height: 65px; cursor: pointer"
                @click="AIAgentText()"
              />
            </div>
            <span class="ss-title">招募类型</span>
            <el-button
              :class="{ active: form.projectRecruitType == 1 }"
              @click="form.projectRecruitType = 1"
            >
              健康志愿者
            </el-button>
            <el-button
              :class="{ active: form.projectRecruitType == 2 }"
              @click="form.projectRecruitType = 2"
            >
              患者
            </el-button>
          </div>
          <template v-if="form.projectRecruitType == 1">
            <el-form
              ref="formRef"
              :model="form"
              :rules="rules"
              label-width="180px"
              :label-position="labelPosition"
              style="width: 70%; margin-left: 110px"
            >
              <div>
                <!-- 项目招募名称 -->
                <el-form-item
                  label="项目招募名称"
                  prop="projectRecruitName"
                  required
                >
                  <el-input
                    v-model="form.projectRecruitName"
                    maxlength="100"
                    show-word-limit
                    placeholder="请输入项目招募名称"
                  />
                </el-form-item>

                <!-- 招募中心和截止日期 -->
                <div>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="招募中心" prop="siteIdList">
                        <el-select
                          v-model="form.siteIdList"
                          clearable
                          filterable
                          :disabled="isSite"
                          placeholder="请选择招募中心"
                        >
                          <el-option
                            v-for="item in centers"
                            :key="item.siteId"
                            :label="item.siteName"
                            :value="item.siteId"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item
                        label="招募截止日期"
                        prop="projectRecruitDeadline"
                        required
                      >
                        <el-date-picker
                          v-model="form.projectRecruitDeadline"
                          type="date"
                          value-format="YYYY-MM-DD"
                          style="width: 100%"
                          placeholder="选择招募截止日期"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <!-- 项目名称 方案编号 -->
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="项目名称" prop="projectName" required>
                      <el-input
                        v-model="form.projectName"
                        maxlength="100"
                        show-word-limit
                        placeholder="请输入项目名称"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="方案编号" prop="schemeNumber" required>
                      <el-input
                        v-model="form.schemeNumber"
                        maxlength="100"
                        show-word-limit
                        placeholder="请输入方案编号"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- 项目编号 申办方 -->
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      label="项目编号"
                      prop="projectNumber"
                      required
                    >
                      <el-input
                        v-model="form.projectNumber"
                        maxlength="100"
                        show-word-limit
                        placeholder="请输入项目编号"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label="申办方"
                      prop="sponsor"
                      :rules="{
                        type: 'object',
                        required: true,
                        message: '请选择申办方'
                      }"
                    >
                      <div
                        class="flex items-center justify-between"
                        style="width: 100%"
                      >
                        <CompanySelect v-model="form.sponsor" />
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <div class="title"
                  >项目介绍
                  <span class="sub-title">
                    可以通过项目介绍向志愿者关心的风险和参与试验内容进行介绍，提高志愿者的报名意愿
                  </span>
                </div>
                <!-- 间隔天数 -->
                <el-form-item
                  label="间隔天数"
                  required
                  label-position="left"
                  label-width="100px"
                >
                  <el-button
                    :class="{ active: form.meshScreenDto.networkingType == 1 }"
                    @click="form.meshScreenDto.networkingType = 1"
                  >
                    联网
                  </el-button>
                  <el-button
                    :class="{ active: form.meshScreenDto.networkingType == 2 }"
                    @click="form.meshScreenDto.networkingType = 2"
                  >
                    不联网
                  </el-button>
                </el-form-item>
                <el-row
                  v-if="form.meshScreenDto.networkingType == 1"
                  :gutter="20"
                >
                  <el-col :span="12"
                    ><el-form-item label="">
                      <el-checkbox-group
                        v-model="form.meshScreenDto.dataList[0].select"
                      >
                        <el-checkbox value="1" name="zhongxing">
                          <span style="font-size: 16px; color: #374151"
                            >中兴</span
                          ></el-checkbox
                        >
                      </el-checkbox-group>
                      <el-input
                        v-model="form.meshScreenDto.dataList[0].month"
                        type="number"
                        placeholder="请输入正整数间隔月数"
                        @input="handleInputMonth($event, 1)"
                        ><template #suffix> 个月 </template></el-input
                      >
                    </el-form-item></el-col
                  >
                  <el-col :span="12"
                    ><el-form-item label="">
                      <el-checkbox-group
                        v-model="form.meshScreenDto.dataList[1].select"
                      >
                        <el-checkbox value="2" name="taimei">
                          <span style="font-size: 16px; color: #374151"
                            >其他</span
                          ></el-checkbox
                        >
                      </el-checkbox-group>

                      <el-input
                        v-model="form.meshScreenDto.dataList[1].month"
                        type="number"
                        placeholder="请输入正整数间隔月数"
                        @input="handleInputMonth($event, 2)"
                        ><template #suffix> 个月 </template>
                      </el-input>
                    </el-form-item></el-col
                  >
                </el-row>

                <!-- 计划完成日期和药物名称 -->
                <el-row :gutter="20">
                  <el-col :span="12">
                    <!-- <el-form-item label="计划筛选日期" prop="planScreeningDate">
                    <el-date-picker
                      v-model="form.planScreeningDate"
                      type="date"
                      style="width: 100%"
                      placeholder="请选择计划筛选日期"
                    />
                  </el-form-item> -->
                    <el-form-item
                      label="计划筛选日期"
                      prop="planScreeningDate"
                      required
                    >
                      <el-date-picker
                        v-model="form.planScreeningDate"
                        type="date"
                        value-format="YYYY-MM-DD"
                        style="width: 100%"
                        placeholder="请选择计划筛选日期"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12"
                    ><el-form-item label="药物名称" prop="drugName" required>
                      <el-input
                        v-model="form.drugName"
                        maxlength="100"
                        show-word-limit
                        placeholder="请输入药物名称"
                      /> </el-form-item
                  ></el-col>
                </el-row>

                <el-row :gutter="20">
                  <el-col :span="12"
                    ><el-form-item label="适应症">
                      <el-input
                        v-model="form.indications"
                        maxlength="100"
                        show-word-limit
                        placeholder="请输入适应症"
                      /> </el-form-item
                  ></el-col>
                  <el-col :span="12"
                    ><el-form-item label="用药方式">
                      <el-input
                        v-model="form.medicationPattern"
                        placeholder="请输入用药方式"
                      /> </el-form-item
                  ></el-col>
                </el-row>

                <el-form-item label="项目周期">
                  <el-input
                    type="textarea"
                    v-model="form.projectCycle"
                    :rows="4"
                    placeholder="请输入项目周期"
                  />
                </el-form-item>
                <el-form-item label="可能会出现的不良反应">
                  <el-input
                    type="textarea"
                    v-model="form.adverseReactions"
                    :rows="4"
                    placeholder="请输入可能会出现的不良反应"
                  />
                </el-form-item>

                <!-- 体检项目 -->
                <el-form-item label="体检项目">
                  <el-input
                    type="textarea"
                    v-model="form.medicalExaminationItems"
                    :rows="4"
                    placeholder="请输入体检项目"
                  />
                </el-form-item>
                <el-form-item v-if="isSite" label="科室">
                  <el-select v-model="form.deptId" placeholder="请选择科室">
                    <el-option
                      v-for="item in deptList"
                      :key="item.id"
                      :label="item.deptName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>

                <div class="title"
                  >关键入选标准
                  <span class="sub-title">
                    我们将通过以下条件，为您推荐合适的志愿者，请尽量详细填写
                  </span>
                </div>
                <!-- 性别要求 -->
                <el-form-item
                  label="性别"
                  required
                  label-position="left"
                  label-width="60px"
                >
                  <el-button
                    :class="{ active: form.sex == 1 }"
                    @click="
                      form.sex = 1;
                      form.ageSetDto.ageSetType = 1;
                      form.bmiSetDto.bmiSetType = 1;
                    "
                  >
                    男性
                  </el-button>
                  <el-button
                    :class="{ active: form.sex == 2 }"
                    @click="
                      form.sex = 2;
                      form.ageSetDto.ageSetType = 1;
                      form.bmiSetDto.bmiSetType = 1;
                    "
                  >
                    女性
                  </el-button>
                  <el-button
                    :class="{ active: form.sex == 3 }"
                    @click="form.sex = 3"
                  >
                    男女不限
                  </el-button>
                </el-form-item>
                <el-form-item
                  label="年龄"
                  required
                  label-position="left"
                  label-width="60px"
                >
                  <template v-if="form.sex == 3">
                    <el-button
                      :class="{ active: form.ageSetDto.ageSetType == 1 }"
                      @click="form.ageSetDto.ageSetType = 1"
                    >
                      男女一致
                    </el-button>
                    <el-button
                      style="margin-right: 12px"
                      :class="{ active: form.ageSetDto.ageSetType == 2 }"
                      @click="form.ageSetDto.ageSetType = 2"
                    >
                      男女不同
                    </el-button>
                  </template>
                  <el-row v-if="form.ageSetDto.ageSetType == 1" :gutter="20">
                    <el-col :span="11">
                      <el-input
                        v-model="form.ageSetDto.ageLowerLimit"
                        type="number"
                        placeholder="年龄下限（包含）"
                      />
                    </el-col>
                    ~
                    <el-col :span="11">
                      <el-input
                        v-model="form.ageSetDto.ageUpperLimit"
                        type="number"
                        placeholder="年龄上限（包含）"
                      />
                    </el-col>
                  </el-row>
                  <el-row
                    v-if="form.ageSetDto.ageSetType == 2"
                    :gutter="20"
                    style="margin-top: 10px"
                  >
                    <el-col :span="5">
                      <el-input
                        v-model="form.ageSetDto.maleAgeLowerLimit"
                        type="number"
                        placeholder="男性年龄下限（包含）"
                      />
                    </el-col>
                    ~
                    <el-col :span="5">
                      <el-input
                        v-model="form.ageSetDto.maleAgeUpperLimit"
                        type="number"
                        placeholder="男性年龄上限（包含）"
                      />
                    </el-col>
                    <el-col :span="5">
                      <el-input
                        v-model="form.ageSetDto.femaleAgeLowerLimit"
                        type="number"
                        placeholder="女性年龄下限（包含）"
                      />
                    </el-col>
                    ~
                    <el-col :span="5">
                      <el-input
                        v-model="form.ageSetDto.femaleAgeUpperLimit"
                        type="number"
                        placeholder="女性年龄上限（包含）"
                      />
                    </el-col>
                  </el-row>
                </el-form-item>
                <!-- BMI -->
                <el-form-item
                  label="BMI"
                  required
                  label-position="left"
                  label-width="60px"
                >
                  <template v-if="form.sex == 3">
                    <el-button
                      :class="{ active: form.bmiSetDto.bmiSetType == 1 }"
                      @click="form.bmiSetDto.bmiSetType = 1"
                    >
                      男女一致
                    </el-button>
                    <el-button
                      style="margin-right: 12px"
                      :class="{ active: form.bmiSetDto.bmiSetType == 2 }"
                      @click="form.bmiSetDto.bmiSetType = 2"
                    >
                      男女不同
                    </el-button>
                  </template>
                  <el-row v-if="form.bmiSetDto.bmiSetType == 1" :gutter="20">
                    <el-col :span="11">
                      <el-input
                        v-model="form.bmiSetDto.bmiLowerLimit"
                        type="number"
                        placeholder="BMI下限（包含）"
                      />
                    </el-col>
                    ~
                    <el-col :span="11">
                      <el-input
                        v-model="form.bmiSetDto.bmiUpperLimit"
                        type="number"
                        placeholder="BMI上限（包含）"
                      />
                    </el-col>
                  </el-row>
                  <el-row
                    v-if="form.bmiSetDto.bmiSetType == 2"
                    :gutter="20"
                    style="margin-top: 10px"
                  >
                    <el-col :span="5">
                      <el-input
                        v-model="form.bmiSetDto.maleBmiLowerLimit"
                        type="number"
                        placeholder="男性BMI下限（包含）"
                      />
                    </el-col>
                    ~
                    <el-col :span="5">
                      <el-input
                        v-model="form.bmiSetDto.maleBmiUpperLimit"
                        type="number"
                        placeholder="男性BMI上限（包含）"
                      />
                    </el-col>
                    <el-col :span="5">
                      <el-input
                        v-model="form.bmiSetDto.femaleBmiLowerLimit"
                        type="number"
                        placeholder="女性BMI下限（包含）"
                      />
                    </el-col>
                    ~
                    <el-col :span="5">
                      <el-input
                        v-model="form.bmiSetDto.femaleBmiUpperLimit"
                        type="number"
                        placeholder="女性BMI上限（包含）"
                      />
                    </el-col>
                  </el-row>
                </el-form-item>
                <el-form-item label="其他关键入选要求" prop="keyRequirements">
                  <el-input
                    type="textarea"
                    v-model="form.keyRequirements"
                    :rows="4"
                    placeholder="请输入其他关键入选标准，尽量不要包含年龄、性别、BMI"
                  />
                </el-form-item>
                <div class="title"
                  >试验获益及奖励
                  <span class="sub-title">
                    我们将通过以下条件，为您推荐合适的志愿者，请尽量详细填写
                  </span>
                </div>
                <el-form-item
                  label="营养补贴"
                  required
                  label-position="left"
                  label-width="100px"
                >
                  <el-button
                    :class="{ active: form.trialSubsidyType == 1 }"
                    @click="form.trialSubsidyType = 1"
                  >
                    固定值
                  </el-button>
                  <el-button
                    :class="{ active: form.trialSubsidyType == 2 }"
                    @click="form.trialSubsidyType = 2"
                  >
                    区间值
                  </el-button>
                </el-form-item>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="试验获益金额">
                      <template v-if="form.trialSubsidyType == 1">
                        <el-input
                          v-model="form.subsidyAmount"
                          type="number"
                          placeholder="请输入试验获益金额(元)"
                        />
                      </template>
                      <template v-if="form.trialSubsidyType == 2">
                        <el-row :gutter="20">
                          <el-col :span="11">
                            <el-input
                              v-model="form.trialLowestAmount"
                              type="number"
                              placeholder="获益金额最低(元)"
                            />
                          </el-col>
                          ~
                          <el-col :span="11">
                            <el-input
                              v-model="form.trialMaxAmount"
                              type="number"
                              placeholder="获益金额最高(元)"
                            />
                          </el-col>
                        </el-row>
                      </template>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12"
                    ><el-form-item label="推荐奖励">
                      <el-input
                        v-model="form.referralReward"
                        show-word-limit
                        type="number"
                        placeholder="如果有推荐奖励，请输入整数，不支持小数(元)"
                        :maxlength="10"
                        @input="handleInput($event, 'referralReward')"
                      /> </el-form-item
                  ></el-col>
                  <el-col :span="12" v-if="!isSite"
                    ><el-form-item label="自主参与奖励">
                      <el-input
                        v-model="form.oneselfRecomAward"
                        show-word-limit
                        type="number"
                        placeholder="如果有自主参与奖励，请输入整数，不支持小数(元)"
                        :maxlength="10"
                        @input="handleInput($event, 'oneselfRecomAward')"
                      /> </el-form-item
                  ></el-col>
                </el-row>
                <el-form-item label="营养补贴描述">
                  <el-input
                    type="textarea"
                    v-model="form.subsidyDesc"
                    :rows="4"
                    placeholder="请输入可以描述试验获益的相关组成以及发放要求和节点等"
                  />
                </el-form-item>

                <!-- 提交按钮 -->
                <el-form-item>
                  <div>
                    <!-- <el-button @click="previewProject()">预览</el-button> -->
                    <el-button
                      type="warning"
                      :loading="loading"
                      @click="submit(1)"
                      >保存草稿</el-button
                    >
                    <el-button
                      type="primary"
                      :loading="loading"
                      @click="submit(2)"
                    >
                      发布
                    </el-button>
                  </div>
                </el-form-item>
              </div>
            </el-form>
          </template>
          <template v-if="form.projectRecruitType == 2">
            <el-form
              ref="patientFormRef"
              :model="patientForm"
              :rules="patientRules"
              label-width="180px"
              :label-position="labelPosition"
              style="width: 70%; margin-left: 110px"
            >
              <!-- 项目招募名称 -->
              <el-form-item label="项目招募名称" prop="projectRecruitName">
                <el-input
                  v-model="patientForm.projectRecruitName"
                  maxlength="100"
                  show-word-limit
                  placeholder="请输入项目招募名称"
                />
              </el-form-item>
              <!-- 招募中心和截止日期 -->
              <div>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="招募中心" prop="siteIdList">
                      <el-select
                        v-model="patientForm.siteIdList"
                        clearable
                        filterable
                        multiple
                        :disabled="isSite"
                        placeholder="请选择招募中心"
                      >
                        <el-option
                          v-for="item in centers"
                          :key="item.siteId"
                          :label="item.siteName"
                          :value="item.siteId"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label="招募截止日期"
                      prop="projectRecruitDeadline"
                      required
                    >
                      <el-date-picker
                        v-model="patientForm.projectRecruitDeadline"
                        type="date"
                        value-format="YYYY-MM-DD"
                        style="width: 100%"
                        placeholder="选择招募截止日期"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <!-- 项目名称 方案编号 -->
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="项目名称" prop="projectName" required>
                    <el-input
                      v-model="patientForm.projectName"
                      maxlength="100"
                      show-word-limit
                      placeholder="请输入项目名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="方案编号" prop="schemeNumber" required>
                    <el-input
                      v-model="patientForm.schemeNumber"
                      maxlength="100"
                      show-word-limit
                      placeholder="请输入方案编号"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <!-- 项目编号 申办方 -->
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="项目编号" prop="projectNumber" required>
                    <el-input
                      v-model="patientForm.projectNumber"
                      maxlength="100"
                      show-word-limit
                      placeholder="请输入项目编号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="申办方"
                    prop="sponsor"
                    :rules="{
                      type: 'object',
                      required: true,
                      message: '请选择申办方'
                    }"
                  >
                    <div
                      class="flex items-center justify-between"
                      style="width: 100%"
                    >
                      <CompanySelect v-model="patientForm.sponsor" />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="title"
                >项目介绍
                <span class="sub-title">
                  可以通过项目介绍向志愿者关心的风险和参与试验内容进行介绍，提高志愿者的报名意愿
                </span>
              </div>
              <el-form-item
                label="试验分期"
                required
                label-position="left"
                label-width="100px"
              >
                <el-button
                  :class="{ active: patientForm.trialStage == 1 }"
                  @click="patientForm.trialStage = 1"
                >
                  I 期
                </el-button>
                <el-button
                  :class="{ active: patientForm.trialStage == 2 }"
                  @click="patientForm.trialStage = 2"
                >
                  II 期
                </el-button>
                <el-button
                  :class="{ active: patientForm.trialStage == 3 }"
                  @click="patientForm.trialStage = 3"
                >
                  III 期
                </el-button>
                <el-button
                  :class="{ active: patientForm.trialStage == 4 }"
                  @click="patientForm.trialStage = 4"
                >
                  IV 期
                </el-button>
              </el-form-item>
              <el-row :gutter="20">
                <el-col :span="12"
                  ><el-form-item label="适应症" prop="indications" required>
                    <el-input
                      v-model="patientForm.indications"
                      maxlength="100"
                      show-word-limit
                      placeholder="请输入适应症"
                    /> </el-form-item
                ></el-col>
                <el-col :span="12"
                  ><el-form-item label="治疗组试验用药" prop="drugName">
                    <el-input
                      v-model="patientForm.drugName"
                      placeholder="请输入试验用药"
                    /> </el-form-item
                ></el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="疾病" prop="disease">
                    <el-cascader
                      v-model="patientForm.disease"
                      placeholder="请输入关键字搜索"
                      :options="diseaseOptions"
                      :props="propsOptions"
                      :show-all-levels="false"
                      :max-collapse-tags="10"
                      style="width: 100%"
                      filterable
                      clearable
                      collapse-tags
                      collapse-tags-tooltip
                      @change="onChangeDisease"
                    ></el-cascader>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="项目周期">
                <el-input
                  type="textarea"
                  v-model="patientForm.projectCycle"
                  :rows="4"
                  placeholder="请输入项目周期"
                />
              </el-form-item>

              <el-form-item v-if="isSite" label="科室" prop="deptId">
                <el-select
                  v-model="patientForm.deptId"
                  placeholder="请选择科室"
                >
                  <el-option
                    v-for="item in deptList"
                    :key="item.id"
                    :label="item.deptName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <div class="title"
                >关键入选标准
                <span class="sub-title">
                  我们将通过以下条件，为您推荐合适的志愿者，请尽量详细填写
                </span>
              </div>
              <el-form-item label="关键入选要求" prop="keyRequirements">
                <el-input
                  type="textarea"
                  v-model="patientForm.keyRequirements"
                  :rows="4"
                  placeholder="请输入关键入选标准"
                />
              </el-form-item>
              <div class="title"
                >试验获益及奖励
                <span class="sub-title">
                  我们将通过以下条件，为您推荐合适的志愿者，请尽量详细填写
                </span>
              </div>
              <el-row :gutter="20">
                <el-col :span="12"
                  ><el-form-item label="推荐奖励">
                    <el-input
                      v-model="patientForm.referralReward"
                      show-word-limit
                      type="number"
                      placeholder="如果有推荐奖励，请输入整数，不支持小数(元)"
                      :maxlength="10"
                      @input="handleInput($event, 'referralReward')"
                    /> </el-form-item
                ></el-col>
                <el-col :span="12" v-if="!isSite"
                  ><el-form-item label="自主参与奖励">
                    <el-input
                      v-model="patientForm.oneselfRecomAward"
                      show-word-limit
                      type="number"
                      placeholder="如果有自主参与奖励，请输入整数，不支持小数(元)"
                      :maxlength="10"
                      @input="handleInput($event, 'oneselfRecomAward')"
                    /> </el-form-item
                ></el-col>
              </el-row>
              <el-form-item label="营养补贴描述">
                <el-input
                  type="textarea"
                  v-model="patientForm.subsidyDesc"
                  :rows="4"
                  placeholder="请输入可以描述试验获益的相关组成以及发放要求和节点等"
                />
              </el-form-item>
              <!-- 提交按钮 -->
              <el-form-item>
                <div>
                  <!-- <el-button @click="previewProject()">预览</el-button> -->
                  <el-button
                    type="warning"
                    :loading="loading"
                    @click="patientSubmit(1)"
                    >保存草稿</el-button
                  >
                  <el-button
                    type="primary"
                    :loading="loading"
                    @click="patientSubmit(2)"
                  >
                    发布
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </template>
        </div>
      </div>
    </ele-card>
    <el-dialog
      v-model="visibleDialog"
      align-center
      title=""
      width="500"
      :show-close="false"
    >
      <!-- 提示文案 -->
      <div style="text-align: center; margin-top: 80px">
        <el-icon class="iconS"><SuccessFilled /></el-icon>
        <div style="color: #4b5563; font-size: 16px">提交成功</div>
        <p style="color: #909399"
          >您的项目已提交平台审核，在审核通过后会发布成功，请您耐心等待</p
        >
      </div>
      <template #footer>
        <div style="text-align: center">
          <el-button @click="toProjectList()"> 返回项目列表 </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="AIAgentDialogVisible"
      title="AI文本识别"
      width="800"
      :show-close="false"
    >
      <div style="display: flex">
        <span class="title">识别内容：</span>
        <el-input
          v-model="AIAcceptContent"
          placeholder="请输入识别内容"
          type="textarea"
          style="flex: 1"
          :rows="10"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            style="padding: 0 30px"
            type="primary"
            :loading="AILoading"
            @click="AIParse()"
          >
            AI识别
          </el-button>
          <el-button @click="AIAgentDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </ele-page>
</template>
<script setup name="AddRecruitMent">
  import { ref, reactive, onMounted, computed } from 'vue';
  import CompanySelect from '@/views/project/recruitment/CompanySelect.vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import {
    addRcruitProject,
    getSiteSelectList,
    getAllDiseaseInfo,
    parseProjectDetails
  } from '@/api/project/index';
  import { searchSiteDeptInfoList } from '@/api/department/index';
  import { SuccessFilled } from '@element-plus/icons-vue';
  import AIAgent from '@/assets/AIAgent.png';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';

  const router = useRouter();

  const { isSite } = useUserStore();

  const { push } = useRouter();

  /** 提交状态 */
  const loading = ref(false);

  const AILoading = ref(false);

  /** 是否是修改 */
  const isUpdate = ref(false);

  const AIAcceptContent = ref('');

  const AIAgentDialogVisible = ref(false);

  const propsOptions = {
    value: 'value',
    children: 'children',
    label: 'label',
    emitPath: false,
    multiple: true
  };

  const diseaseOptions = ref([]);

  /** 表单实例 */
  const formRef = ref(null);

  const patientFormRef = ref(null);

  const labelPosition = ref('top');

  const visibleDialog = ref(false);

  const centers = ref([]);

  /** 表单验证规则 */
  const patientRules = reactive({
    disease: [
      { required: true, message: '请选择疾病', trigger: ['blur', 'change'] }
    ],
    projectRecruitName: [
      {
        required: true,
        message: '请输入项目招募名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    siteIdList: [
      {
        required: true,
        message: '请选择招募中心',
        trigger: ['change', 'blur']
      }
    ],
    projectRecruitDeadline: [
      {
        required: true,
        message: '选择招募截止日期',
        type: 'string',
        trigger: 'change'
      }
    ],
    drugName: [
      {
        required: true,
        message: '请输入试验用药',
        type: 'string',
        trigger: 'blur'
      }
    ],
    indications: [
      {
        required: true,
        message: '请输入适应症',
        type: 'string',
        trigger: 'blur'
      }
    ],
    projectName: [
      {
        required: true,
        message: '请输入项目名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    schemeNumber: [
      {
        required: true,
        message: '请输入方案编号',
        type: 'string',
        trigger: 'blur'
      }
    ],
    projectNumber: [
      {
        required: true,
        message: '请输入项目编号',
        type: 'string',
        trigger: 'blur'
      }
    ],
    keyRequirements: [
      {
        required: true,
        message: '请输入关键入选标准',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 表单验证规则 */
  const rules = reactive({
    projectRecruitName: [
      {
        required: true,
        message: '请输入项目招募名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    deptId: [
      {
        required: true,
        message: '请选择科室',
        trigger: ['change', 'blur']
      }
    ],
    siteIdList: [
      {
        required: true,
        message: '请选择招募中心',
        trigger: ['change', 'blur']
      }
    ],
    projectRecruitDeadline: [
      {
        required: true,
        message: '选择招募截止日期',
        type: 'string',
        trigger: 'change'
      }
    ],
    planScreeningDate: [
      {
        required: true,
        message: '请选择计划筛选日期',
        type: 'string',
        trigger: 'change'
      }
    ],
    drugName: [
      {
        required: true,
        message: '请输入药物名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    medicationPattern: [
      {
        required: true,
        message: '请输入试验用药',
        type: 'string',
        trigger: 'blur'
      }
    ],
    // indications: [
    //   {
    //     required: true,
    //     message: '请输入适应症',
    //     type: 'string',
    //     trigger: 'blur'
    //   }
    // ],
    keyRequirements: [
      {
        required: true,
        message: '请输入其他关键入选标准，尽量不要包含年龄、性别、BMI',
        type: 'string',
        trigger: 'blur'
      }
    ],
    projectName: [
      {
        required: true,
        message: '请输入项目名称',
        type: 'string',
        trigger: 'blur'
      }
    ],
    schemeNumber: [
      {
        required: true,
        message: '请输入方案编号',
        type: 'string',
        trigger: 'blur'
      }
    ],
    projectNumber: [
      {
        required: true,
        message: '请输入项目编号',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  const [patientForm, patientResetFields, patientAssignFields] = useFormData({
    deptId: null,
    siteIdList: [],
    projectRecruitType: 2,
    projectRecruitName: '',
    center: '',
    projectRecruitDeadline: '',
    trialStage: 1,
    planScreeningDate: '',
    drugName: '',
    indications: '',
    medicationPattern: '',
    projectCycle: '',
    adverseReactions: '',
    medicalExaminationItems: '',
    sex: 1,
    keyRequirements: '',
    trialSubsidyType: 1,
    subsidyDesc: '',
    subsidyAmount: null,
    maleSubsidyAmount: null,
    femaleSubsidyAmount: null,
    oneselfRecomAward: null,
    projectName: '',
    schemeNumber: '',
    projectNumber: '',
    sponsorId: null,
    sponsorName: '',
    sponsor: null,
    disease: '',
    diseaseDto: []
  });

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    deptId: null,
    siteIdList: [],
    projectRecruitType: 1,
    projectRecruitName: '',
    center: '',
    projectRecruitDeadline: '',
    intervalDays: 1,
    planScreeningDate: '',
    drugName: '',
    indications: '',
    medicationPattern: '',
    projectCycle: '',
    adverseReactions: '',
    medicalExaminationItems: '',
    sex: 1,
    ageSetDto: {
      ageSetType: 1,
      ageLowerLimit: null,
      ageUpperLimit: null,
      maleAgeLowerLimit: null,
      maleAgeUpperLimit: null,
      femaleAgeLowerLimit: null,
      femaleAgeUpperLimit: null
    },
    bmiSetDto: {
      bmiSetType: 1,
      bmiLowerLimit: null,
      bmiUpperLimit: null,
      maleBmiLowerLimit: null,
      maleBmiUpperLimit: null,
      femaleBmiLowerLimit: null,
      femaleBmiUpperLimit: null
    },
    keyRequirements: '',
    trialSubsidyType: 1,
    subsidyDesc: '',
    subsidyAmount: null,
    trialLowestAmount: null,
    trialMaxAmount: null,
    projectName: '',
    schemeNumber: '',
    projectNumber: '',
    sponsorId: null,
    sponsorName: '',
    sponsor: null,
    oneselfRecomAward: null,
    meshScreenDto: {
      networkingType: 1,
      dataList: [
        {
          id: 1,
          select: [],
          checked: false,
          month: null
        },
        {
          id: 2,
          select: [],
          checked: false,
          month: null
        }
      ]
    }
  });

  /** AI Agent 识别弹窗 */
  const AIAgentText = () => {
    AIAgentDialogVisible.value = true;
  };

  /** AI Agent 识别 */
  const AIParse = async () => {
    try {
      if (!AIAcceptContent.value) {
        EleMessage.error('请输入AI识别内容！');
        return;
      }
      AILoading.value = true;
      const params = { schemeText: AIAcceptContent.value };
      const res = await parseProjectDetails(params);
      console.log(res);
      AILoading.value = false;
      EleMessage.success('识别成功！');
      AIAgentDialogVisible.value = false;
      // Object.assign(form, res?.data);
      if (form.projectRecruitType == 1) {
        Object.entries(res?.data || {}).forEach(([key, value]) => {
          if (value && value !== null && value !== undefined) {
            form[key] = value;
          }
        });
      } else {
        Object.entries(res?.data || {}).forEach(([key, value]) => {
          if (value && value !== null && value !== undefined) {
            patientForm[key] = value;
          }
        });
      }
      console.log('合并之后的数据：', form);
      console.log('合并之后的数据：', patientForm);
    } catch (e) {
      AILoading.value = false;
      EleMessage.error(e.message);
    }
  };

  /** 保存草稿 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value ? updateConfig : addRcruitProject;
      const params = {
        ...form,
        multipleSiteFlag: form?.siteIdList?.length > 1 ? 2 : 1
      };
      saveOrUpdate(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  const handleInput = (val, name) => {
    if (val == '' || val < 0) {
      form[name] = '';
      patientForm[name] = '';
      return;
    }
    let integerValue = Math.round(Number(val));
    if (form.projectRecruitType == 1) {
      form[name] = integerValue || '';
    } else {
      patientForm[name] = integerValue || '';
    }
  };

  const handleInputMonth = (val, type) => {
    if (val == '' || val < 0) {
      if (type == 1) {
        form.meshScreenDto.dataList[0].month = '';
      } else {
        form.meshScreenDto.dataList[1].month = '';
      }
      return;
    }
    let integerValue = Math.round(Number(val));
    if (type == 1) {
      form.meshScreenDto.dataList[0].month = integerValue || '';
    } else {
      form.meshScreenDto.dataList[1].month = integerValue || '';
    }
  };

  const toProjectList = () => {
    const path = '/recruitProject';
    router.push({
      path,
      query: {
        currentStatus: 'zhaomu'
      }
    });
  };

  /** 预览 */
  const previewProject = () => {};

  const showBackToProjectListDialog = () => {
    visibleDialog.value = true;
  };

  const validateData = () => {
    if (form.sex == 1 || form.sex == 2) {
      // BMI
      form.bmiSetDto.maleBmiLowerLimit = null;
      form.bmiSetDto.maleBmiUpperLimit = null;
      form.bmiSetDto.femaleBmiLowerLimit = null;
      form.bmiSetDto.femaleBmiUpperLimit = null;
      // 年龄
      form.ageSetDto.maleAgeLowerLimit = null;
      form.ageSetDto.maleAgeUpperLimit = null;
      form.ageSetDto.femaleAgeLowerLimit = null;
      form.ageSetDto.femaleAgeUpperLimit = null;
    }

    if (form.sex == 3) {
      if (form.ageSetDto.ageSetType == 1) {
        form.ageSetDto.maleAgeLowerLimit = null;
        form.ageSetDto.maleAgeUpperLimit = null;
        form.ageSetDto.femaleAgeLowerLimit = null;
        form.ageSetDto.femaleAgeUpperLimit = null;
      } else {
        form.ageSetDto.ageLowerLimit = null;
        form.ageSetDto.ageUpperLimit = null;
      }

      if (form.bmiSetDto.bmiSetType == 1) {
        form.bmiSetDto.maleBmiLowerLimit = null;
        form.bmiSetDto.maleBmiUpperLimit = null;
        form.bmiSetDto.femaleBmiLowerLimit = null;
        form.bmiSetDto.femaleBmiUpperLimit = null;
      } else {
        form.bmiSetDto.bmiLowerLimit = null;
        form.bmiSetDto.bmiUpperLimit = null;
      }
    }
  };

  function validateMeshScreen(meshScreenDto) {
    if (meshScreenDto.networkingType == 1) {
      let valid = true;
      let hasValidItem = false;
      meshScreenDto.dataList.forEach((item) => {
        const hasSelectValue =
          item.select && item.select != '' && item.select.length > 0;
        const hasValue =
          item.month != null && item.month != undefined && item.month != '';
        if (hasSelectValue && hasValue) {
          item.checked = true;
          hasValidItem = true;
        } else if (!hasSelectValue && !hasValue) {
          item.checked = false;
        } else {
          item.checked = false;
          valid = false;
        }
      });
      // 如果有错误信息，提示错误
      if (!valid || !hasValidItem) {
        EleMessage.error('间隔天数填写错误或未填写，请确认后提交！');
        return false;
      }
    } else {
      meshScreenDto.dataList = [
        {
          id: 1,
          select: [],
          checked: false,
          month: null
        },
        {
          id: 2,
          select: [],
          checked: false,
          month: null
        }
      ];
    }
    return true; // 返回验证成功
  }

  /** 健康提交发布 */
  const submit = (type) => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      const isValid = validateMeshScreen(form.meshScreenDto);
      if (!isValid) return;
      loading.value = true;
      validateData();
      const saveOrUpdate = isUpdate.value ? updateConfig : addRcruitProject;
      const params = {
        ...form,
        siteIdList: [form?.siteIdList],
        recruitStatus: type,
        sponsorId: form?.sponsor?.companyId,
        sponsorName: form?.sponsor?.companyName
      };
      saveOrUpdate(params)
        .then((res) => {
          loading.value = false;
          if (type == 2) {
            showBackToProjectListDialog();
          } else {
            EleMessage.success(res?.msg);
          }
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e);
        });
    });
  };

  /** 患者提交发布 */
  const patientSubmit = (type) => {
    patientFormRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const saveOrUpdate = isUpdate.value ? updateConfig : addRcruitProject;
      const params = {
        ...patientForm,
        multipleSiteFlag: patientForm?.siteIdList?.length > 1 ? 2 : 1,
        recruitStatus: type,
        sponsorId: patientForm?.sponsor?.companyId,
        sponsorName: patientForm?.sponsor?.companyName
      };
      saveOrUpdate(params)
        .then((res) => {
          loading.value = false;
          if (type == 2) {
            showBackToProjectListDialog();
          } else {
            EleMessage.success(res?.msg);
          }
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e);
        });
    });
  };

  const getOptions = async () => {
    try {
      const params = { isSite: isSite };
      const res = await getSiteSelectList(params);
      centers.value = res;
      if (isSite) {
        form.siteIdList = centers.value[0]?.siteId;
        patientForm.siteIdList = [centers.value[0]?.siteId];
      }
    } catch (e) {
      EleMessage.error(e.message);
    }
  };

  const getDiseaseOptions = async () => {
    try {
      const res = await getAllDiseaseInfo();
      diseaseOptions.value = res;
    } catch (e) {
      EleMessage.error(e.message);
    }
  };

  const findItemById = (options, id) => {
    for (let option of options) {
      if (option.value == id) {
        return option;
      }
      if (option.children) {
        const found = findItemById(option.children, id);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  const getItemsByValues = (values) => {
    let items = [];
    for (let value of values) {
      const foundItem = findItemById(diseaseOptions.value, value);
      if (foundItem) {
        items.push(foundItem);
      } else {
        return [];
      }
    }
    return items; // 返回所有找到的对象
  };

  const onChangeDisease = (value) => {
    patientForm.diseaseDto = getItemsByValues(value);
    console.log('#', patientForm.diseaseDto);
  };

  const deptList = ref([]);
  const getDeptOptions = async () => {
    try {
      const res = await searchSiteDeptInfoList();
      deptList.value = res;
    } catch (e) {
      EleMessage.error(e.message);
    }
  };

  onMounted(() => {
    getOptions();
    getDiseaseOptions();
    getDeptOptions();
  });
</script>
<style lang="scss" scoped>
  .active {
    background-color: #5280fb; /* 选中按钮的背景色 */
    color: white; /* 选中按钮的文字颜色 */
  }
  .title {
    font-weight: 700;
    font-size: 24px;
    color: #374151;
    line-height: 32px;
    margin-bottom: 12px;
  }

  .sub-title {
    font-weight: 700;
    font-size: 14px;
    color: #6b7280;
  }

  .ss-title {
    margin-right: 16px;
    font-size: 16px;
    color: #4c5c82;
  }

  .iconS {
    font-size: 63px;
    color: #67c23a;
  }
</style>
