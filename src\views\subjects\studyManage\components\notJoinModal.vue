<!-- 编辑弹窗 -->
<template>
  <!-- 添加自定义类名 custom-center-modal -->
  <ele-modal
    class="custom-center-modal"
    form
    :width="660"
    :model-value="modelValue"
    title="标记未入组"
    @update:modelValue="updateModelValue"
    align-center
  >
    <template #header>
      <div style="position: relative">
        <span>标记未入组</span>
        <!-- <span
          style="
            font-size: 14px;
            color: #faad14;
            font-weight: 500;
            position: absolute;
            bottom: 0;
            right: 15px;
          "
        >
          仅支持标记“筛选”和“待入组”的数据
        </span> -->
      </div>
    </template>
    <div class="modal-table-dialog error">
      <div style="display: flex; align-items: center; margin-bottom: 8px">
        <div style="font-size: 16px">批量填充</div>
        <el-input
          v-model="remark"
          placeholder="请输入未入组原因"
          clearable
          maxlength="150"
          style="flex: 1; margin: 0 10px"
        />
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
      <el-form :model="ruleForm" ref="formRule">
        <el-table
          :data="ruleForm.tableData"
          :header-cell-style="{ background: '#eeeeee' }"
          height="50vh"
          @selection-change="handleSelectionChange"
          ref="tableRef"
        >
          <template #empty>
            <el-empty description="暂无数据" />
          </template>
          <el-table-column type="selection" width="65" />
          <el-table-column
            prop="name"
            label="（筛选号）姓名"
            width="200"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div
                style="
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
              >
                <span v-if="row.filterno">（{{ row.filterno }}）</span
                >{{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="未入组原因">
            <template #header>
              <span style="color: #b90000; margin-right: 3px">*</span>未入组原因
            </template>
            <template #default="{ row, $index }">
              <el-form-item
                :prop="`tableData.${$index}.remark`"
                :rules="[
                  {
                    required: true,
                    validator: validatorRule,
                    trigger: ['blur', 'change']
                  }
                ]"
              >
                <el-input
                  v-model="row.remark"
                  placeholder="请输入未入组原因"
                  clearable
                  style="width: 100%"
                  show-word-limit
                  maxlength="150"
                />
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit"
          >确 定</el-button
        >
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { markNotJoinGroup } from '@/api/subjects/studyManage/index.js';
  import { Warning } from '@element-plus/icons-vue';

  const emit = defineEmits(['done', 'update:modelValue']);
  const remark = ref('');
  import { ElMessageBox } from 'element-plus';

  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Array
  });

  const loading = ref(false);

  const close = () => {
    updateModelValue(false);
  };
  const formRule = ref(null);
  const validatorRule = (rule, value, callback) => {
    const parts = rule.field.split('.');
    const index = parts[1];
    if (
      !value &&
      selectionsList.value.some(
        (val) => val.guid == ruleForm.value.tableData[index].guid
      )
    ) {
      callback(new Error('请输入未入组原因'));
      return;
    }
    callback();
  };
  const submit = () => {
    if (selectionsList.value.length == 0) {
      EleMessage.warning('请至少勾选一条数据再确认');
      return;
    }
    formRule.value.validate((valid) => {
      if (valid) {
        const data = selectionsList.value.map((val) => {
          return {
            guid: val.guid,
            filterno: ruleForm.value.tableData.find(
              (item) => item.guid == val.guid
            ).filterno,
            remarks: ruleForm.value.tableData.find(
              (item) => item.guid == val.guid
            ).remark
          };
        });
        ElMessageBox.confirm(
          `是否确定将【${selectionsList.value
            .map((val) => val.name)
            .join()}】标记为未入组?`,
          '提示',
          {
            confirmButtonText: '确 定',
            cancelButtonText: '取 消',
            center: true
          }
        ).then(() => {
          loading.value = true;
          markNotJoinGroup(data).then((res) => {
            loading.value = false;
            if (res.code == 200) {
              EleMessage.success('标记成功');
              emit('done');
              updateModelValue(false);
              return;
            }
            EleMessage.error(res.msg);
          });
        });

        return;
      }
    });
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const ruleForm = ref({ tableData: [] });
  const selectionsList = ref([]);
  const tableRef = ref(null);
  nextTick(async () => {
    ruleForm.value.tableData = JSON.parse(JSON.stringify(props.data));
    ruleForm.value.tableData.forEach((item) => {
      setTimeout(() => {
        tableRef.value.toggleRowSelection(item, true);
      });
    });
  });
  const handleSelectionChange = (val) => {
    selectionsList.value = val;
    ruleForm.value.tableData.forEach((item, index) => {
      if (!selectionsList.value.some((val) => val.guid == item.guid)) {
        formRule.value.validateField(`tableData.${index}.remark`);
      }
    });
  };
  const save = () => {
    ruleForm.value.tableData.forEach((item, index) => {
      if (selectionsList.value.some((val) => val.guid == item.guid)) {
        item.remark = remark.value;
      }
      if (selectionsList.value.some((val) => val.guid == item.guid)) {
        formRule.value.validateField(`tableData.${index}.remark`);
      }
    });
  };
</script>
<style>
  .modal-dialog .el-table .cell {
    overflow: initial !important;
  }
</style>
