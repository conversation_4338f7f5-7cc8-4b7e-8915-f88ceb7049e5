<template>
  <ele-page flex-table>
    <ele-pro-table
      ref="tableRef"
      v-model:current="current"
      v-model:selections="selections"
      row-key="applyId"
      :columns="columns"
      :datasource="datasource"
      :toolbar="{ theme: tableHeader }"
      :bottom-line="tableFullHeight"
      highlight-current-row
      flex-table
    >
      <template #toolbar>
        <div class="d-flex">
          <div style="margin-bottom: 8px">
            <el-button
              v-if="![7, 8].includes(props.model)"
              class="ele-btn-icon"
              :icon="PlusOutlined"
              :disabled="selections.length == 0"
              @click="passBatch()"
            >
              批量通过
            </el-button>
            <el-button
              v-if="![7, 8].includes(props.model)"
              class="ele-btn-icon hidden-sm-and-down"
              :icon="DeleteOutlined"
              :disabled="selections.length == 0"
              @click="handleDelete()"
            >
              批量淘汰
            </el-button>
          </div>
          <div>
            <el-form inline :model="formParams">
              <el-form-item label="">
                <el-input
                  v-model="formParams.searchValue"
                  placeholder="请输入关键字"
                  clearable
                  style="width: 200px; height: 38px"
                  maxlength="99"
                />
              </el-form-item>
              <el-form-item
                v-if="props.isHealthProject && props.model != 7"
                label="显示BMI异常："
              >
                <el-select
                  v-model="formParams.bmiIsAbnormal"
                  placeholder="请选择"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="是" value="1" />
                  <el-option label="否" value="0" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="props.model != 7" label="性别：">
                <el-select
                  v-model="formParams.sex"
                  placeholder="请选择"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="男" value="0" />
                  <el-option label="女" value="1" />
                </el-select>
              </el-form-item>
              <el-form-item
                v-if="props.isHealthProject && props.model != 7"
                label="是否本地："
              >
                <el-select
                  v-model="formParams.area"
                  placeholder="请选择"
                  clearable
                  style="width: 120px"
                >
                  <el-option label="是" value="0" />
                  <el-option label="否" value="1" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="props.model == 7" label="失败状态：">
                <el-select
                  v-model="formParams.applyStatus"
                  placeholder="请选择"
                  clearable
                  style="width: 160px"
                >
                  <el-option label="全部" value="-1" />
                  <el-option label="审核失败" value="2" />
                  <el-option label="筛选失败" value="4" />
                  <el-option label="签到失败" value="10" />
                  <el-option label="入组失败" value="7" />
                  <el-option label="脱落" value="9" />
                </el-select>
              </el-form-item>
              <el-form-item style="margin-right: 0px !important">
                <el-button type="primary" @click="handleReload">搜索</el-button>
                <el-button
                  type="info"
                  @click="(formParams = {}), handleReload()"
                  >重置</el-button
                >
                <el-button type="info" @click="exportData">
                  导出联系表
                </el-button>
                <el-button
                  v-if="props?.model == 0 && props?.isHealthProject"
                  type="info"
                  @click="exportQuestionnaire"
                >
                  导出问卷
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </template>

      <template #medicalFileVo="{ row }">
        <div>
          <!-- <template
            v-if="
              row?.medicalFileVo && row.medicalFileVo?.medicalRecordType == 2
            "
          >
            <div v-if="row.medicalFileVo.fileDtos.length > 0">
              <el-link
                type="primary"
                @click="getFileUrl(row.medicalFileVo?.fileDtos)"
              >
                查看图片
              </el-link>
            </div>
          </template> -->

          <template v-if="row?.medicalFileVo">
            <el-link type="primary" @click="downloadFile(row)">
              下载病例
            </el-link>
          </template>
        </div>
      </template>
      <template #disease="{ row }">{{ diseaseLabels(row.disease) }}</template>
      <template #sex="{ row }">{{ row?.sex == 0 ? '男' : '女' }}</template>
      <template #questionnaireContent="{ row }">
        <el-link type="primary" @click="goToQuestionnaire(row)"> 问卷 </el-link>
      </template>
      <template #statusName="{ row }">
        <ele-dot v-if="row.statusName == '在研'" text="在研" type="success" />
        <ele-dot
          v-else-if="row.statusName == '未启动'"
          text="未启动"
          type="info"
        />
        <ele-dot
          v-else-if="row.statusName == '其他'"
          text="其他"
          type="default"
        />
        <ele-dot
          v-else-if="row.statusName == '结题/已完成'"
          text="结题/已完成"
          type="danger"
        />
        <ele-dot v-else :text="row.statusName" />
      </template>
      <!-- 操作列 -->
      <template #action="{ row }">
        <template v-if="![7, 8].includes(props.model)">
          <el-link
            type="primary"
            :underline="false"
            @click="showConfirmDialod(row)"
          >
            {{ props.model == 6 ? '出组' : '通过' }}
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="weedOut(row)">
            {{ props.model == 6 ? '脱落' : '淘汰' }}
          </el-link>
          <el-divider
            v-if="
              [1, 2, 3, 5, 6, 7, 8].includes(props.model) ||
              ([0, 1, 2].includes(props.model) && props.isMultipleSite)
            "
            direction="vertical"
          />
        </template>
        <ele-dropdown
          v-if="
            [0, 1, 2].includes(props.model) &&
            props.isMultipleSite &&
            [1, 2, 3, 5, 6, 7, 8].includes(props.model)
          "
          style="padding-top: 2px"
          :items="
            [
              {
                title: '更正',
                command: 'update',
                visible: [1, 2, 3, 5, 6, 7, 8].includes(props.model)
              },
              {
                title: '修改参与中心',
                command: 'modify',
                visible: [0, 1, 2].includes(props.model) && props.isMultipleSite
              }
            ].filter((i) => i.visible)
          "
          @command="(command) => dropClick(command, row)"
        >
          <el-link type="primary" :underline="false">
            <span>更多</span>
          </el-link>
        </ele-dropdown>
        <template v-else>
          <template v-if="[1, 2, 3, 5, 6, 7, 8].includes(props.model)">
            <el-link
              type="primary"
              :underline="false"
              @click="dropClick('update', row)"
            >
              更正
            </el-link>
            <el-divider
              v-if="[0, 1, 2].includes(props.model) && props.isMultipleSite"
              direction="vertical"
            />
          </template>
          <template
            v-if="[0, 1, 2].includes(props.model) && props.isMultipleSite"
          >
            <el-link
              type="primary"
              :underline="false"
              @click="dropClick('modify', row)"
            >
              修改参与中心
            </el-link>
          </template>
        </template>
      </template>
    </ele-pro-table>

    <el-dialog
      v-model="dialogVisible"
      :title="currentTitle"
      width="600"
      align-center
    >
      <div class="d-content">{{ currentContent }}</div>
      <template #footer>
        <div style="text-align: center">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="pass"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
    <QuestionnaireModal
      v-model="showQuestionnaire"
      :data="currentData"
      @done="reload"
    />
    <CorrectModal
      v-model="showCorrect"
      :model="props?.model"
      :applyId="applyId"
      @done="reload"
    />

    <ModifyCenterModal
      v-model="showModifyCenter"
      :model="props?.model"
      :currnetID="currnetID"
      :applyCheckID="applyCheckID"
      @done="reload"
    />
  </ele-page>
</template>

<script setup name="CommonTableList">
  import { ElMessageBox } from 'element-plus';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { computed, ref, h, onMounted, onActivated, watch } from 'vue';
  import dayjs from 'dayjs';
  import weekday from 'dayjs/plugin/weekday';
  import quarterOfYear from 'dayjs/plugin/quarterOfYear';
  import {
    getHealthProjectApplyPageList,
    getDiseaseProjectApplyPageList,
    updateUserApplyStatus,
    exportHealthProjectApply,
    exportDiseaseProjectApplyPageList,
    downloadUserMedicalFile,
    exportQuestionnaireContent
  } from '@/api/recruitment/index';
  import { ElDivider } from 'element-plus';
  import { usePageTab } from '@/utils/use-page-tab';
  import { useRouter } from 'vue-router';
  import QuestionnaireModal from './QuestionnaireModal.vue';
  import CorrectModal from './CorrectModal.vue';
  import ModifyCenterModal from './ModifyCenterModal.vue';
  import ImagePreview from '@/components/ImagePreview';
  import { useUserStore } from '@/store/modules/user';

  const { isSite } = useUserStore();

  const { info } = useUserStore();

  const emit = defineEmits(['done']);

  dayjs.extend(weekday);
  dayjs.extend(quarterOfYear);
  const spacer = h(ElDivider, { direction: 'vertical' });

  const { removePageTab } = usePageTab();

  const { push } = useRouter();

  const formParams = ref({});

  // 表格选中数据
  const selections = ref([]);

  const showModifyCenter = ref(false);

  const currentTitle = ref('');

  const currentContent = ref('');

  const dialogVisible = ref(false);

  const currentData = ref({});

  const showQuestionnaire = ref(false);

  const showCorrect = ref(false);

  //#region 表格样式参数
  const props = defineProps({
    tableHeader: String,
    tableFullHeight: Boolean,
    isHealthProject: Boolean,
    isMultipleSite: Boolean,
    projectId: String,
    projectRecruitName: String,
    siteId: String,
    model: String,
    activeTabName: String
  });

  // 表格实例
  const tableRef = ref(null);

  // 表格单选选中数据
  const current = ref(null);

  // 搜索组件实例
  const searchRef = ref();

  const currnetData = ref({});

  // 表格数据源
  const datasource = ({ page, limit, where, orders, filters }) => {
    if (!props.projectId) {
      console.log('项目ID为空，不请求接口');
      return Promise.resolve([]);
    }
    const API = props.isHealthProject
      ? getHealthProjectApplyPageList
      : getDiseaseProjectApplyPageList;
    return API({
      ...where,
      orderBy: orders.sort,
      isAsc: orders.order,
      ...filters,
      page,
      limit,
      projectId: props.projectId || '',
      siteId: props.siteId || '',
      applyStatus:
        props?.model == 7
          ? formParams.value?.applyStatus
            ? formParams.value?.applyStatus
            : -1
          : props?.model
    });
  };

  // 表格列配置
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        width: 48,
        align: 'center',
        fixed: 'left'
      },
      {
        type: 'index',
        columnKey: 'index',
        label: `序号`,
        width: 70,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'userName',
        label: '姓名',
        minWidth: '120',
        showOverflowTooltip: true
      },
      {
        prop: 'age',
        label: '年龄',
        align: 'center',
        width: '80'
      },
      {
        prop: 'sex',
        label: '性别',
        align: 'center',
        width: '80',
        slot: 'sex'
      },
      {
        prop: 'nation',
        label: '民族',
        minWidth: '120',
        showOverflowTooltip: true
      },
      {
        prop: 'recommendDesc',
        label: '报名类型',
        minWidth: '120',
        showOverflowTooltip: true,
        hideInTable: isSite
      },
      {
        prop: 'referrerName',
        label: '推荐人',
        minWidth: '120',
        showOverflowTooltip: true,
        hideInTable: isSite
      },
      {
        prop: 'area',
        label: '地区',
        width: '180',
        hideInTable: !props.isHealthProject,
        showOverflowTooltip: true
      },
      {
        prop: 'bmi',
        label: 'BMI值',
        width: '80',
        hideInTable: !props.isHealthProject
      },
      // {
      //   prop: 'outJoinIntervalDay',
      //   label: '距上次出组天数',
      //   minWidth: '160',
      //   align: 'center',
      //   sortable: 'custom'
      // },
      {
        prop: 'questionnaireContent',
        label: '问卷',
        minWidth: '100',
        slot: 'questionnaireContent',
        hideInTable: !props.isHealthProject
      },
      {
        prop: 'disease',
        label: '疾病',
        minWidth: '180',
        slot: 'disease',
        showOverflowTooltip: true,
        hideInTable: props.isHealthProject
      },
      {
        prop: 'informStatus',
        label: '是否预知情',
        minWidth: '120',
        formatter: (row) => (row.informStatus == 1 ? '是' : '否'),
        hideInTable: !(props.model == 1 && isSite)
      },
      {
        prop: 'infoState',
        label: '是否有异议',
        minWidth: '120',
        formatter: (row) => (row.infoState == 1 ? '是' : '否'),
        hideInTable: !(props.model == 1 && isSite)
      },
      {
        prop: 'informRemark',
        label: '异议备注',
        minWidth: '120',
        hideInTable: !(props.model == 1 && isSite),
        showOverflowTooltip: true
      },
      {
        prop: 'medicalFileVo',
        label: '病例档案',
        minWidth: '120',
        slot: 'medicalFileVo',
        hideInTable: props.isHealthProject
      },
      {
        prop: 'siteName',
        label: '期望参与',
        minWidth: '140',
        showOverflowTooltip: true,
        hideInTable: props.isHealthProject
      },
      {
        prop: 'handleTime',
        label:
          props.model == 0
            ? '报名时间'
            : props.model == 1
              ? '审核通过时间'
              : props.model == 3
                ? '签到通过时间'
                : props.model == 5
                  ? '筛选通过时间'
                  : props.model == 6
                    ? '入组通过时间'
                    : props.model == 8
                      ? '出组时间'
                      : '失败时间',
        minWidth: '220',
        sortable: 'custom'
      },
      {
        prop: 'applyStatusDesc',
        label: '状态',
        width: '120',
        showOverflowTooltip: true
        // hideInTable: props?.model != 7
      },
      {
        prop: 'failReason',
        label: '失败原因',
        width: '120',
        showOverflowTooltip: true,
        hideInTable: props?.model != 7
      },
      {
        columnKey: 'action',
        label: '操作',
        width: props.isHealthProject ? 180 : 240,
        align: 'center',
        resizable: false,
        fixed: 'right',
        slot: 'action'
      }
    ];
  });

  const reloadSearch = (where) => {
    searchRef.value?.onSearchFilterSubmit(where);
  };

  const getFileType = (url) => {
    // 使用正则表达式提取文件类型
    const match = url.match(/\.([a-zA-Z0-9]+)(\?|$)/);
    return match ? match[1] : null; // 返回文件类型或 null
  };

  const getFileUrl = (val) => {
    let urls = [];
    if (Array.isArray(val)) {
      urls = val.map((item) => item.url);
    } else {
      const parsedVal = JSON.parse(val);
      if (parsedVal && parsedVal.url) {
        urls.push(parsedVal.url);
      }
    }
    let srcList = [];
    urls.forEach((url) => {
      if (url) {
        let type = getFileType(url);
        if (['gif', 'png', 'jpeg', 'jpg', 'bmp'].includes(type.toLowerCase())) {
          let url2 =
            url.indexOf('https') !== -1
              ? url
              : import.meta.env.VITE_APP_BASE_IMG + url;
          srcList.push(url2);
        } else {
          window.open(`${import.meta.env.VITE_APP_BASE_IMG}${url}`);
        }
      }
    });
    ImagePreview({ urlList: srcList });
  };

  /** 批量通过 */
  const passBatch = () => {
    const rows = selections.value;
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    const content = `是否确认让【${rows.map((d) => d.userName).join()}】的数据项通过吗?`;
    ElMessageBox.confirm(
      `<div style="font-size: 18px; color: #3B426F";>${content}</div>`,
      '审核通过',
      {
        type: 'warning',
        draggable: true,
        center: true,
        customStyle: { maxWidth: '600px' },
        dangerouslyUseHTMLString: true
      }
    )
      .then(() => {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });

        const applyId = rows.map((d) => d.applyId);
        let status = 1;
        let model = 0;
        model = props.model == 6 ? 2 : 0;
        status = getStatus(model);
        const params = {
          applyId: applyId,
          applyStatus: status
        };
        updateUserApplyStatus(params)
          .then(() => {
            loading.close();
            EleMessage.success('操作成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  const handleDelete = () => {
    const rows = selections.value;
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    const content = `是否确认让【${rows.map((d) => d.userName).join()}】的数据项淘汰吗?`;
    ElMessageBox.prompt(
      `<div style="font-size: 18px; color: #3B426F">${content}</div>`,
      `${props.activeTabName}淘汰`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customStyle: { maxWidth: '600px' },
        inputErrorMessage: '淘汰原因不能为空',
        dangerouslyUseHTMLString: true,
        inputValidator: (value) => {
          if (!value) {
            return '淘汰原因不能为空';
          }
        }
      }
    ).then(async ({ value }) => {
      try {
        const loading = EleMessage.loading({
          message: '请求中..',
          plain: true
        });
        const applyId = rows.map((d) => d.applyId);
        let status = 0;
        let model = 0;
        model = props.model == 6 ? 3 : 1;
        status = getStatus(model);
        const params = {
          applyId: applyId,
          applyStatus: status,
          failReason: value
        };
        await updateUserApplyStatus(params);
        loading.close();
        EleMessage.success(`操作成功！`);
        reload();
      } catch (e) {
        loading.close();
        EleMessage.error(e);
      }
    });
  };

  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  const handleReload = () => {
    tableRef.value?.reload?.({
      where: formParams.value
    });
  };

  const reload1 = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ where });
  };

  const loadSiteConfig = async () => {};

  onMounted(async () => {
    await loadSiteConfig();
  });
  onActivated(() => {
    // reload();
  });

  /**
   * 0 通过  1 淘汰  2 出组  3 脱落   // 脱落： 9 出组：8  失败原因、更正原因
   */
  const getStatus = (type) => {
    const statusMap = {
      0: {
        0: 1,
        1: 3,
        3: 5,
        5: 6
      },
      1: {
        0: 2,
        1: 10,
        3: 4,
        5: 7
      },
      2: {
        6: 8
      },
      3: {
        6: 9
      }
    };
    return statusMap[type]?.[props?.model] || 1; // 默认值为 1
  };

  const weedOut = async (item) => {
    const sex = item?.sex == 0 ? '男' : '女';
    Object.assign(currnetData.value, item);
    try {
      const handleTitle = props.model == 6 ? '脱落' : '淘汰';
      const title = `您确认让【${item?.userName} I ${item?.age}岁 I ${sex} I ${item?.nation}】${props.activeTabName}${handleTitle}吗？`;
      ElMessageBox.prompt(
        `<div style="font-size: 18px; color: #3B426F">${title}</div>`,
        `${props.activeTabName}${handleTitle}`,
        {
          textarea: true,
          inputPlaceholder: `请填写${handleTitle}原因`,
          customStyle: { maxWidth: '600px', fontSize: '24px' },
          dangerouslyUseHTMLString: true,
          inputValidator: (value) => {
            if (!value) {
              return `请填写${handleTitle}原因`;
            }
          },
          inputAttrs: {
            rows: 5 // 设置行数为 5
          },
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }
      ).then(async ({ value }) => {
        let status = 0;
        let model = 0;
        model = props.model == 6 ? 3 : 1;
        status = getStatus(model);
        const params = {
          applyId: [currnetData.value?.applyId],
          applyStatus: status,
          failReason: value
        };
        await updateUserApplyStatus(params);
        EleMessage.success('操作成功');
        reload();
      });
    } catch (e) {
      console.log(e);
      EleMessage.error(e.message);
    }
  };

  const pass = async () => {
    try {
      let status = 1;
      let model = 0;
      model = props.model == 6 ? 2 : 0;
      status = getStatus(model);
      const params = {
        applyId: [currnetData.value?.applyId],
        applyStatus: status // 1
      };
      await updateUserApplyStatus(params);
      EleMessage.success('操作成功');
      dialogVisible.value = false;
      reload();
    } catch (e) {
      EleMessage.error(e);
    }
  };

  const diseaseLabels = (data) => {
    if (!data) return;
    const diseaseArray = JSON.parse(data);
    return diseaseArray.map((item) => item.label).join(', ');
  };

  const showConfirmDialod = (item) => {
    dialogVisible.value = true;
    const sex = item?.sex == 0 ? '男' : '女';
    currentTitle.value =
      props.model != 6 ? `${props.activeTabName}通过` : `出组通过`;
    currentContent.value = `您确认让【${item?.userName} I ${item?.age}岁 I ${sex} I ${item?.nation}】${currentTitle.value}吗？`;
    Object.assign(currnetData.value, item);
  };

  const goToQuestionnaire = (item) => {
    showQuestionnaire.value = true;
    Object.assign(currentData.value, item);
  };

  const currnetID = ref('');
  const applyCheckID = ref('');
  const applyId = ref('');

  /** 下拉菜单点击事件 更正   修改参与中心 */
  const dropClick = async (key, row) => {
    if (key === 'update') {
      console.log(row);
      applyId.value = row?.applyId;
      showCorrect.value = true;
    } else if (key === 'modify') {
      currnetID.value = row?.projectId;
      applyCheckID.value = row?.applyId;
      showModifyCenter.value = true;
    }
  };

  const downloadFile = (data) => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    const params = {
      userId: data?.userId
    };
    downloadUserMedicalFile(params, data.userName)
      .then(() => {
        loading.close();
      })
      .catch((e) => {
        loading.close();
        EleMessage.error(e.message);
      });
  };

  const exportData = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });
    const API = props.isHealthProject
      ? exportHealthProjectApply
      : exportDiseaseProjectApplyPageList;
    tableRef.value?.fetch?.(({ where, filters }) => {
      const params = {
        ...where,
        ...filters,
        ...formParams.value,
        projectId: props.projectId,
        projectRecruitName: props?.projectRecruitName,
        applyStatus: props.model == 7 ? -1 : props.model
      };
      API(params)
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  const exportQuestionnaire = () => {
    const loading = EleMessage.loading({
      message: '请求中..',
      plain: true
    });

    tableRef.value?.fetch?.(({ where, filters }) => {
      const params = {
        ...where,
        ...filters,
        ...formParams.value,
        projectId: props.projectId,
        applyStatus: props.model == 7 ? -1 : props.model
      };
      exportQuestionnaireContent(params)
        .then(() => {
          loading.close();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    });
  };

  watch(
    () => [props.projectId, props.siteId],
    ([projectId, siteId]) => {
      if (projectId || siteId) {
        reload();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .el-dropdown-link {
    display: flex;
    align-items: center;
    color: var(--el-color-primary);
    cursor: pointer;
  }

  .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .d-title {
    font-size: 20px;
    color: #4f5e84;
  }
  .d-content {
    text-align: center;
    font-size: 18px;
    color: #3b426f;
  }
</style>
