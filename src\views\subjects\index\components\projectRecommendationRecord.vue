<template>
  <div class="flex-column">
    <ele-card style="height: 410px">
      <template #header>
        <div class="d-flex" style="height: 25px">
          <div style="display: flex; align-items: center"
            >项目推荐记录
            <el-radio-group
              style="margin-left: 36px; color: #718ebf"
              v-model="monthType"
              @change="change"
            >
              <el-radio :value="1">本月</el-radio>
              <el-radio :value="2">本季度</el-radio>
              <el-radio :value="3">本年</el-radio>
            </el-radio-group>
          </div>
          <div>
            <!-- <span class="all-title">全部项目></span> -->
          </div>
        </div>
      </template>
      <div class="title">
        <span
          >有推荐奖励人数：<span class="content">{{
            recommendRecords?.recommendNumber || 0
          }}</span></span
        >
        <span style="margin-left: 20px"
          >有拉新奖励人数：<span class="content">{{
            recommendRecords?.recommendNewUserNumber || 0
          }}</span></span
        >
        <span style="margin-left: 20px"
          >有参与奖人数：<span class="content">{{
            recommendRecords?.oneselfRecomNumber || 0
          }}</span></span
        >
        <span style="margin-left: 20px"
          >需结算奖励人数：<span class="content">{{
            recommendRecords?.waitSettlementNumber || 0
          }}</span></span
        >
      </div>
      <div v-if="loading">
        <ele-loading :loading="loading" text="数据加载中" background="red">
          <div style="width: 100%; height: 500px; background: #ffffff"></div>
        </ele-loading>
      </div>
      <div
        style="
          width: 100%;
          height: calc(100vh - 570px - 52px);
          padding: 0 20px;
          box-sizing: border-box;
        "
      >
        <div ref="main" style="width: 100%; height: 100%"></div>
      </div>
    </ele-card>
  </div>
</template>
<script setup name="projectRecommendationRecord">
  import { nextTick, onMounted, ref } from 'vue';
  import * as echarts from 'echarts';
  import { Icon } from '@iconify/vue';
  import { filterStatistic } from '@/api/subjects/home/<USER>';
  import {
    getRecommendStatistics,
    getProjectRecommendListStatistics
  } from '@/api/project/index';
  import {
    getYAxisMaxValue,
    formatYAxisLabel,
    FormattedNumber
  } from '@/utils/common.js';
  const emit = defineEmits(['done']);
  const caseTimedateRange = ref([]);
  const monthType = ref(3);

  const main = ref(null); // 使用ref创建虚拟DOM引用，使用时用main.value
  const option = ref({});
  const tableData = ref([]);
  const loading = ref(false);
  const chartInitData = async () => {
    const myChart = echarts.init(main.value);
    loading.value = true;

    const params = { monthType: monthType.value };
    await getProjectRecommendListStatistics(params).then((res) => {
      tableData.value = res.data || [];
      loading.value = false;
    });
    emit('done', 'numchangeLoading');
    option.value = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        formatter: function (params) {}
      },
      legend: {
        data: ['推荐奖励', '拉新奖励', '自主参与奖励'],
        bottom: 10 // 将图例放在底部
      },
      xAxis: [
        {
          type: 'category',
          data: tableData.value.map((val) => val.projectRecruitName),
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            interval: 0, // 显示所有标签
            rotate: 15, // 旋转标签
            formatter: function (value) {
              // 设置最大长度
              var maxLength = 4; // 最大字符数
              return value.length > maxLength
                ? value.substring(0, maxLength) + '...'
                : value;
            }
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          min: 0,
          max: getYAxisMaxValue(
            tableData.value.map((val) => val.recommendNumber)
          ),
          axisLabel: {
            formatter: (value) => `${value}`
          },
          // name: '人次', // 左 Y 轴显示的文字
          nameLocation: 'end', // 文字位置
          nameGap: 30 // 文字与坐标轴的间距
        }
      ],
      series: [
        {
          name: '推荐奖励',
          type: 'bar',
          data: tableData.value.map((val) => val.recommendNumber),
          barWidth: '25%',
          barCategoryGap: '30%',
          barGap: '20%', // 不同系列柱状图之间的间距
          itemStyle: {
            color: '#31B5F5'
          },
          label: {
            show: false,
            formatter: (params) => `${params.value}`
          },
          xAxisIndex: 0,
          z: 1
        },
        {
          name: '拉新奖励',
          type: 'bar',
          data: tableData.value.map((val) => val.recommendNewUserNumber),
          barWidth: '25%',
          barCategoryGap: '30%',
          barGap: '20%',
          itemStyle: {
            color: '#FF84D7'
          },
          label: {
            show: false,
            formatter: (params) => `${params.value}`
          },
          xAxisIndex: 0,
          z: 1
        },
        {
          name: '自主参与奖励',
          type: 'bar',
          data: tableData.value.map((val) => val.oneselfRecomNumber),
          barWidth: '25%',
          barCategoryGap: '30%',
          barGap: '20%',
          itemStyle: {
            color: '#8080FF'
          },
          label: {
            show: false,
            formatter: (params) => `${params.value}`
          },
          xAxisIndex: 0,
          z: 1
        }
      ]
    };

    option.value && myChart.setOption(option.value);
  };

  const recommendRecords = ref({});
  const getRecommendData = async () => {
    const params = { monthType: monthType.value };
    const res = await getRecommendStatistics(params);
    recommendRecords.value = res?.data;
  };

  const initData = () => {
    getRecommendData();
    nextTick(() => {
      chartInitData();
    });
  };

  const change = () => {
    initData();
  };

  onMounted(() => {
    initData();
  });
</script>
<style lang="scss" scoped>
  .title {
    color: #718ebf;
    font-size: 18px;
  }

  .content {
    font-weight: 700;
    font-size: 18px;
    color: #5280fb;
  }

  .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .all-title {
    height: 22px;
    font-size: 16px;
    color: #718ebf;
    line-height: 22px;
    cursor: pointer;
  }
</style>
