<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="450"
    :model-value="modelValue"
    title="试验记录"
    @update:modelValue="updateModelValue"
  >
    <div class="modal-dialog testRecordModaldescriptions">
      <el-descriptions
        title="当前志愿者，最后一次入组试验信息"
        :column="1"
        v-if="JSON.stringify(info) != '{}'"
      >
        <el-descriptions-item label="试验机构：">
          {{ info.fullname }}
        </el-descriptions-item>
        <el-descriptions-item label="状态：" v-if="info.status == 0">
          初查不合格
        </el-descriptions-item>
        <el-descriptions-item label="状态：" v-if="info.status == 1">
          筛选
        </el-descriptions-item>
        <el-descriptions-item label="状态：" v-if="info.status == 2">
          入组
        </el-descriptions-item>
        <el-descriptions-item label="状态：" v-if="info.status == 3">
          完成
        </el-descriptions-item>
        <el-descriptions-item label="状态：" v-if="info.status == 4">
          出组
        </el-descriptions-item>
        <el-descriptions-item label="状态：" v-if="info.status == 5">
          未入组
        </el-descriptions-item>
        <el-descriptions-item label="状态：" v-if="info.status == 6">
          入组未给药
        </el-descriptions-item>
        <el-descriptions-item label="状态：" v-if="info.status == 7">
          待入组
        </el-descriptions-item>
        <el-descriptions-item label="状态：" v-if="info.status == 8">
          入组前筛查不合格
        </el-descriptions-item>
        <el-descriptions-item label="入组时间：">
          {{ info.joindate }}
        </el-descriptions-item>
        <el-descriptions-item label="末次给药时间：">
          {{ info.lastdosedate }}
        </el-descriptions-item>
        <el-descriptions-item label="末次访视时间：">
          {{ info.trialedate }}
        </el-descriptions-item>
        <el-descriptions-item label="建议试验间隔天数：">
          {{ info.interval }}
        </el-descriptions-item>
      </el-descriptions>
      <div v-else>
        <el-empty :image-size="100" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref } from 'vue';
  import { getTrailRecord } from '@/api/subjects/studyManage/index.js';

  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object
  });

  const close = () => {
    updateModelValue(false);
  };

  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const info = ref({});
  nextTick(async () => {
    getTrailRecord(props.data).then((res) => {
      info.value = res.data || {};
    });
  });
</script>
<style>
  .testRecordModaldescriptions .el-descriptions__label:not(.is-bordered-label) {
    margin-right: 5px !important;
  }
</style>
