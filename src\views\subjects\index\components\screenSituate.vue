<template>
  <div class="flex-column">
    <div
      class="flex-between"
      style="box-sizing: border-box; align-items: center"
    >
      <span style="font-size: 18px; color: #3b426f">近5年筛查情况</span>
    </div>
    <div v-if="loading">
      <ele-loading :loading="loading" text="数据加载中" background="red">
        <div style="width: 100%; height: 500px; background: #ffffff"></div>
      </ele-loading>
    </div>
    <div
      style="
        width: 100%;
        height: calc(100vh - 326px - 52px);
        padding: 0 20px;
        box-sizing: border-box;
      "
    >
      <div ref="main" style="width: 100%; height: 100%"></div>
    </div>
  </div>
</template>
<script setup>
  import * as echarts from 'echarts';
  import { nextTick, ref } from 'vue';
  import { Icon } from '@iconify/vue';
  import { filterStatistic } from '@/api/subjects/home/<USER>';
  import {
    getYAxisMaxValue,
    formatYAxisLabel,
    FormattedNumber
  } from '@/utils/common.js';
  const emit = defineEmits(['done']);

  const main = ref(null); // 使用ref创建虚拟DOM引用，使用时用main.value
  const option = ref({});
  const tableData = ref([]);
  const loading = ref(false);
  const chartInitData = async () => {
    const myChart = echarts.init(main.value);
    loading.value = true;

    await filterStatistic().then((res) => {
      tableData.value = res.filterJoinStatisticVoList || [];
      loading.value = false;
    });
    emit('done', 'numchangeLoading');
    option.value = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        formatter: function (params) {
          let result = '';
          let result2 = '';
          // 筛选出类型为 bar 的系列数据
          const barParams = params.filter(
            (param) => param.seriesType === 'bar'
          );
          barParams.forEach((param) => {
            let seriesColor = param.color;
            let formattedValue;
            if (
              param.seriesName === '筛查人次' ||
              param.seriesName === '入组人次'
            ) {
              // 添加千分位分号
              formattedValue = param.value
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            } else {
              formattedValue = param.value;
            }
            result += `<div class="echarts-tooltip-series"><span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ${seriesColor}; margin-right: 5px;"></span>${param.seriesName}:<span style="font-size:18px;color:#5280fb"> ${formattedValue}</span></div>`;
            result2 = `<div class="echarts-tooltip-title">${params[0].name}</div>`;
          });
          return result + result2;
        }
      },
      legend: {
        data: ['筛查人次', '入组人次'],
        bottom: 10 // 将图例放在底部
      },
      xAxis: [
        {
          type: 'category',
          data: tableData.value.map((val) => val.year),
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          min: 0,
          max: getYAxisMaxValue(tableData.value.map((val) => val.filterCount)),
          axisLabel: {
            formatter: (value) => `${value}`
          },
          // name: '人次', // 左 Y 轴显示的文字
          nameLocation: 'end', // 文字位置
          nameGap: 30 // 文字与坐标轴的间距
        }
        // {
        //   type: 'value',
        //   min: 0,
        //   max: getYAxisMaxValue(tableData.value.map((val) => val.joinCount)),
        //   axisLabel: {
        //     formatter: (value) => `${value}`
        //   },
        //   name: '入组人次', // 左 Y 轴显示的文字
        //   nameLocation: 'end', // 文字位置
        //   nameGap: 30 // 文字与坐标轴的间距
        // }
      ],
      series: [
        // {
        //   name: '筛查人次',
        //   type: 'line',
        //   data: tableData.value.map((val) => val.filterCount),
        //   itemStyle: {
        //     color: '#c0e1a8'
        //   },
        //   label: {
        //     show: true,
        //     formatter: (params) => `${Number(params.value || '0')}`
        //   },
        //   xAxisIndex: 0,
        //   z: 2,
        //   symbol: 'circle',
        //   symbolSize: 8,
        //   showSymbol: true,
        //   // 自定义数据点位置，让圆点向左偏移
        //   position: (params) => {
        //     return [params[0] - 10, params[1]];
        //   }
        // },
        // {
        //   name: '入组人次',
        //   type: 'line',
        //   data: tableData.value.map((val) => val.joinCount),
        //   itemStyle: {
        //     color: '#f2bd2d'
        //   },
        //   label: {
        //     show: true,
        //     formatter: (params) => `${Number(params.value || '0')}`
        //   },
        //   xAxisIndex: 0,
        //   z: 2,
        //   symbol: 'circle',
        //   symbolSize: 8,
        //   showSymbol: true,
        //   // 自定义数据点位置，让圆点向左偏移
        //   position: (params) => {
        //     return [params[0] - 10, params[1]];
        //   }
        // },
        {
          name: '筛查人次',
          type: 'bar',
          data: tableData.value.map((val) => val.filterCount),
          barWidth: '30%',
          barCategoryGap: '30%',
          barGap: '20%', // 不同系列柱状图之间的间距
          itemStyle: {
            color: '#31B5F5'
          },
          label: {
            show: false,
            formatter: (params) => `${params.value}`
          },
          xAxisIndex: 0,
          z: 1
        },
        {
          name: '入组人次',
          type: 'bar',
          data: tableData.value.map((val) => val.joinCount),
          barWidth: '30%',
          barCategoryGap: '30%',
          barGap: '20%',
          itemStyle: {
            color: '#5280FB'
          },
          label: {
            show: false,
            formatter: (params) => `${params.value}`
          },
          xAxisIndex: 0,
          z: 1
        }
      ]
    };

    option.value && myChart.setOption(option.value);
  };

  nextTick(() => {
    chartInitData();
  });
</script>
<style>
  .exportBtn {
    margin-bottom: 10px;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    cursor: pointer;
    color: #507aff;
  }
</style>
