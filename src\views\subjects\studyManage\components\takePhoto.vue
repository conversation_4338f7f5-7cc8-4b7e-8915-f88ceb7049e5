<!-- 编辑弹窗 -->
<template>
  <div>
    <!-- 摄像头选择 -->
    <selectCameraSelect
      ref="selectCamera"
      class="select-camera"
      @changeCamera="setCamera"
    />
    <!-- 画笔控件 用来拍照 -->
    <canvas style="display: none" ref="canvasDom"></canvas>
    <!-- 播放器，用来播放拍摄的视频 -->

    <div style="position: relative">
      <video
        width="434"
        height="323"
        :class="!showOpen ? 'camera_video' : ''"
        style="margin-bottom: 18px"
        ref="videoDom"
      ></video>
      <div
        v-if="!showOpen"
        class="notShowOpen"
        style="width: 434px; height: 323px"
      >
        <el-image :src="vidicon" style="width: 88px; height: 76px" />
        <div
          style="
            width: 100%;
            position: absolute;
            bottom: 20px;
            text-align: center;
            left: 0;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: 400;
            font-size: 16px;
            color: #718ebf;
          "
        >
          请检查摄像头连接、浏览器权限
        </div>
      </div>
      <div
        class="cameraBox"
        :style="`height:${videoDomHeight}px ;`"
        v-if="showOpen"
      >
        <div class="camera"></div>
      </div>
    </div>

    <!--  显示照片  -->
    <div style="display: flex; justify-content: center; align-items: center">
      <el-button
        type="primary"
        @click="takePhoto"
        :disabled="flag || !isSign"
        :title="
          flag
            ? type == '1'
              ? '请先读取身份证后拍照'
              : '请开启人脸识别后拍照'
            : ''
        "
        >拍 照</el-button
      >
      <el-button
        type="primary"
        @click="submit"
        :disabled="flag || !base64Photo"
        :loading="checkLoading"
      >
        {{ checkLoading ? '证件核对中' : '证件核对' }}</el-button
      >
    </div>
    <!-- <img v-else :src="imgurl" style="width: 660px; height: 495px" />
    <div style="display: flex; justify-content: center">
      <el-button type="primary" @click="takePhoto">{{
        imgurl ? '重拍' : '拍照'
      }}</el-button>
    </div> -->
  </div>
</template>

<script setup>
  import { ref, nextTick, onMounted } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import vidicon from '@/assets/subjects/vidicon.svg';
  import { throttle } from 'lodash';
  import selectCameraSelect from './selectCameraSelect.vue';
  const emit = defineEmits(['done', 'doneSubmit']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    checkLoading: Boolean,
    flag: Boolean,
    isSign: Boolean,
    type: String
  });
  // canvas控件对象
  const canvasDom = ref(null);
  // video 控件对象
  const videoDom = ref(null);
  // 照片路径
  const imgurl = ref(null);

  // nextTick(() => {
  //   openCamera();
  // });
  const showOpen = ref(false);
  // const openCamera = (deviceId = null) => {
  //   // 设置视频约束
  //   const videoConstraints = deviceId
  //     ? { deviceId: { exact: deviceId } }
  //     : true; // 如果没指定，就使用true，表示使用默认
  //   if (navigator.mediaDevices) {
  //     navigator.mediaDevices
  //       .getUserMedia({
  //         audio: false,
  //         video: videoConstraints
  //       })
  //       .then((stream) => {
  //         videoDom.value.srcObject = stream;
  //         videoDom.value.play();
  //         showOpen.value = true;
  //       })
  //       .catch((err) => {
  //         console.log(err);
  //         EleMessage.error(
  //           '未检测到摄像头开启，请检查摄像头是否插好，或者摄像头被占用！'
  //         );
  //       });
  //   } else {
  //     EleMessage.error('该浏览器不支持开启摄像头，请更换最新版浏览器！');
  //   }
  // };

  // 设置摄像头
  const setCamera = (stream) => {
    // 将视频流传入viedo控件
    videoDom.value.srcObject = stream;
    // 播放
    videoDom.value.play();
    showOpen.value = true;
  };

  /* 老版本拍照
  // 拍照
  const takePhoto = () => {
    if (!showOpen.value) {
      EleMessage.error(
        '未检测到摄像头开启，请检查摄像头是否插好，或者摄像头被占用！'
      );
      return;
    }
    // 如果已经拍照了就重新启动摄像头
    //if (imgurl.value) {
    //  imgurl.value = null;
    //  openCamera();
    //  return;
    //}

    // 设置画布大小与摄像大小一致
    canvasDom.value.width = videoDom.value.videoWidth;
    canvasDom.value.height = videoDom.value.videoHeight;
    // 执行画的操作
    canvasDom.value.getContext('2d').drawImage(videoDom.value, 0, 0);
    // 将结果转换为可展示的格式
    imgurl.value = canvasDom.value.toDataURL('image/png');
    // 关闭摄像头
    // stop();
    nextTick(() => {
      emit('done', imgurl.value);
    });
  };*/
  // 拍照
  const takePhoto = () => {
    if (!showOpen.value) {
      EleMessage.error(
        '未检测到摄像头开启，请检查摄像头是否插好，或者摄像头被占用！'
      );
      return;
    }

    const videoWidth = videoDom.value.videoWidth;
    const videoHeight = videoDom.value.videoHeight;
    // 计算截取区域，这里让截取区域边长为宽高最小值的0.6倍
    const cropFactor = 0.7;
    const cropWidth = Math.min(videoWidth, videoHeight) * cropFactor;
    const cropHeight = Math.min(videoWidth, videoHeight);
    const cropX = (videoWidth - cropWidth) / 2;
    const cropY = (videoHeight - cropHeight) / 2;

    // 设置画布大小为截取区域大小
    canvasDom.value.width = cropWidth;
    canvasDom.value.height = cropHeight;
    const ctx = canvasDom.value.getContext('2d');
    if (ctx) {
      // 执行画的操作，只绘制中间部分
      ctx.drawImage(
        videoDom.value,
        cropX,
        cropY,
        cropWidth,
        cropHeight,
        0,
        0,
        cropWidth,
        cropHeight
      );
    }
    // 将结果转换为可展示的格式
    imgurl.value = canvasDom.value.toDataURL('image/png');
    base64Photo.value = canvasDom.value.toDataURL('image/png');
    // 关闭摄像头
    // stop();
    nextTick(() => {
      emit('done', imgurl.value);
    });
  };
  // 关闭摄像头
  const stop = () => {
    let stream = videoDom.value.srcObject;
    if (!stream) return;
    let tracks = stream.getTracks();
    tracks.forEach((x) => {
      x.stop();
    });
    videoDom.value.srcObject = null;
  };
  const videoDomHeight = ref(0);
  onMounted(() => {
    nextTick(() => {
      videoDomHeight.value = videoDom.value.offsetHeight || 371;
      console.log('videoDomHeight', videoDomHeight.value);
    });
  });
  const selectCamera = ref(null);
  //拍照的base64信息
  const base64Photo = ref('');
  //通知父组件证件核对
  const submit = throttle(() => {
    emit('doneSubmit', base64Photo.value.replace('data:image/png;base64,', ''));
  }, 1500);
  const closeCamera = () => {
    selectCamera.value.stop();
  };
  const clearPhoto = () => {
    base64Photo.value = '';
  };
  defineExpose({
    closeCamera,
    stop,
    setCamera,
    clearPhoto
  });
</script>
<style lang="scss" scoped>
  .camera_video {
    border: 1px solid #94aed9;
    border-radius: 4px;
    background: #ffffff;
    box-sizing: border-box;
  }
  .cameraBox {
    position: absolute;
    top: 0;
    left: 0;
    width: 434px;
    height: 323px !important;
    background: rgba(0, 0, 0, 0.5);
    mask: url(../../../../assets/subjects/photo.png) no-repeat 50% 50% / 100%
      100%;
  }
  // .select-camera {
  //   position: absolute;
  //   top: -6px;
  //   right: 35px;
  //   width: 300px;
  //   height: 40px;
  // }

  // .camera {
  // position: absolute;
  // top: 0;
  // left: 0;
  // inset: 0;
  // background: rgba(0, 0, 0, 0.3);
  // mask: url(../../../assets/index/photo.png) no-repeat 50% 50% / 100% 100%;
  // }
  .notShowOpen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999999;
  }
</style>
