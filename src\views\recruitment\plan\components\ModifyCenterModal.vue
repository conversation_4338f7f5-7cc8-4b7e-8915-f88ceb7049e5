<!-- 修改密码弹窗 -->
<template>
  <ele-modal
    form
    :width="600"
    title="修改期望参与中心"
    :append-to-body="true"
    v-model="visible"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-position="top"
      :rules="rules"
      label-width="90px"
      @submit.prevent=""
    >
      <el-form-item label="参与中心" prop="siteId">
        <el-select v-model="form.siteId" clearable placeholder="请选择参与中心">
          <el-option
            v-for="item in centers"
            :key="item.siteId"
            :label="item.siteName"
            :value="item.siteId"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleOk">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import {
    updateUserJoinSite,
    getProjectSiteList
  } from '@/api/recruitment/index';

  const emit = defineEmits(['update:modelValue', 'done']);

  const props = defineProps({
    modelValue: Boolean,
    currnetID: String,
    applyCheckID: String,
    model: String
  });

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交loading */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  const centers = ref([]);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    siteId: null
  });

  /** 表单验证规则 */
  const rules = reactive({
    siteId: [
      {
        required: true,
        message: '请选择参与中心',
        trigger: ['change', 'blur']
      }
    ]
  });

  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭弹窗 */
  const handleCancel = () => {
    updateModelValue(false);
  };

  /** 保存修改 */
  const handleOk = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      try {
        const params = {
          ...form,
          applyId: props.applyCheckID
        };
        const res = await updateUserJoinSite(params);
        EleMessage.success('操作成功');
        emit('done');
        updateModelValue(false);
      } catch (e) {
        loading.value = false;
        EleMessage.error(e.message);
      }
    });
  };

  /** 弹窗关闭事件 */
  const handleClosed = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    loading.value = false;
  };

  const getProjectSiteListFun = async () => {
    try {
      const params = {
        projectId: props?.currnetID
      };
      const res = await getProjectSiteList(params);
      centers.value = res;
    } catch (e) {
      EleMessage.error(e.message);
    }
  };

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        getProjectSiteListFun();
      }
    }
  );
</script>
