<template>
  <ele-page flex-table :multi-card="false" hide-footer>
    <ele-card flex-table :body-style="{ padding: '24px' }">
      <ele-loading :loading="loading">
        <el-row :gutter="8">
          <el-col :xs="24" :sm="23" :md="24" class="mb-36">
            <div style="display: flex">
              <img src="~@/assets/index/icon2.png" style="margin-right: 4px" />
              <ele-text size="md" strong>
                {{ formData.companyName }}
              </ele-text>

              <el-link
                type="primary"
                :icon="Edit"
                class="text-base"
                style="margin-left: 8px"
                @click="showEdit = true"
                >编辑</el-link
              >
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" class="mb-36">
            <ele-text size="ms"> 电话：{{ formData.telephone }} </ele-text>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" class="mb-36">
            <ele-text size="ms"> 邮箱：{{ formData.email }} </ele-text>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" class="mb-36">
            <ele-text size="ms"> 地址：{{ formData.address }} </ele-text>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="flex mt-3">
            <ele-text size="ms">机构门头照：</ele-text>
            <el-image
              v-if="formData.doorOssId"
              style="width: 200px; height: 200px"
              :src="formData.doorUrl"
              fit="fill"
            />
          </el-col>
        </el-row>
      </ele-loading>
    </ele-card>
    <info-edit v-model="showEdit" :data="formData" @done="reload" />
  </ele-page>
</template>

<script setup name="Info">
  import { ref, computed } from 'vue';
  import { Icon } from '@iconify/vue';
  import { useThemeStore } from '@/store/modules/theme';
  import { storeToRefs } from 'pinia';
  import { useUserStore } from '@/store/modules/user';
  import { getSiteInfo } from '@/api/site';
  import infoEdit from '../components/info-edit.vue';
  import { Edit } from '@element-plus/icons-vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';

  const { roleOrg } = useUserStore();

  const loading = ref(false);

  const formData = ref({});

  const text = ref(null);

  const showEdit = ref(false);

  const getPhoto = computed(() => {
    return `${import.meta.env.VITE_APP_BASE_IMG}/${formData.value.fileUrl}`;
  });

  const load = async () => {
    try {
      loading.value = true;
      const res = await getSiteInfo();
      Object.assign(formData.value, res?.data);
      loading.value = false;
    } catch (error) {
      loading.value = false;
    }
  };

  const reload = () => {
    load();
  };

  load();
</script>
<style lang="scss" scoped>
  .mb-36 {
    margin-bottom: 36px;
  }

  .qrcode {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 360px;
    margin: 10px 10px 10px 0;
    background-color: #408cff;
    border-radius: 5px;

    .title {
      width: 100%;
      padding: 10px 0;
      font-size: 16px;
      color: #fff;
      text-align: center;
      border-bottom: 1px solid #fff;
    }

    .img {
      padding: 10px;
      margin-top: 10px;
      background-color: #fff;
      border-radius: 5px;

      .qr {
        padding: 10px;
        border: 1px dotted #3e8afb;
        border-radius: 5px;
      }
    }

    .tips {
      padding: 10px;
      font-weight: 600;
      color: #fff;
    }
  }

  .title {
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: bold;
    color: #333;
  }

  .explain {
    display: flex;
    align-items: center;
    padding: 6px 0;
    margin-bottom: 15px;
    font-size: 14px;
    color: #f2711c;
    background-color: #ffede1;
    border: 1px solid #ffa56a;
    border-radius: 4px;

    svg {
      margin: 0 15px;
      font-size: 18px;
    }
  }
</style>
