<!-- 搜索表单 -->
<template>
  <el-form label-width="100px" @keyup.enter="search" @submit.prevent="">
    <el-row :gutter="8" style="margin: 0px">
      <el-col :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label="推荐人姓名">
          <el-input
            clearable
            v-model.trim="form.recommendUserName"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label="项目名称">
          <el-input
            clearable
            v-model.trim="form.projectName"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label="招募类型">
          <el-select
            v-model="form.projectRecruitType"
            placeholder="招募类型"
            clearable
          >
            <el-option label="健康项目" :value="1" />
            <el-option label="患者项目" :value="2" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label="参与状态">
          <el-select
            v-model="form.applyStatus"
            placeholder="参与状态"
            clearable
          >
            <el-option label="审核中" :value="0" />
            <el-option label="待签到" :value="1" />
            <el-option label="筛选中" :value="3" />
            <el-option label="待入组" :value="5" />
            <el-option label="入组" :value="6" />
            <el-option label="出组" :value="8" />
            <el-option label="失败" :value="-1" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="!isSite" :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label="奖励类型">
          <el-select v-model="form.rewardType" placeholder="奖励类型" clearable>
            <el-option label="推荐奖励" :value="2" />
            <el-option label="拉新奖励" :value="1" />
            <el-option label="自主参与奖励" :value="3" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label="是否推荐官">
          <el-select
            v-model="form.recommendFlag"
            placeholder="是否推荐官"
            clearable
          >
            <el-option label="否" :value="0" />
            <el-option label="是" :value="1" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label="是否已打款">
          <el-select
            v-model="form.receivedFlag"
            placeholder="是否已打款"
            clearable
          >
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>

      <el-col :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label="报名时间">
          <el-date-picker
            unlink-panels
            type="daterange"
            v-model="dateRange"
            range-separator="-"
            value-format="YYYY-MM-DD"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            class="ele-fluid"
          />
        </el-form-item>
      </el-col>
      <el-col :lg="6" :md="12" :sm="12" :xs="24">
        <el-form-item label-width="16px">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { useUserStore } from '@/store/modules/user';

  const { isSite } = useUserStore();

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetForm] = useFormData({
    recommendUserName: '',
    receivedFlag: null,
    projectName: '',
    applyStatus: null,
    projectRecruitType: null,
    rewardType: null,
    dateRange: ['', '']
  });

  /** 日期范围 */
  const dateRange = ref(['', '']);

  /** 重置表单数据 */
  const resetFields = () => {
    resetForm();
    dateRange.value = ['', ''];
  };

  /** 搜索 */
  const search = () => {
    const [d1, d2] = dateRange.value || [];
    emit('search', {
      ...form,
      startApplyTime: d1,
      endApplyTime: d2
    });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };

  defineExpose({ resetFields });
</script>
