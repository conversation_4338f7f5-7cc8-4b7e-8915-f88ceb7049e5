<template>
  <ele-page
    flex-table
    :multi-card="false"
    hide-footer
    style="min-height: 420px; background-color: #ecf2f9"
  >
    <ele-card flex-table style="margin-top: 8px">
      <!-- Tabs 组件 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        highlight-current-row
      >
        <!-- 关键字搜索 -->
        <template #toolbar>
          <record-search @search="reload" />
        </template>
        <template #action="{ row }">
          <el-space v-if="row.receivedFlag == 0 && row.applyStatus == 8">
            <el-link
              type="primary"
              :underline="false"
              @click="toConfirmPayment(row)"
            >
              确认打款
            </el-link>
          </el-space>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup name="reward">
  import { ref, computed } from 'vue';
  import { getPageList, confirmPayment } from '@/api/project/index';
  import { useRouter } from 'vue-router';
  import RecordSearch from './components/record-search.vue';
  import { ElMessage, ElMessageBox } from 'element-plus';

  const router = useRouter();

  const baseUrl = import.meta.env.VITE_APP_BASE_IMG;
  // 搜索组件实例
  const searchRef = ref();

  // 表格实例
  const tableRef = ref(null);

  const showModal = ref(false);

  const current = ref(null);

  // 表格列配置
  const columns = computed(() => {
    return [
      {
        label: `推荐人姓名`,
        prop: 'recommendName',
        minWidth: 120
      },
      {
        label: `手机号（注册）`,
        prop: 'phone',
        minWidth: 150
      },
      {
        label: `是否推荐官`,
        prop: 'recommendFlag',
        minWidth: 150,
        formatter: (row) => (row.recommendFlag == 1 ? '是' : '否')
      },
      {
        label: `招募项目名称`,
        prop: 'projectRecruitName',
        minWidth: 180,
        showOverflowTooltip: true
      },
      {
        label: `招募类型`,
        prop: 'projectRecruitType',
        minWidth: 100,
        formatter: (row) =>
          row.projectRecruitType == 1 ? '健康项目' : '患者项目'
      },
      {
        label: `奖励类型`,
        prop: 'rewardType',
        minWidth: 100,
        formatter: (row) =>
          row.rewardType == 1
            ? '拉新（首次成功出组的项目）'
            : row.rewardType == 2
              ? '项目推荐'
              : '自主参与奖励',
        showOverflowTooltip: true
      },
      {
        label: `奖励金额`,
        prop: 'referralReward',
        minWidth: 120
      },
      {
        label: `是否已打款`,
        prop: 'receivedFlag',
        formatter: (row) => (row.receivedFlag == 1 ? '是' : '否'),
        minWidth: 120
      },
      {
        label: `被推荐志愿者`,
        prop: 'userName',
        minWidth: 140,
        showOverflowTooltip: true
      },
      {
        label: `参与中心`,
        prop: 'companyName',
        minWidth: 180,
        showOverflowTooltip: true
      },
      {
        label: `报名时间`,
        prop: 'applyTime',
        align: 'center',
        minWidth: 140,
        showOverflowTooltip: true
      },
      {
        label: `当前参与状态`,
        prop: 'applyStatusDesc',
        minWidth: 140,
        showOverflowTooltip: true
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 120,
        align: 'center',
        resizable: false,
        slot: 'action'
      }
    ];
  });

  // 表格数据源
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getPageList({
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
  };

  /* 搜索 */
  const reload = (where) => {
    current.value = null;
    tableRef.value?.reload?.({ page: 1, where });
  };

  const toConfirmPayment = async (row) => {
    await ElMessageBox.confirm(
      `是否确认推荐人为${row.recommendName}的数据已打款？`,
      '提示',
      {
        type: 'warning',
        draggable: true
      }
    );
    try {
      const params = {
        id: row.id,
        receivedFlag: 1
      };
      await confirmPayment(params);
      ElMessage.success('操作成功！');
      reload();
    } catch (e) {
      ElMessage.error(e.message);
    }
  };
</script>
<style lang="scss" scoped></style>
