<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    width="450"
    :model-value="modelValue"
    title="公司查询"
    @update:modelValue="updateModelValue"
  >
    <div class="modal-dialog" style="height: 150px">
      <div style="margin-bottom: 20px">
        <!-- <el-alert
          title="如果查询不到，请联系010-86463913转2 ！"
          type="warning"
          center
          :titleStyle="{ color: 'red' }"
          :closable="false"
        /> -->
        <el-tabs
          v-model="currentStatus"
          class="demo-tabs"
          @tab-click="currentStatusChange"
        >
          <el-tab-pane label="国内公司" name="guonei" />
          <el-tab-pane label="国际公司" name="guoji" />
        </el-tabs>
      </div>
      <el-select
        v-if="currentStatus == 'guonei'"
        v-model="value"
        placeholder="请输入关键词搜索，确认无误后点击确定"
        filterable
        remote
        reserve-keyword
        remote-show-suffix
        :remote-method="remoteMethod"
        :loading="loading"
        style="width: 100%"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-input
        v-if="currentStatus == 'guoji'"
        v-model="value"
        placeholder="请输入英文公司名称，确认无误后点击确定"
        @input="checkChinese"
      />
    </div>

    <template #footer>
      <div class="dialog-footer" style="display: flex; justify-content: center">
        <div>
          <el-button type="info" @click="updateModelValue(false)"
            >取 消</el-button
          >
          <el-button type="primary" :loading="loading" @click="submit">
            确 定
          </el-button>
        </div>
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref } from 'vue';
  import { companySearch } from '@/api/subjects/home';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';

  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object
  });
  const loading = ref(false);
  const options = ref([]);
  const value = ref('');
  const currentStatus = ref('guonei');
  const checkChinese = () => {
    value.value = value.value.replace(/[\u4E00-\u9FA5]/g, '');
  };
  const currentStatusChange = (type) => {
    currentStatus.value = type.props.name;
    value.value = '';
  };
  const remoteMethod = (query) => {
    if (query) {
      loading.value = true;
      companySearch({ companyName: query }).then((res) => {
        options.value = res.map((val) => {
          return {
            label: val.companyName,
            value: val.companyName
          };
        });
        loading.value = false;
      });
    } else {
      options.value = [];
    }
  };
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const submit = () => {
    if (!value.value) {
      return EleMessage.error('请选择或输入公司后提交！');
    }
    // loading.value = true;
    // companyAdd({ customerType: '2', customerName: value.value }).then((res) => {
    //   console.log(res);
    //   loading.value = false;
    //   if (res.code == 200) {
    //     const data = {
    //       label: value.value,
    //       value: res.data
    //     };
    //     emit('done', data);
    //     updateModelValue(false);
    //     return;
    //   }
    //   return EleMessage.error(res.msg);
    // });
    const data = {
      label: value.value,
      value: value.value,
      name: value.value
    };
    emit('done', data);
    updateModelValue(false);
    return;
  };
</script>
