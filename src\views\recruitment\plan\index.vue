<template>
  <ele-page hide-footer flex-table style="background-color: #ecf2f9">
    <ele-card
      :body-style="{ paddingTop: '8px', paddingBottom: '16px' }"
      :header-style="{ padding: '0 16px' }"
    >
      <div class="title">
        <el-button
          class="title-f"
          type="primary"
          plain
          @click="showChoiceProjectDialog = true"
          >选择项目
        </el-button>
        <div class="d-flex">
          <div v-if="projectData.projectRecruitName" class="pTitle">
            <span :style="getTextColor(projectData?.projectRecruitType)">{{
              projectData?.projectRecruitType == 1 ? '【健康】' : '【患者】'
            }}</span
            >{{ projectData.projectRecruitName }}
          </div>
          <span v-else @click="showChoiceProjectDialog = true" class="pTitle">
            请先选择试验项目
          </span>
          <div>
            <el-button
              @click="showChatDialog"
              type="info"
              style="margin-right: 12px"
              >沟通</el-button
            >
            <!-- <el-link type="primary" @click="toSetting()">
              设置计划入组
            </el-link> -->
            <ele-dropdown
              :items="
                [
                  {
                    title: '设置计划入组',
                    command: 'setting',
                    visible: true
                  }
                ].filter((i) => i.visible)
              "
              @command="(command) => dropClick(command, item)"
            >
              <el-button type="info" :underline="false" style="display: flex">
                <!-- <span>更多</span> -->
                <el-icon :size="24">
                  <Operation />
                </el-icon>
              </el-button>
            </ele-dropdown>
          </div>
        </div>
      </div>
      <template v-if="!isHealthProject">
        <ele-check-card
          v-model="active"
          :items="multCenterData"
          :row="{ gutter: 12 }"
          style="margin-top: 12px; border-radius: 0px"
        >
          <template #item="{ item }">
            <div
              class="app-card"
              :class="{ highlight: active == item.value }"
              @click="checkedProject(item)"
            >
              <div
                class="app-link-title"
                :class="item.planJoinGroupTotalPeople ? 'mb-11' : ''"
              >
                {{ item.name }}
              </div>
              <div v-if="item.planJoinGroupTotalPeople">
                <el-progress
                  :percentage="
                    item.planJoinGroupTotalPeople > 0
                      ? (item.joinGroupNumber / item.planJoinGroupTotalPeople) *
                        100
                      : 0
                  "
                >
                  <template #default>
                    <span class="f-16"
                      >{{ item.joinGroupNumber }} /
                      {{ item.planJoinGroupTotalPeople }}</span
                    >
                  </template>
                </el-progress>
              </div>
              <div class="f-16" v-else
                >{{ projectData.joinGroupSucceed }} / 未设置</div
              >
            </div>
          </template>
        </ele-check-card>
      </template>
      <template v-else>
        <div style="margin-top: 12px">
          <el-row>
            <el-col :span="4">
              <div class="d-flex">
                <div class="f-16">入组进度：</div>
                <div style="flex: 1">
                  <el-progress
                    v-if="projectData.planJoinGroupTotalPeople"
                    :percentage="
                      projectData.planJoinGroupTotalPeople > 0
                        ? (projectData.joinGroupSucceed /
                            projectData.planJoinGroupTotalPeople) *
                          100
                        : 0
                    "
                  >
                    <template #default>
                      <span class="f-16"
                        >{{ projectData.joinGroupSucceed }} /
                        {{ projectData.planJoinGroupTotalPeople }}</span
                      >
                    </template>
                  </el-progress>
                  <span v-else class="f-16"
                    >{{ projectData.joinGroupSucceed }} / 未设置</span
                  >
                </div>
              </div>
            </el-col>
            <el-col :span="5" style="text-align: center">
              <span class="f-16" style="margin-left: 20px">
                男报名成功/计划报名： {{ projectData?.maleApplyNumber }} /
                {{ projectData?.malePlanApplyNumber || '未设置' }}
              </span>
            </el-col>
            <el-col :span="5">
              <span class="f-16" style="margin-left: 20px">
                女报名成功/计划报名： {{ projectData?.femaleApplyNumber }} /
                {{ projectData?.femalePlanApplyNumber || '未设置' }}
              </span>
            </el-col>
          </el-row>
        </div>
      </template>
      <div
        v-if="
          projectData?.referralReward ||
          projectData?.oneselfRecomAward ||
          projectData?.projectRecruitDeadline
        "
        class="content-l"
        ><span v-if="projectData?.referralReward" class="ss-content"
          >推荐奖：<span style="color: #e87359">{{
            projectData?.referralReward
          }}</span></span
        ><span v-if="projectData?.oneselfRecomAward" class="ss-content"
          >自主参与奖：<span style="color: #e87359">{{
            projectData?.oneselfRecomAward
          }}</span></span
        ><span
          v-if="projectData?.projectRecruitDeadline"
          class="ss-content"
          style="margin-right: 0px"
          >招募截止日期：{{ projectData?.projectRecruitDeadline }}</span
        >
      </div>
    </ele-card>
    <ele-card
      flex-table
      :body-style="{ paddingTop: '8px' }"
      :header-style="{ padding: '0 16px' }"
    >
      <!-- Tabs 组件 -->
      <el-tabs v-model="activeTab">
        <!-- {{ tabs }} -->
        <el-tab-pane
          v-for="tab in tabs"
          :key="tab.name"
          :label="tab.label"
          :name="tab.name"
        >
          <template #label>
            <span class="custom-tabs-label">
              <span>{{ tab.label }}</span
              ><span v-if="tab.value">({{ tab.value }})</span>
            </span>
          </template>
          <CommonTableList
            v-if="activeTab === tab.name"
            :model="tab.model"
            :activeTabName="tab.label"
            :siteId="currentSiteId"
            :isMultipleSite="isMultipleSite"
            :projectId="projectId"
            :keywords="keywords"
            :isHealthProject="isHealthProject"
            :projectRecruitName="projectData.projectRecruitName"
          />
        </el-tab-pane>
      </el-tabs>
    </ele-card>
    <ChoiceProjectModal
      v-model="showChoiceProjectDialog"
      :model="1"
      :projectId="projectId"
      :keywords="ckeywords"
      @done="reSearch"
    />
    <SettingProjectModal
      v-model="showSettingDialog"
      :model="1"
      :projectId="projectId"
      :projectData="projectData"
      :isHealthProject="isHealthProject"
      @done="reSearch"
    />
    <ChatProjectModel
      v-model="chatDialogVisible"
      :projectId="projectId"
      :title="pmTitle"
    />
  </ele-page>
</template>

<script setup name="RecruitmentProgress">
  import { computed, ref } from 'vue';
  import {
    getProjectJoinProgress,
    getStateNumber
  } from '@/api/recruitment/index';
  import CommonTableList from './components/CommonTableList.vue';
  import SettingProjectModal from './components/SettingProjectModal.vue';
  import ChoiceProjectModal from './components/ChoiceProjectModal.vue';
  import ChatProjectModel from '@/views/project/components/ChatProjectModel.vue';
  import { useRoute } from 'vue-router';
  import { Operation } from '@element-plus/icons-vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { ElMessageBox } from 'element-plus/es';
  import {
    getOrgPageList,
    deleteProject,
    changeProjectRcruitStatus
  } from '@/api/project/index';

  const route = useRoute();

  const active = ref(1);

  const ckeywords = ref('');

  const keywords = ref('');

  const showSettingDialog = ref(false);

  const tabs = ref([
    { label: '报名审核', name: 'tab1', model: 0, value: null },
    { label: '待签到', name: 'tab2', model: 1, value: null },
    { label: '筛选中', name: 'tab3', model: 3, value: null },
    { label: '待入组', name: 'tab4', model: 5, value: null },
    { label: '入组', name: 'tab5', model: 6, value: null },
    { label: '出组', name: 'tab6', model: 8, value: null },
    { label: '失败', name: 'tab7', model: 7, value: null }
  ]);

  const activeTab = ref('tab1');

  const showChoiceProjectDialog = ref(false);

  defineProps({
    // 表头背景
    tableHeader: String
    // 表格高度
  });

  const currentSiteId = ref(null);

  const projectData = ref({});

  const multCenterData = computed(() => {
    let data = projectData.value?.multiSiteJoinGroupDto || [];
    if (data.length === 0) return [];
    let constructedData = data.map((item, index) => {
      return {
        value: index + 1,
        name: item.siteName,
        col: { md: 4, sm: 12, xs: 24 },
        ...item
      };
    });
    console.log('#', constructedData);
    return constructedData;
  });

  const isHealthProject = computed(() => {
    return projectData.value?.projectRecruitType == 1;
  });

  const pmTitle = computed(() => {
    return projectData.value?.projectRecruitName || '';
  });

  const formatProject = computed(() => {
    return `${projectData.value?.joinGroupSucceed}/${projectData.value?.planJoinGroupTotalPeople}`;
  });

  const projectId = computed(() => {
    return projectData.value?.projectId;
  });

  const isMultipleSite = computed(() => {
    return projectData.value?.multipleSiteFlag == 2;
  });

  const chatDialogVisible = ref(false);

  const showChatDialog = async (row) => {
    chatDialogVisible.value = true;
  };

  const toSetting = () => {
    showSettingDialog.value = true;
  };

  const checkedProject = (item) => {
    currentSiteId.value = item?.siteId || null;
    getDataBluk();
  };

  const getTextColor = (type) => {
    if (type == 1) {
      return { color: '#209373' }; // 健康类型为绿色
    } else {
      return { color: '#F97316' }; // 患者类型为蓝色
    }
  };

  /** 下拉菜单点击事件 */
  const dropClick = async (key, row) => {
    if (key === 'remove') {
      try {
        await ElMessageBox.confirm(
          `您确定删除该招募职位吗？删除后不可撤销`,
          '提示',
          {
            type: 'warning',
            draggable: true,
            customStyle: {
              maxWidth: '600px'
            }
          }
        );
        await deleteProject(row?.projectId);
        EleMessage.success('操作成功');
        reload();
      } catch (e) {
        if (e != 'cancel') {
          EleMessage.error(e?.message);
        }
      }
    } else if (['open', 'publish'].includes(key)) {
      try {
        const params = {
          projectId: projectId.value,
          recruitStatus: 2
        };
        const res = await changeProjectRcruitStatus(params);
        EleMessage.success('操作成功');
        reload();
      } catch (e) {
        EleMessage.error(e?.message);
      }
    } else if (key === 'closed') {
      try {
        const params = {
          projectId: row?.projectId,
          recruitStatus: 4
        };
        const res = await changeProjectRcruitStatus(params);
        EleMessage.success('操作成功');
        reload();
      } catch (e) {
        console.log(e);
        EleMessage.error(e?.message);
      }
    } else if (key === 'setting') {
      toSetting();
    }
  };

  const energizeMagic = () => {
    tabs.value.forEach((tab) => {
      switch (tab.model) {
        case 0:
          tab.value = dataBluk.value.applyAudiNumber;
          break;
        case 1:
          tab.value = dataBluk.value.applySucceedNumber;
          break;
        case 3:
          tab.value = dataBluk.value.filteringNumber;
          break;
        case 5:
          tab.value = dataBluk.value.waitGroupNumber;
          break;
        case 6:
          tab.value = dataBluk.value.joinGroupSucceedNumber;
          break;
        case 8:
          tab.value = dataBluk.value.outGroupNumber;
          break;
        case 7:
          tab.value = dataBluk.value.failNumber;
          break;
        default:
          tab.value = null;
      }
    });
  };

  const dataBluk = ref({});

  const getDataBluk = async () => {
    const params = {
      siteId: currentSiteId.value || '',
      projectId: projectData.value?.projectId
    };
    const res = await getStateNumber(params);
    Object.assign(dataBluk.value, res);
    energizeMagic();
  };

  const query = async (id) => {
    currentSiteId.value = '';
    const projectId = id || route?.query?.projectId;
    const params = { projectId: projectId };
    const res = await getProjectJoinProgress(params);
    Object.assign(projectData.value, res?.data || {});
    getDataBluk();
  };

  const reSearch = (id) => {
    query(id);
  };

  query();
</script>

<style lang="scss" scoped>
  .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  .pTitle {
    margin-left: 10px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: bold;
    cursor: pointer;
    font-size: 22px;
    font-weight: bold;
    color: #3b426f;
  }
  .horizontal-line {
    height: 12px;
    background-color: #ecf2f9; /* 你可以根据需要更改颜色 */
    width: 100%; /* 横线宽度为100% */
  }
  .title {
    display: flex;
    align-content: center;
    margin-top: 8px;
  }

  .title-f {
    font-size: 18px;
    background-color: #fff7e8;
    color: #ffb457;
    border-color: #ffb457;
  }

  .app-link-title {
    font-size: 16px;
    margin-bottom: 4px;
    text-align: left;
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis;
  }
  .app-card {
    padding: 18px 12px;
    background-color: #fff;
  }
  .f-16 {
    font-size: 16px;
  }

  .mb-11 {
    margin-bottom: 11px;
  }

  .content-l {
    display: inline-block;
    align-items: center;
    margin-top: 14px;
    line-height: 37px;
    height: 37px;
    background: #ecf2f9;
    border-radius: 30px;
    padding: 0 15px;
  }

  .ss-content {
    margin-right: 12px;
    font-size: 16px;
    color: #5b5b64;
  }

  .highlight {
    background-color: #eff6ff;
  }

  :deep(.el-row) {
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
    overflow: auto;
  }

  :deep(.ele-check-card) {
    height: 93px;
    border-radius: 2px;
  }
</style>
