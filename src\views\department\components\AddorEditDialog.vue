<template>
  <ele-modal
    form
    maxable
    :width="700"
    :model-value="modelValue"
    :title="isUpdate ? '编辑' : '新增'"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-width="120px"
      label-position="right"
      label-suffix="："
    >
      <el-form-item
        prop="deptName"
        label="科室名称"
        :rules="{
          required: true,
          message: '请输入科室名称',
          trigger: 'blur'
        }"
      >
        <el-input v-model="form.deptName" :autofocus="true" />
      </el-form-item>
      <el-form-item
        v-for="(item, index) in form.piList"
        :key="index"
        label-width="0px"
      >
        <el-col :span="20">
          <el-form-item label="PI姓名" :prop="'piList.' + index + '.piName'">
            <el-input v-model="form.piList[index].piName" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="9" style="margin-left: 40px">
          <el-form-item label="工号">
            <el-input v-model="form.piList[index].jobNumber" />
          </el-form-item>
        </el-col> -->
        <el-col :span="2" class="icon">
          <el-icon v-if="index == form.piList.length - 1" @click="addRow"
            ><CirclePlus
          /></el-icon>
          <el-icon v-if="form.piList.length > 1" @click="deleteRow(index)"
            ><Remove
          /></el-icon>
        </el-col>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>
<script setup>
  import { ref, watch } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { editSiteDeptInfo, addSiteDeptInfo } from '@/api/department';
  import { CirclePlus, Remove } from '@element-plus/icons-vue';

  const emit = defineEmits(['success', 'update:modelValue']);

  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    data: Object
  });
  // 是否是修改
  const isUpdate = ref(false);
  // 提交状态
  const loading = ref(false);

  // 表单实例
  const formRef = ref(null);

  // 表单数据
  const { form, resetFields, assignFields } = useFormData({
    deptId: '',
    deptName: '',
    piList: [{ piName: '' }]
  });
  const addRow = () => {
    form.piList.push({ piName: '' });
  };
  const deleteRow = (index) => {
    form.piList.splice(index, 1);
  };
  /* 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      try {
        loading.value = true;
        const saveOrUpdate = isUpdate.value
          ? editSiteDeptInfo
          : addSiteDeptInfo;
        const res = await saveOrUpdate({ ...form });
        console.log(res);
        loading.value = false;
        updateModelValue(false);
        EleMessage.success('操作成功');
        emit('success');
      } catch (e) {
        loading.value = false;
        EleMessage.error(e);
      }
    });
  };

  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields(props.data);
          form.deptId = props.data?.id;
          if (!props.data?.deptPi) {
            form.piList = [{ piName: '' }];
          } else {
            const namesArray = props.data?.deptPi.split(',');
            form.piList = namesArray.map((name) => ({ piName: name }));
          }
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        isUpdate.value = false;
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
<style lang="scss" scoped>
  .icon {
    display: flex;
    align-items: center;
    margin-left: 20px;

    :deep(.el-icon) {
      font-size: 30px;
    }
  }
</style>
