<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="860"
    top="40px"
    v-model="visible"
    :title="isUpdate ? '修改公告' : '添加公告'"
    @open="handleOpen"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="90px"
      @submit.prevent=""
    >
      <el-form-item label="公告标题" prop="announcementTitle">
        <el-input
          clearable
          v-model="form.announcementTitle"
          placeholder="请输入公告标题"
        />
      </el-form-item>
      <el-row :gutter="16">
        <el-col :sm="12" :xs="24">
          <el-form-item label="生效时间" prop="dateRange">
            <el-date-picker
              unlink-panels
              type="datetimerange"
              v-model="form.dateRange"
              range-separator="-"
              value-format="YYYY-MM-DD HH:mm:ss"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :xs="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :value="1">已发布</el-radio>
              <el-radio :value="0">未发布</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="内容">
        <tinymce-editor
          ref="editorRef"
          :init="config"
          v-model="form.announcementDesc"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { addNewNotice, updateNewNotice } from '@/api/system/notice';
  import TinymceEditor from '@/components/TinymceEditor/index.vue';

  const props = defineProps({
    /** 修改回显的数据 */
    data: Object
  });

  const emit = defineEmits(['done']);

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 编辑器 */
  const editorRef = ref(null);

  /** 编辑器配置 */
  const config = ref({
    height: 380
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    announcementId: void 0,
    announcementTitle: '',
    status: 1,
    announcementDesc: '',
    dateRange: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    announcementTitle: [
      {
        required: true,
        message: '请输入公告标题',
        type: 'string',
        trigger: 'blur'
      }
    ],
    dateRange: [
      { required: true, message: '请选择时间范围', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (!value || value.length !== 2) {
            callback(new Error('请选择完整的时间范围'));
          } else {
            const [startTime, endTime] = value;
            if (new Date(startTime) >= new Date(endTime)) {
              callback(new Error('开始时间必须早于结束时间'));
            } else {
              callback(); // 校验通过
            }
          }
        },
        type: 'string',
        trigger: 'change'
      }
    ]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      const [startTime, endTime] = form.dateRange || [];
      const params = { ...form, startTime, endTime };
      const saveOrUpdate = isUpdate.value ? updateNewNotice : addNewNotice;
      saveOrUpdate(params)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          handleCancel();
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 弹窗打开事件 */
  const handleOpen = () => {
    if (props.data) {
      assignFields(props.data);
      form.dateRange = [props.data?.startTime, props.data?.endTime];
      isUpdate.value = true;
    } else {
      resetFields();
      isUpdate.value = false;
    }
    nextTick(() => {
      nextTick(() => {
        formRef.value?.clearValidate?.();
      });
    });
  };
</script>
