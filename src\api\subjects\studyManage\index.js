import request from '@/utils/subjectsrequest';
import requestBusiness from '@/utils/request-business';
import { download } from '@/utils/common';
import dayjs from 'dayjs';
let unique = 0;

function uuid(prefix) {
  const time = Date.now();
  const random = Math.floor(Math.random() * 1000000000);
  unique++;
  return prefix + '_' + random + unique + String(time);
}

/**
 * 查询用户签到记录
 * @param {*} data
 * @returns
 */
export async function queryUserCheckInRecord(params) {
  const res = await requestBusiness.get('/api/queryUserCheckInRecord', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 同步更新租户信息
 */
export async function syncUpdateOrganization(data) {
  const res = await request.post(
    `/sys/organization/syncUpdateOrganization`,
    data
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 同步更新用户信息
 */
export async function syncUpdateUsers(data) {
  const res = await request.post(`/sys/users/syncUsersUpdate`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * token快速登录
 */
export async function fastLogin(data) {
  const res = await request.post(`/fastLogin`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 查询试验信息列表
 */
export async function trialsList(data) {
  const res = await request.post(`/sys/trials/list`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return res.data.data;
}
/**
 * 查询受试者试验信息列表
 */
export async function subjecttrialList(data) {
  if (!data.trialsGuid) {
    return {
      code: 200,
      data: {
        list: [],
        total: 0
      }
    };
  }
  const res = await request.post(`/sys/subjecttrial/list`, data);
  if (res.data.code === 200) {
    res.data.data.list.forEach((item) => {
      item.uuid = uuid('uid');
    });
    return res.data.data;
  }
  return res.data;
}
/**
 * 放行
 */
export async function release(data) {
  const res = await request.get(`/sys/subjecttrial/release/${data.guid}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 标记入组未给药
 */
export async function markFalloff(params) {
  const res = await request.get(`/sys/subjecttrial/markFalloff`, { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 允许标记状态受试者信息
 */
export async function markSubjectTrialList(params) {
  const res = await request.get(`/sys/subjecttrial/markSubjectTrialList`, {
    params
  });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return [];
}
/**
 * 受试者信息导出
 */
export async function subjecttrialExport(data) {
  const res = await request({
    url: '/sys/subjecttrial/export',
    method: 'POST',
    data: data,
    responseType: 'blob'
  });
  download(
    res.data,
    `志愿者信息_${dayjs(new Date()).format('YYYYMMDDHHMMssSSS')}.xlsx`
  );
  return 'success';
}
/**
 * 查询完整信息
 */
export async function getCompleteInfo(data) {
  const res = await request.get(
    `/sys/subjecttrial/getCompleteInfo/${data.guid}/${data.type}`
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 查询受试者试验信息信息
 */
export async function subjecttrialInfo(data) {
  const res = await request.get(`/sys/subjecttrial/info/${data.guid}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 受试者照片信息（身份证、筛选照片、入组前筛选照片）
 */
export async function pictureData(params) {
  const res = await request.get(`/sys/subjecttrialdata/details/pictureData`, {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 获取打印卡牌
 */
export async function getPrintCards(data) {
  const res = await request.post(`/sys/subjecttrial/getPrintCards`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 获取试验记录
 */
export async function getTrailRecord(data) {
  const res = await request.get(
    `/sys/subjecttrial/getTrailRecord/${data.subjectsGuid}`
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 查询试验信息信息
 */
export async function trialsInfo(data) {
  const res = await request.get(`/sys/trials/info/${data.guid}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 筛选-检验
 */
export async function joinFilterCheck(params) {
  const res = await request.get(`/sys/subjecttrial/joinFilterCheck`, {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 读卡提醒（查询用户备忘录、不适宜）
 */
export async function idCardCheck(params) {
  const res = await request.get(`/sys/subjecttrial/idCardCheck`, { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 证照核对
 */
export async function certificateCheck(data) {
  const res = await request.post(`/sys/subjecttrial/certificateCheck`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 筛选
 */
export async function filter(data) {
  data.similaritytype = Number(data.similaritytype);
  const res = await request.post(`/sys/subjecttrial/filter`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 新的筛选
 */
export async function newfilter(data) {
  data.similaritytype = Number(data.similaritytype);
  const res = await request.post(`/sys/subjecttrial/filterNew`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 入组前筛选
 */
export async function joinFilter(data) {
  data.similaritytype = Number(data.similaritytype);
  const res = await request.post(`/sys/subjecttrial/joinFilter`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 身份证号查询
 */
export async function queryIdcard(data) {
  const res = await request.get(`/sys/subject/query/${data.idcard}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 标记不适宜
 */
export async function markBlacklist(data) {
  const res = await request.post(`/sys/subjecttrial/markBlacklist`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 获取最新一条不适宜人群
 */
export async function subjectblackLatest(data) {
  const res = await request.post(`/sys/subjectblacklist/latest`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 修改不适宜人群
 */
export async function subjectblackUpdate(data) {
  const res = await request.post(`/sys/subjectblacklist/update`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 筛选-修改受试者试验信息
 */
export async function filterUpdate(data) {
  const res = await request.post(`/sys/subjecttrial/filterUpdate`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 设置备忘录
 */
export async function setupMemo(data) {
  const res = await request.post(`/sys/subjecttrial/setupMemo`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 获取最新一条备忘录
 */
export async function memoLatest(data) {
  const res = await request.post(`/sys/memo/latest`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 修改备忘录
 */
export async function memoUpdate(data) {
  const res = await request.post(`/sys/memo/update`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 修改受试者试验信息
 */
export async function subjecttrialUpdate(data) {
  const res = await request.post(`/sys/subjecttrial/update`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 标记出组
 */
export async function markOutGroup(data) {
  const res = await request.post(`/sys/subjecttrial/markOutGroup`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 批量编辑
 */
export async function batchUpdate(data) {
  const res = await request.post(`/sys/subjecttrial/batchUpdate`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 标记入组
 */
export async function markJoinGroup(data) {
  const res = await request.post(`/sys/subjecttrial/markJoinGroup`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 统计本机构 项目待入组的处理人数
 */
export async function selectWaitJoinStatistic(params) {
  const res = await request.get(`/sys/subjecttrial/selectWaitJoinStatistic`, {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 查询机构信息列表
 */
export async function organizationList(data) {
  const res = await request.post(`/sys/organization/list`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return res.data;
}
/**
 * 标记未入组
 */
export async function markNotJoinGroup(data) {
  const res = await request.post(`/sys/subjecttrial/markNotJoinGroup`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 查询申办方信息列表
 */
export async function sponsorList(data) {
  const res = await request.post(`/sys/sponsor/list`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 保存试验信息
 */
export async function trialsSave(data) {
  const res = await request.post(`/sys/trials/save`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 修改试验信息
 */
export async function trialsUpdate(data) {
  const res = await request.post(`/sys/trials/update`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 查询试验信息
 */
export async function trialsProjectInfo(guid) {
  const res = await request.get(`/sys/trials/info/${guid}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 查询本机构专业
 */
export async function professionalList(data) {
  const res = await request.get(`/sys/trials/getProfessional`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
// 天眼查搜索公司
export async function companySearch(data) {
  // const res = await request.get('/sys/sponsor/company/search', {
  //   params
  // });
  // if (res.data.code === 200) {
  //   return res.data.data;
  // }
  // return res.data;
  const res = await request.post(`/sys/sponsor/company/search`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return res.data;
}

/**
 * 人员核对
 */
export async function personnelCheck(data) {
  const res = await request.post(`/sys/subjecttrial/personnelCheck`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 人员核对历史记录列表
 */
export async function selectPersonnelVerificationList(data) {
  const res = await request.post(
    `/sys/subjecttrial/selectPersonnelVerificationList`,
    data
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 人员核对历史记录导出
 */
export async function selectPersonnelVerificationExport(data) {
  const res = await request({
    url: '/sys/subjecttrial/selectPersonnelVerificationExport',
    method: 'POST',
    data: data,
    responseType: 'blob'
  });
  download(
    res.data,
    `人员核对记录_${dayjs(new Date()).format('YYYYMMDDHHMMssSSS')}.xlsx`
  );
  return 'success';
}

/**
 * 人员核对（低相似度）-是本人

 */
export async function personnelCheckUpdate(data) {
  const res = await request.post(
    `/sys/subjecttrial/personnelCheckUpdate`,
    data
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 标记不合格

 */
export async function markUnqualified(data) {
  const res = await request.post(`/sys/subjecttrial/markUnqualified`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
