import request from '@/utils/subjectsrequest';
import { download, toFormData } from '@/utils/common';
import dayjs from 'dayjs';

/**
 * 查询不适宜人群列表
 */
export async function subjectblackList(data) {
  const res = await request.post(`/sys/subjectblacklist/list`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导出不适宜人群
 */
export async function exportSubjectblack(params) {
  const res = await request({
    url: '/sys/subjectblacklist/export',
    method: 'POST',
    data: params,
    responseType: 'blob'
  });
  download(
    res.data,
    `不适宜人群_${dayjs(new Date()).format('YYYYMMDDHHMMssSSS')}.xlsx`
  );
}

/**
 * 修改不适宜人群
 */
export async function subjectblackUpdate(data) {
  const res = await request.post(`/sys/subjectblacklist/update`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 删除不适宜人群
 */
export async function subjectblackDelete(data) {
  const res = await request.post(`/sys/subjectblacklist/delete`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}

/**
 * 查询机构信息列表
 */
export async function organizationList(data) {
  const res = await request.post(`/sys/organization/list`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
