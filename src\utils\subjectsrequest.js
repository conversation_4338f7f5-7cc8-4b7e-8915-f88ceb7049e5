/**
 * axios实例
 */
import axios from 'axios';
import { unref } from 'vue';
import { ElMessageBox } from 'element-plus/es';
import { LAYOUT_PATH } from '@/config/setting';
import router from '@/router';
import { getSsoToken, setSsoToken } from './token-util';
import { logout } from './common';

/** 创建axios实例 */
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_SUBJECTS_BASE_API
});

/**
 * 添加请求拦截器
 */
service.interceptors.request.use(
  (config) => {
    // 添加token到header
    const token = getSsoToken();

    if (token && config.headers) {
      config.headers['token'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 添加响应拦截器
 */
service.interceptors.response.use(
  (res) => {
    // 登录过期处理
    if (res.data?.code === 401) {
      const { path, fullPath } = unref(router.currentRoute);
      if (path == LAYOUT_PATH) {
        logout(true, void 0, router.push);
      } else if (path !== '/login') {
        ElMessageBox.close();
        ElMessageBox.alert('登录状态已过期, 请退出重新登录!', '系统提示', {
          confirmButtonText: '重新登录',
          callback: (action) => {
            if (action === 'confirm') {
              logout(false, fullPath);
            }
          },
          draggable: true
        });
      }
      return Promise.reject(new Error(res.data.message));
    }
    // 续期token
    const newToken = res.headers['token'];
    if (newToken) {
      setSsoToken(newToken);
    }
    if (res.data?.data?.totalCount) {
      res.data.data.total = res.data.data.totalCount;
      res.data.data.records = res.data.data.list;
    }
    return res;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default service;
