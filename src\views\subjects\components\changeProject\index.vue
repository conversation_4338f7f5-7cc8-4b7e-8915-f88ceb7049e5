<template>
  <div style="display: flex; align-items: center">
    <!--<el-button
      v-if="loginUser.role == 2"
      type="primary"
      @click="handleAdd"
      >新增项目</el-button
    >-->
    <el-button
      type="primary"
      plain
      @click="dialogVisible = true"
      style="
        font-size: 18px;
        background-color: #fff7e8;
        color: #ffb457;
        border-color: #ffb457;
      "
      >选择项目</el-button
    >
    <!-- <el-tooltip
      effect="dark"
      content="切换项目"
      placement="top-start"
      :show-after="100"
    >
      <img
        src="@/assets/changeProject.png"
        style="width: 23px; height: 23px; cursor: pointer"
        alt=""
        @click="dialogVisible = true"
      />
    </el-tooltip>-->

    <div class="projectChangeprojectName" v-if="info.projectName">
      <el-tooltip
        effect="dark"
        :content="`（${orgDisabled ? selectParms.code : info.fullname}） ${
          selectParms.projectName
        }`"
        placement="top-start"
        :show-after="100"
      >
        <span @click="dialogVisible = true">
          {{
            `（${orgDisabled ? selectParms.code : info.fullname}）${selectParms.projectName}`
          }}
        </span>
      </el-tooltip>
    </div>
    <span @click="dialogVisible = true" class="projectChangeprojectName" v-else>
      请先选择试验项目
    </span>
  </div>
  <el-dialog v-model="dialogVisible" title="选择项目" width="1000" align-center>
    <el-tabs
      v-model="currentStatus"
      class="demo-tabs"
      @tab-click="currentStatusChange"
    >
      <el-tab-pane label="进行中" name="incomplete" />
      <el-tab-pane label="已完成" name="completed" />
    </el-tabs>
    <div class="tableForm">
      <el-form inline :model="params">
        <el-form-item label="筛查机构：" v-if="!orgDisabled">
          <el-select
            v-model="params.designativeOrganizationGuid"
            :disabled="orgDisabled"
            placeholder="请选择或搜索筛查机构"
            filterable
            remote
            reserve-keyword
            :remote-method="remoteMethod"
            :loading="loading"
            style="width: 200px"
            @change="organizationChange"
            remote-show-suffix
          >
            <el-option
              v-for="item in options"
              :key="item.guid"
              :label="item.fullname"
              :value="item.guid"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关键字：">
          <el-input
            v-model="params.keywords"
            placeholder="请输入关键字"
            clearable
            style="width: 200px"
            maxlength="99"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="reload">搜索</el-button>
          <el-button type="info" @click="(params.keywords = ''), reload()"
            >重置</el-button
          >
          <el-button
            v-if="userStore.authorities.includes('project:screen:add')"
            v-permission="'project:screen:add'"
            type="primary"
            @click="handleAdd"
            >新增项目</el-button
          >
        </el-form-item>
      </el-form>
      <!-- <div v-if="loginUser.role == 2">
        <el-button type="primary" @click="handleAdd"
          >新增项目</el-button
        >
      </div> -->
    </div>
    <ele-pro-table
      ref="tableRef"
      :columns="columns"
      :datasource="datasource"
      :toolbar="false"
      :loadOnCreated="orgDisabled"
      highlight-current-row
      :bottom-line="tableFullHeight"
      emptyText="暂无数据"
      height="40vh"
    >
      <template #status="{ row }">
        <span v-if="row.status == 1" style="color: #67c32a">进行中</span>
        <span v-if="row.status == 2" style="color: #999999">已完成</span>
        <!-- <span v-if="row.status == 3" style="color: #a69ea8">中止</span> -->
        <!-- <span v-if="row.status == 4" style="color: #656ad2">终止</span> -->
      </template>
      <template #action="{ row }">
        <el-space>
          <el-link type="primary" @click="change(row)">选择</el-link>
        </el-space>
      </template>
    </ele-pro-table>
  </el-dialog>
  <addProject
    v-model="showCreate"
    :data="current"
    @done="reload"
    :isCopyAdd="isCopyAdd"
  />
</template>

<script setup>
  import { ref, nextTick, computed, watch } from 'vue';
  import {
    organizationList,
    trialsList
  } from '@/api/subjects/studyManage/index.js';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import addProject from '@/views/subjects/components/addProject/index.vue';
  import { useUserStore } from '@/store/modules/user';
  const userStore = useUserStore();
  /** 当前用户信息 */
  const loginUser = computed(() => userStore.info ?? {});
  const emit = defineEmits(['done']);
  const dialogVisible = ref(false);

  const info = ref({ projectName: '' });
  const params = ref({ status: '1' });
  const props = defineProps({
    orgDisabled: Boolean,
    projectInfo: Object
  });

  const loading = ref(false);
  const options = ref([]);
  const isCopyAdd = ref(false);
  // 新增修改弹框控制
  const showCreate = ref(false);
  // 当前选中数据
  const current = ref({});
  const handleAdd = () => {
    isCopyAdd.value = false;
    showCreate.value = true;
    current.value = null;
  };
  const selectParms = ref({
    projectName: '',
    projectId: '',
    code: ''
  });
  const change = (row) => {
    info.value.projectName = row.name;
    selectParms.value.projectName = row.name;
    selectParms.value.projectId = row.guid;
    selectParms.value.code = row.code;
    info.value.code = row.code;
    info.value.projectId = row.guid;
    info.value.guid = row.guid;
    info.value.status = row.status;
    info.value.createDate = row.createDate;
    info.value.organizationGuid = params.value.designativeOrganizationGuid;
    info.value.fullname = options.value.find(
      (item) => item.guid == params.value.designativeOrganizationGuid
    ).fullname;
    info.value = { ...info.value, ...row };
    emit('done', info.value);
    EleMessage.success('切换成功');
    dialogVisible.value = false;
  };
  const organizationChange = (e) => {
    if (e) {
      params.value.designativeOrganizationGuid = e;
    }
    reload();
  };
  const remoteMethod = (query) => {
    if (query) {
      loading.value = true;
      organizationList({
        keywords: query,
        pageNumber: 1,
        pageSize: 100
      }).then((res) => {
        loading.value = false;
        options.value = res.list;
      });
    } else {
      organizationList({
        pageNumber: 1,
        pageSize: 100
      }).then((res) => {
        options.value = res.list;
      });
    }
  };
  const tableRef = ref(null);
  /** 刷新表格 */
  const reload = () => {
    if (!params.value.designativeOrganizationGuid) {
      EleMessage.warning('请选择筛查机构！');
      return;
    }
    tableRef.value?.reload?.({ where: params.value });
  };
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      label: '序号',
      width: 65,
      align: 'center',
      showOverflowTooltip: true,
      fixed: 'left'
    },
    {
      prop: 'code',
      label: '试验编号',
      width: 160,
      showOverflowTooltip: true
    },
    {
      prop: 'schemeCode',
      label: '方案编号',
      width: 160,
      showOverflowTooltip: true
    },
    {
      prop: 'name',
      label: '试验名称',
      showOverflowTooltip: true
    },
    /*{
      prop: 'status',
      label: '试验状态',
      width: 110,
      slot: 'status',
      filterMultiple: false,
      filters: [
        {
          text: '进行中',
          value: '1'
        },
        {
          text: '已完成',
          value: '2'
        }
      ],
      showOverflowTooltip: true
    },*/
    {
      columnKey: 'action',
      label: '操作',
      hideInSetting: true,
      width: 80,
      slot: 'action',
      hideInPrint: true,
      hideInExport: true,
      fixed: 'right'
    }
  ]);
  const currentStatus = ref('incomplete');
  const currentStatusChange = (type) => {
    currentStatus.value = type.props.name;
    if (currentStatus.value == 'incomplete') {
      params.value.status = 1;
    }
    if (currentStatus.value == 'completed') {
      params.value.status = 2;
    }
    reload();
  };
  const datasource = async ({ page, limit, where, orders, filters }) => {
    if (!params.value.designativeOrganizationGuid) {
      return {
        list: [],
        total: 0
      };
    }
    return trialsList({
      guid: params.value.designativeOrganizationGuid,
      ...filters,
      // ...where,
      status: params.value.status,
      keywords: params.value.keywords,
      orderBy: orders.sort,
      orderItem: orders.order,
      pageNumber: page,
      pageSize: limit
    });
  };
  const initData = () => {
    if (props?.projectInfo?.projectId) {
      params.value.designativeOrganizationGuid = props.projectInfo.projectId;
      options.value = [
        {
          fullname: props.projectInfo.projectName,
          guid: props.projectInfo.projectId
        }
      ];
      info.value.projectName = props.projectInfo.projectName;
      info.value.code = props.projectInfo.code;
      info.value.status = props.projectInfo.status;
      info.value.createDate = props.projectInfo.createDate;
      selectParms.value.projectName = props.projectInfo.projectName;
      selectParms.value.projectId = props.projectInfo.projectId;
      selectParms.value.code = props.projectInfo.code;
      selectParms.value.status = props.projectInfo.status;
      selectParms.value.createDate = props.projectInfo.createDate;
    }
    console.log('props.projectInfo====>', props.projectInfo);
    info.value = { ...info.value, ...selectParms.value };
  };
  watch(
    () => props.projectInfo,
    () => {
      initData();
    },
    { deep: true }
  );
  nextTick(() => {
    initData();
  });
</script>
<style>
  .projectChangeprojectName {
    margin-left: 10px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: bold;
    cursor: pointer;
    font-size: 22px;
    font-weight: bold;
    color: #3b426f;
  }
</style>
<style lang="scss" scoped>
  .tableForm {
    display: flex;
    justify-content: space-between;
  }
  :deep(.el-tabs__nav-wrap::after) {
    height: 1px !important;
  }
</style>
