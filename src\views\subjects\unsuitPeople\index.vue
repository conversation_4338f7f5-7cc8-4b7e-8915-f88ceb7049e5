<template>
  <div class="index">
    <ele-card
      flex-table
      :body-style="{ padding: '4px 0 20px 0', overflow: 'hidden' }"
      style="height: 100%"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        v-model:selections="selections"
        :columns="columns"
        :datasource="datasource"
        :toolbar="{ theme: tableHeader }"
        highlight-current-row
        :bottom-line="tableFullHeight"
        cache-key="workAuditCheckTable20"
      >
        <!-- <smart-form
          v-model="searchExpand"
          ref="searchRef"
          :entityFileds="searchConfig"
          @search="reload" 
        />-->
        <!-- 表头左侧功能按钮 -->
        <template #toolbar>
          <!-- <div class="top-search3"> -->
          <!-- <smart-search :entityFileds="searchConfig" @search="reloadSearch" /> -->
          <!-- <div class="tableForm3"> -->
          <!-- <el-button
                type="warning"
                class="ele-btn-icon"
                :icon="ArrowDown"
                plain
                @click="downLoad"
                style="margin-right: 20px"
              >
                导出
              </el-button> -->
          <div class="tableForm">
            <el-form inline :model="params">
              <el-form-item label="标记机构：" v-if="loginUser.role == 99">
                <el-select
                  v-model="params.designativeOrganizationGuid"
                  placeholder="请选择或搜索标记机构"
                  clearable
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="remoteMethod"
                  style="width: 200px"
                >
                  <el-option
                    v-for="(dict, index) in organizaList"
                    :key="index"
                    :value="dict.guid"
                    :label="dict.fullname"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="姓名：">
                <el-input
                  v-model="params.subjectsName"
                  placeholder="请输入姓名"
                  clearable
                  style="width: 200px"
                  maxlength="99"
                />
              </el-form-item>
              <el-form-item label="身份证号：">
                <el-input
                  v-model="params.idCard"
                  placeholder="请输入身份证号"
                  clearable
                  style="width: 200px"
                  maxlength="99"
                />
              </el-form-item>
              <!--<el-form-item label="标记原因：">
                <el-input
                  v-model="params.explain"
                  placeholder="请输入标记原因"
                  clearable
                  style="width: 200px"
                  maxlength="99"
                />
              </el-form-item>-->
              <el-form-item>
                <el-button type="primary" plain @click="reload">搜索</el-button>
                <el-button type="primary" plain @click="showHighSearch = true">
                  高级检索</el-button
                >
                <el-button type="info" @click="(params = {}), reload()"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </template>

        <!-- 表头工具按钮 -->
        <!-- <template #tools>
          <el-space size="default" :wrap="true">
            <el-link type="primary" @click="searchExpand = !searchExpand">
              {{ searchExpand ? '普通搜索' : '高级搜索' }}
            </el-link>
            <el-divider direction="vertical" style="margin: 0" />
          </el-space>
        </template> -->
        <template #tools>
          <div class="ele-tool" @click="downLoad">
            <img
              src="@/assets/exportIcon.svg"
              alt=""
              style="width: 19px"
            />
          </div>
        </template>
        <template #action="{ row }">
          <el-space>
            <el-link
              type="primary"
              :underline="false"
              @click="handleAddorEdit(row)"
              style="margin-right: 10px; color: #507aff"
            >
              编辑
            </el-link>
            <!-- <el-divider direction="vertical" /> -->
            <el-link
              type="primary"
              :underline="false"
              @click="handleDelete(row)"
              style="color: #507aff"
            >
              解除
            </el-link>
          </el-space>
        </template>
        <template #idCard="{ row }">
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding-right: 10px;
            "
          >
            <span>{{ $PhoneOrIdCrad(row.idCard, 'idCard') }}</span>
            <img
              v-if="getIdCard(row)"
              :src="row.isShow ? eyeOpen : eyeClose"
              style="
                transform: scale(1.28);
                cursor: pointer;
                margin: 0 5px;
                width: 14px;
                height: 14px;
              "
              @click="changeImg(row)"
            />
          </div>
        </template>
        <template #telphone="{ row }">
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding-right: 10px;
            "
          >
            <span>{{ $PhoneOrIdCrad(row.telephone, 'phone') }}</span>
            <img
              v-if="getTelphone(row)"
              :src="row.isShow2 ? eyeOpen : eyeClose"
              style="
                transform: scale(1.28);
                cursor: pointer;
                margin: 0 5px;
                width: 14px;
                height: 14px;
              "
              @click="changeImg2(row)"
            />
          </div>
        </template>
        <template #valid="{ row }">
          <el-space>
            <el-link
              type="info"
              v-if="row.valid == 0"
              :underline="false"
              style="color: #999999"
              >否</el-link
            >
            <el-link
              type="primary"
              v-if="row.valid == 1"
              :underline="false"
              style="color: #67c32a"
              >是</el-link
            >
          </el-space>
        </template>
      </ele-pro-table>
      <!-- 弹窗 -->
    </ele-card>
    <editUnsuit v-model="showCreate" :data="current" @done="reload" />
    <highSearchModal
      v-model="showHighSearch"
      :data="params"
      :orgList="organizaList"
      @done="highSearchLoad"
      v-if="showHighSearch"
    />
  </div>
</template>
<script>
  export default {
    name: 'UnsuitPeople'
  };
</script>
<script setup>
  import { ref, computed, nextTick } from 'vue';
  import eyeOpen from '@/assets/eyeOpen.png';
  import eyeClose from '@/assets/eyeClose.png';
  import {
    subjectblackList,
    exportSubjectblack,
    subjectblackDelete,
    organizationList
  } from '@/api/subjects/unsuitPeople/index.js';
  import {
    validIdCard
  } from '@/utils/common.js';
  import { getCompleteInfo } from '@/api/subjects/studyManage/index.js';
  import editUnsuit from './components/editUnsuit.vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { ElMessageBox } from 'element-plus/es';
  import highSearchModal from './components/highSearchModal.vue';
  import { Icon } from '@iconify/vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';
  const userStore = useUserStore();
  /** 当前用户信息 */
  const loginUser = computed(() => userStore.info ?? {});
  const router = useRouter();
  defineProps({
    // 表头背景
    tableHeader: String,
    // 表格高度
    tableFullHeight: Boolean
  });
  const params = ref({
    pageNumber: 1,
    pageSize: 15
  });
  //高级检索弹框
  const showHighSearch = ref(false);

  // 表格列配置
  const columns2 = ref([
    // {
    //   type: 'selection',
    //   columnKey: 'selection',
    //   width: 48,
    //   align: 'center',
    //   fixed: 'left'
    // },
    {
      type: 'index',
      columnKey: 'index',
      label: '序号',
      minWidth: 65,
      align: 'center',
      showOverflowTooltip: true,
      fixed: 'left',
      isShow: true
    },
    {
      prop: 'organizationName',
      label: '标记机构',
      minWidth: 220,
      showOverflowTooltip: true,
      isShow: loginUser.value.role == 99 ? true : false
    },
    {
      prop: 'subjectsName',
      label: '姓名',
      minWidth: 90,
      showOverflowTooltip: true,
      isShow: true
    },
    {
      prop: 'idCard',
      label: '身份证号',
      width: 240,
      showOverflowTooltip: true,
      slot: 'idCard',
      isShow: true
    },
    {
      prop: 'telephone',
      label: '电话号码',
      width: 170,
      showOverflowTooltip: true,
      slot: 'telphone',
      isShow: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'usersName',
      label: '标记人',
      minWidth: 100,
      showOverflowTooltip: true,
      isShow: true
    },
    {
      prop: 'date',
      label: '标记日期',
      minWidth: 120,
      showOverflowTooltip: true,
      isShow: true
    },
    {
      prop: 'indate',
      label: '有效期至',
      minWidth: 120,
      showOverflowTooltip: true,
      isShow: true
    },
    {
      prop: 'valid',
      label: '是否有效',
      minWidth: 100,
      showOverflowTooltip: true,
      slot: 'valid',
      isShow: true
    },
    {
      prop: 'explain',
      label: '标记原因',
      minWidth: 250,
      showOverflowTooltip: true,
      isShow: true
    },
    {
      columnKey: 'action',
      label: '操作',
      hideInSetting: true,
      width: 120,
      align: 'left',
      resizable: false,
      slot: 'action',
      fixed: 'right',
      isShow: true
    }
  ]);
  const columns = ref([]);
  columns.value = columns2.value.filter((s) => s.isShow);
  // 表格选中数据
  const selections = ref([]);
  // 新增修改弹框控制
  const showCreate = ref(false);
  // 当前选中数据
  const current = ref({});
  // 表格实例
  const tableRef = ref(null);
  const organizaList = ref([]);
  const organizaCache = ref([]);
  const getTelphone = (row) => {
    let idCard = row.telephone;
    if (idCard) {
      // 获取前3位
      const firstPart = idCard.slice(0, 3);
      // 生成中间4位的星号
      const middlePart = '*'.repeat(4);
      // 获取后4位
      const lastPart = idCard.slice(7);

      // 拼接成新的字符串
      return firstPart + middlePart + lastPart;
    } else {
      return '';
    }
  };
  const changeImg2 = (row) => {
    row.isShow2 = !row.isShow2;
    getCompleteInfo({ guid: row.subjectsGuid, type: '2' }).then((res) => {
      row.telephone = res.msg;
      row.telephone = getPhone(row);
    });
    return;
    /*let idCard = row.telephone;
    if (row.isShow2 && row.telephone) {
      // 获取前3位
      const firstPart = idCard.slice(0, 3);
      // 生成中间4位的星号
      const middlePart = '*'.repeat(4);
      // 获取后4位
      const lastPart = idCard.slice(7);

      // 拼接成新的字符串
      return firstPart + middlePart + lastPart;
    } else {
      return idCard;
    }*/
  };
  const getCard = (row) => {
    try {
      if (row.isShow) {
        return row.idCard;
      }
      return row.idCard.replace(/(\d{6})\d+(\d{4})/, '$1********$2');
    } catch (error) {
      console.log(error);
      return row.idCard;
    }
  };
  const getPhone = (row) => {
    try {
      if (row.isShow2) {
        return row.telephone;
      }
      return row.telephone.replace(/(\d{3})\d+(\d{4})/, '$1****$2');
    } catch (error) {
      console.log(error);
      return row.telephone;
    }
  };
  const changeImg = (row) => {
    row.isShow = !row.isShow;
    getCompleteInfo({ guid: row.subjectsGuid, type: '1' }).then((res) => {
      row.idCard = res.msg;
      row.idCard = getCard(row);
    });
    return;
    /*let idCard = row.idCard;
    if (row.isShow && idCard) {
      // 获取前6位
      const firstPart = idCard.slice(0, 6);
      // 生成中间8位的星号
      const middlePart = '*'.repeat(8);
      // 获取后4位
      const lastPart = idCard.slice(14);

      // 拼接成新的字符串
      return firstPart + middlePart + lastPart;
    } else {
      return idCard;
    }*/
  };
  const getIdCard = (row) => {
    let idCard = row.idCard;
    if (idCard) {
      // 获取前6位
      const firstPart = idCard.slice(0, 6);
      // 生成中间8位的星号
      const middlePart = '*'.repeat(8);
      // 获取后4位
      const lastPart = idCard.slice(14);

      // 拼接成新的字符串
      return firstPart + middlePart + lastPart;
    }
    return '';
  };
  // 表格数据源
  const datasource = ({ page, limit, where }) => {
    return subjectblackList({
      ...where,
      pageNumber: page,
      pageSize: limit
    });
  };
const reload = () => {
    if (params.value.idCard) {
      params.value.idCard = params.value.idCard.replace(/\s+/g, "");
      if (!validIdCard(params.value.idCard)) {
        EleMessage.error('请输入完整的身份证号');
        return;
      }
    }
    tableRef.value?.reload?.({ where: params.value });
  };
  const handleAddorEdit = (row = null) => {
    showCreate.value = true;
    current.value = row;
  };
  const highSearchLoad = (value) => {
    params.value = value.p;
    organizaList.value = value.o;
    reload();
  };
  /** 删除按钮操作 */
  const handleDelete = (row) => {
    ElMessageBox.confirm(`确定要解除该不适宜人群信息吗？`, '提示', {
      confirmButtonText: '是',
      cancelButtonText: '否',
      center: true
    }).then(async () => {
      try {
        await subjectblackDelete({ guid: row.guid }).then((res) => {
          if (res.code == 200) {
            EleMessage.success(`操作成功！`);
            reload();
          } else {
            EleMessage.error(res.msg);
          }
        });
      } catch (e) {
        EleMessage.error(e);
      }
    });
  };
  /* 导出数据 */
const downLoad = () => {
    if (params.value.idCard) {
      params.value.idCard = params.value.idCard.replace(/\s+/g, "");
      if (!validIdCard(params.value.idCard)) {
        EleMessage.error('请输入完整的身份证号');
        return;
      }
    }
    const loading = EleMessage.loading('请求中..');
    // tableRef.value?.fetch?.(({ where }) => {
    if (!params.value.pageNumber) {
      params.value.pageNumber = 1;
      params.value.pageSize = 15;
    }
    exportSubjectblack(params.value)
      .then(() => {
        loading.close();
      })
      .catch((e) => {
        loading.close();
        EleMessage.error(e.msg);
      });
    // });
  };
  const remoteMethod = (query) => {
    if (query) {
      organizationList({ keywords: query, pageNumber: 1, pageSize: 100 }).then(
        (res) => {
          organizaList.value = res.data.list;
        }
      );
    } else {
      organizaList.value = organizaCache.value;
    }
  };
  nextTick(() => {
    organizationList({
      pageNumber: 1,
      pageSize: 50,
      orderBy: '',
      orderItem: 'asc'
    }).then((res) => {
      console.log(res);
      organizaList.value = res.data.list;
      organizaCache.value = res.data.list;
    });
  });
</script>
<style lang="scss" scoped>
  .index {
    // width: 100%;
    height: calc(100vh - 81px - 14px);
    background: #ffffff;
    border-radius: 8px;
    padding: 0 20px;
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    overflow-x: hidden;
  }
  .top-search3 {
    display: flex;
  }
  .tableForm3 {
    display: flex;
    flex-wrap: wrap;
  }
  :deep(.ele-toolbar .ele-toolbar-tools) {
    position: relative;
    // top: -7px !important;
  }
</style>
