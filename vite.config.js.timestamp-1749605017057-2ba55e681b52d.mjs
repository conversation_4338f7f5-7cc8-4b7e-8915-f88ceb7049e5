// vite.config.js
import { defineConfig, loadEnv } from "file:///D:/zzlProject/static-recruit-web/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/zzlProject/static-recruit-web/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { resolve } from "path";
import Compression from "file:///D:/zzlProject/static-recruit-web/node_modules/vite-plugin-compression/dist/index.mjs";
import Components from "file:///D:/zzlProject/static-recruit-web/node_modules/unplugin-vue-components/dist/vite.js";
import { ElementPlusResolver } from "file:///D:/zzlProject/static-recruit-web/node_modules/unplugin-vue-components/dist/resolvers.js";
import { EleAdminResolver } from "file:///D:/zzlProject/static-recruit-web/node_modules/@hnjing/zxzy-admin-plus/es/utils/resolvers.js";
import VueDevTools from "file:///D:/zzlProject/static-recruit-web/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
var vite_config_default = defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd());
  const isBuild = command === "build";
  const alias = {
    "@/": resolve("src") + "/",
    "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js"
  };
  const plugins = [vue()];
  if (isBuild) {
    plugins.push(
      Components({
        dts: false,
        resolvers: [
          ElementPlusResolver({
            importStyle: "sass"
          }),
          EleAdminResolver({
            importStyle: "sass"
          })
        ]
      })
    );
    plugins.push(
      Compression({
        disable: !isBuild,
        threshold: 10240,
        algorithm: "gzip",
        ext: ".gz"
      })
    );
    alias["./as-needed"] = "./global-import";
  } else {
    plugins.push(VueDevTools());
    alias["./as-needed"] = "./global-import";
  }
  return {
    resolve: { alias },
    plugins,
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`,
          silenceDeprecations: ["legacy-js-api"]
        }
      }
    },
    server: {
      host: "0.0.0.0",
      port: Number(env.VITE_APP_PORT),
      open: true,
      // 运行是否自动打开浏览器
      proxy: {
        // 反向代理解决跨域
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_APP_BASE_API_URL,
          // 线上接口地址
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp("^" + env.VITE_APP_BASE_API), "")
          // 替换 /dev-api 为 target 接口地址
        },
        // [env.VITE_APP_ADMIN_BASE_API]: {
        //   target: env.VITE_APP_ADMIN_BASE_API_URL,
        //   changeOrigin: true,
        //   rewrite: path =>
        //     path.replace(new RegExp("^" + env.VITE_APP_ADMIN_BASE_API), "") // 文件分片微服务
        // },
        [env.VITE_APP_BUSINESS_BASE_API]: {
          target: env.VITE_APP_BUSINESS_BASE_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(new RegExp("^" + env.VITE_APP_BUSINESS_BASE_API), "")
          // 业务微服务
        },
        warmup: {
          clientFiles: ["./index.html", "./src/{views,components}/*"]
        }
      }
    },
    optimizeDeps: {
      include: [
        "echarts/core",
        "echarts/charts",
        "echarts/renderers",
        "echarts/components",
        "vue-echarts",
        "echarts-wordcloud",
        "sortablejs",
        "vuedraggable"
      ]
    },
    build: {
      target: "chrome63",
      chunkSizeWarningLimit: 2e3,
      terserOptions: command === "build" ? {
        // 仅在构建时生效
        compress: {
          drop_console: true,
          // 移除console
          drop_debugger: true
          // 移除debugger
        }
      } : {}
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
