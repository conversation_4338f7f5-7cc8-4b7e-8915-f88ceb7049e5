<!-- 修改密码弹窗 -->
<template>
  <ele-modal
    form
    :width="600"
    title="更正"
    :append-to-body="true"
    v-model="visible"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-position="top"
      :rules="rules"
      label-width="90px"
      @submit.prevent=""
    >
      <el-form-item v-if="props.model != 7" label="失败原因">
        <el-input
          v-model="form.failReason"
          :maxlength="100"
          :rows="3"
          type="textarea"
          placeholder="请输入失败原因"
        />
      </el-form-item>
      <el-form-item label="更正原因" prop="correctionReason">
        <el-input
          v-model="form.correctionReason"
          :maxlength="100"
          :rows="3"
          type="textarea"
          placeholder="请输入更正原因"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleOk">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { correctionApplyStatus } from '@/api/recruitment/index';

  const emit = defineEmits(['update:modelValue', 'done']);

  const props = defineProps({
    applyId: String,
    model: String
  });

  const showFailReason = computed(() => {});

  const showCorrectionReason = computed(() => {});

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交loading */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    applyId: null,
    failReason: '',
    correctionReason: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    correctionReason: [
      {
        required: true,
        message: '请输入更正原因',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭弹窗 */
  const handleCancel = () => {
    updateModelValue(false);
  };

  /** 保存修改 */
  const handleOk = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      try {
        const params = {
          ...form,
          applyId: props.applyId
        };
        const res = await correctionApplyStatus(params);
        EleMessage.success('操作成功');
        emit('done');
        updateModelValue(false);
      } catch (e) {
        loading.value = false;
        EleMessage.error(e.message);
      }
    });
  };

  /** 弹窗关闭事件 */
  const handleClosed = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    loading.value = false;
  };
</script>
