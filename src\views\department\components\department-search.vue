<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 16px 0px 16px', overflow: 'hidden' }">
    <el-form label-width="100px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="科室名称">
            <el-input
              clearable
              v-model.trim="form.deptName"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="PI姓名">
            <el-input
              clearable
              v-model.trim="form.deptPi"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
            <el-button type="primary" @click="emit('add')">新增</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search', 'add']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    deptName: '',
    deptPi: ''
  });

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };

  defineExpose({ resetFields });
</script>
