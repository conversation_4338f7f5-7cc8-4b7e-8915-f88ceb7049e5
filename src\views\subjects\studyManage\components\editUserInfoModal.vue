<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1200"
    :model-value="modelValue"
    title="编辑"
    @update:modelValue="updateModelValue"
    class="simplebar-modal"
  >
    <div v-if="loadingShow">
      <ele-loading :loading="loadingShow" text="加载中" background="red">
        <div style="width: 100%; height: 200px; background: #ffffff"></div>
      </ele-loading>
    </div>
    <div data-simplebar class="modal-dialog" style="padding: 0 15px" v-else>
      <el-form
        :model="userInfo"
        label-width="120"
        ref="formRule"
        :rules="rules"
        label-position="left"
      >
        <div class="modal-title">志愿者身份信息</div>
        <el-row :gutter="40">
          <el-col :span="8">
            <el-form-item label="身份证号" style="width: 100%">
              <el-input
                v-model="userInfo.idcard"
                placeholder="请输入身份证号"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              >
                <template #suffix>
                  <img
                    src="@/assets/subjects/eyeOpen.png"
                    style="
                      transform: scale(1.28);
                      cursor: pointer;
                      margin: 0 5px;
                      width: 14px;
                      height: 14px;
                    "
                    v-if="showEye"
                    @click="getShowEye(userInfo)"
                  />
                  <img
                    src="@/assets/subjects/eyeClose.png"
                    style="
                      transform: scale(1.28);
                      cursor: pointer;
                      margin: 0 5px;
                      width: 14px;
                      height: 14px;
                    "
                    v-if="!showEye"
                    @click="getShowEye(userInfo)"
                  />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="姓名" style="width: 100%">
              <el-input
                v-model="userInfo.name"
                placeholder="请输入姓名"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="姓名缩写" style="width: 100%">
              <el-input
                v-model="userInfo.acronym"
                placeholder="请输入姓名缩写"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="民族" style="width: 100%">
              <el-input
                v-model="userInfo.nation"
                placeholder="请输入民族"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别" style="width: 100%">
              <el-input
                v-model="userInfo.sex"
                placeholder="请输入性别"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年龄" style="width: 100%">
              <el-input
                v-model="userInfo.age"
                placeholder="请输入年龄"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出生年月" style="width: 100%">
              <el-input
                v-model="userInfo.birthday"
                placeholder="请输入出生年月"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="telephone" style="width: 100%">
              <phoneInput
                v-model="userInfo.telephone"
                placeholder="请输入联系电话"
                clearable
                :style="{ width: '100%' }"
                maxlength="13"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="紧急电话" prop="telephone2" style="width: 100%">
              <phoneInput
                v-model="userInfo.telephone2"
                placeholder="请输入紧急电话"
                clearable
                :style="{ width: '100%' }"
                maxlength="13"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
            <el-form-item label="地址" style="width: 100%">
              <el-input
                v-model="userInfo.address"
                placeholder="请输入地址"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col> -->
        </el-row>

        <div style="margin-top: 20px" v-if="data.status == 2">
          <div class="modal-title">志愿者试验信息</div>
          <el-row :gutter="40">
            <!-- <el-col :span="8">
              <el-form-item label="身高(cm)" style="width: 100%">
                <el-input
                  v-model="userInfo.stature"
                  placeholder="请输入身高"
                  clearable
                  style="width: 100%"
                  maxlength="99"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="体重(kg)" style="width: 100%">
                <el-input
                  v-model="userInfo.weigh"
                  placeholder="请输入体重"
                  clearable
                  style="width: 100%"
                  maxlength="99"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="紧急电话"
                prop="telephone2"
                style="width: 100%"
              >
                <phoneInput
                  v-model="userInfo.telephone2"
                  placeholder="请输入紧急电话"
                  clearable
                  :style="{ width: '100%' }"
                  maxlength="13"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="备用电话"
                prop="telephone3"
                style="width: 100%"
              >
                <phoneInput
                  v-model="userInfo.telephone3"
                  placeholder="请输入备用电话"
                  clearable
                  :style="{ width: '100%' }"
                  maxlength="13"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="电子邮箱" style="width: 100%">
                <el-input
                  v-model="userInfo.email"
                  placeholder="请输入电子邮箱"
                  clearable
                  style="width: 100%"
                  maxlength="99"
                />
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="筛选号" style="width: 100%">
                <el-input
                  v-model="userInfo.filterno"
                  placeholder="请输入筛选号"
                  clearable
                  style="width: 100%"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="筛选时间" style="width: 100%">
                <el-input
                  v-model="userInfo.filterdate"
                  placeholder="请输入筛选时间"
                  clearable
                  style="width: 100%"
                  :disabled="true"
                  maxlength="99"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="入组号" style="width: 100%">
                <el-input
                  v-model="userInfo.joinno"
                  placeholder="请输入入组号"
                  clearable
                  style="width: 100%"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="入组时间" style="width: 100%">
                <el-date-picker
                  v-model="userInfo.joindate"
                  type="date"
                  placeholder="请输入入组时间"
                  :disabled-date="disableddate"
                  clearable
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="末次给药时间" style="width: 100%">
                <el-date-picker
                  v-model="userInfo.lastdosedate"
                  type="date"
                  placeholder="请选择末次给药时间"
                  :disabled-date="disableddate"
                  clearable
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="末次访视时间" style="width: 100%">
                <el-date-picker
                  v-model="userInfo.trialedate"
                  type="date"
                  placeholder="请选择末次访视时间"
                  :disabled-date="disableddate"
                  clearable
                  style="width: 100%"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="依从性评价" style="width: 100%">
                <el-select
                  v-model="userInfo.evaluate"
                  placeholder="请选择依从性评价"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="优" value="优" />
                  <el-option label="良" value="良" />
                  <el-option label="中" value="中" />
                  <el-option label="差" value="差" />
                  <el-option label="很差" value="很差" />
                  <el-option label="空白" value="空白" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="试验间隔期(天)" style="width: 100%">
                <el-input
                  type="number"
                  v-model="userInfo.interval"
                  placeholder="请输入试验间隔期(天)"
                  style="width: 100%"
                  min="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" style="width: 100%">
                <el-input
                  v-model="userInfo.remarks"
                  type="textarea"
                  :autosize="{
                    minRows: 2,
                    maxRows: 6
                  }"
                  placeholder="请输入备注"
                  clearable
                  style="width: 100%"
                  show-word-limit
                  maxlength="200"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="筛查备注" style="width: 100%">
                <el-input
                  v-model="userInfo.sysremarks"
                  type="textarea"
                  :autosize="{
                    minRows: 2,
                    maxRows: 6
                  }"
                  placeholder="请输入筛查备注"
                  clearable
                  style="width: 100%"
                  :disabled="true"
                  show-word-limit
                  maxlength="200"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div style="margin-top: 20px" v-else>
          <div class="modal-title">志愿者试验信息</div>
          <el-row :gutter="40">
            <el-col :span="8">
              <el-form-item label="筛选号" style="width: 100%">
                <el-input
                  v-model="userInfo.filterno"
                  placeholder="请输入筛选号"
                  clearable
                  style="width: 100%"
                  maxlength="20"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="筛选时间" style="width: 100%">
                <el-input
                  v-model="userInfo.filterdate"
                  placeholder="请输入筛选时间"
                  clearable
                  style="width: 100%"
                  maxlength="99"
                  :disabled="true"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注" style="width: 100%">
                <el-input
                  v-model="userInfo.remarks"
                  type="textarea"
                  :autosize="{
                    minRows: 2,
                    maxRows: 6
                  }"
                  placeholder="请输入备注"
                  clearable
                  style="width: 100%"
                  show-word-limit
                  maxlength="200"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <template #footer v-if="!loadingShow">
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit"
          >确 定</el-button
        >
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref, computed, getCurrentInstance } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import {
    subjecttrialUpdate,
    getCompleteInfo,
    subjecttrialInfo
  } from '@/api/subjects/studyManage/index.js';
  import { Icon } from '@iconify/vue';
  import { useUserStore } from '@/store/modules/user';
  import dayjs from 'dayjs';
  import phoneInput from '@/views/subjects/components/phoneInput/index.vue';
  const userStore = useUserStore();
  const userStoreInfo = computed(() => userStore.info ?? {});
  const disableddate = (time) => {
    const nowDate = dayjs(time);
    const currentDate = dayjs(userStoreInfo.value.currentTime);
    return !(currentDate >= nowDate);
  };
  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object
  });
  const { proxy } = getCurrentInstance();
  const isMobileRequired = proxy.$subjectsMobileRequired;
  const rules = ref({
    telephone: [
      {
        required: isMobileRequired,
        message: '请输入手机号码',
        trigger: ['blur', 'change']
      },
      {
        validator: (rule, value, callback) => {
          if (value) {
            const regex = /^1[3-9]\d{9}$/;
            if (regex.test(value)) {
              callback();
              return;
            }
            callback(new Error('手机号格式不正确'));
            return;
          }
          callback();
        },
        trigger: ['blur', 'change']
      }
    ],
    telephone2: [
      {
        validator: (rule, value, callback) => {
          if (value) {
            const regex = /^1[3-9]\d{9}$/;
            if (regex.test(value)) {
              callback();
              return;
            }
            callback(new Error('手机号格式不正确'));
            return;
          }
          callback();
        },
        trigger: ['blur', 'change']
      }
    ],
    telephone3: [
      {
        validator: (rule, value, callback) => {
          if (value) {
            const regex = /^1[3-9]\d{9}$/;
            if (regex.test(value)) {
              callback();
              return;
            }
            callback(new Error('手机号格式不正确'));
            return;
          }
          callback();
        },
        trigger: ['blur', 'change']
      }
    ]
  });
  const getShowEye = (row) => {
    showEye.value = !showEye.value;
    getCompleteInfo({ guid: row.subjectsGuid, type: '1' }).then((res) => {
      row.idcard = res.msg;
      row.idcard = getCard(row);
    });
  };
  const getCard = (row) => {
    try {
      if (showEye.value) {
        return row.idcard;
      }
      return row.idcard.replace(/(\d{6})\d+(\d{4})/, '$1********$2');
    } catch (error) {
      console.log(error);
      return row.idcard;
    }
  };
  const loading = ref(false);
  const loadingShow = ref(true);
  const showEye = ref(false);
  const userInfo = ref({});
  const close = () => {
    updateModelValue(false);
  };
  const formRule = ref(null);

  const submit = () => {
    formRule.value.validate((valid) => {
      if (valid) {
        loading.value = true;
        const data = {
          guid: props.data.guid,
          subjectsGuid: props.data.subjectsGuid,
          acronym: userInfo.value.acronym,
          stature: userInfo.value.stature,
          weigh: userInfo.value.weigh,
          telephone: userInfo.value.telephone,
          telephone2: userInfo.value.telephone2,
          telephone3: userInfo.value.telephone3,
          email: userInfo.value.email,
          filterno: userInfo.value.filterno,
          joinno: userInfo.value.joinno,
          joindate: userInfo.value.joindate,
          trialedate: userInfo.value.trialedate,
          lastdosedate: userInfo.value.lastdosedate,
          evaluate: userInfo.value.evaluate,
          interval: userInfo.value.interval,
          remarks: userInfo.value.remarks
        };
        subjecttrialUpdate(data).then((res) => {
          loading.value = false;
          if (res.code == 200) {
            EleMessage.success('编辑成功');
            emit('done');
            updateModelValue(false);
            return;
          }
          EleMessage.error(res.msg);
        });
        return;
      }
    });
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  nextTick(async () => {
    subjecttrialInfo({ guid: props.data.guid }).then((res) => {
      userInfo.value = res.data;
      userInfo.value.idcard = getCard(userInfo.value);
      loadingShow.value = false;
    });
  });
</script>
<style scoped>
  .modal-dialog {
    max-height: 560px;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .modal-content {
    border: 1px solid #dddddd;
    padding: 30px 20px 20px 20px;
    position: relative;
  }
  .modal-title {
    font-family: Microsoft YaHei;
    font-size: 16px;
    font-weight: bold;
    line-height: 20px;
    letter-spacing: 0px;
    font-variation-settings: 'opsz' auto;
    color: #3b426f;
    margin-bottom: 20px;
  }
  .image-slot {
    width: 98px;
    height: 128px;
    border: 1px solid #dddddd;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  :deep(.formRequired .el-form-item__label) {
    transform: translateX(-10px);
  }
</style>
