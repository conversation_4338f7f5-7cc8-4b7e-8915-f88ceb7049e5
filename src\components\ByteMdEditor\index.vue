<!-- 编辑弹窗 -->
<template>
  <!-- fullscreen -->
  <ele-modal
    form
    :width="activeName == '1' ? '1060px' : '803px'"
    :model-value="modelValue"
    :title="`【${type == 1 ? '筛选' : type == 2 ? '入组前筛选' : '人员核对'}】 ${trialsDataInfo.name}`"
    @update:modelValue="updateModelValue"
    class="testModal"
  >
    <div style="padding-bottom: 25px">
      <!-- <div class="testModalProjectName">
        <span style="margin-right: 50px"
          >项目时间：{{
            `${trialsDataInfo.sdate} ~ ${trialsDataInfo.edate}`
          }}</span
        >

        <div style="display: flex; align-items: center">
          <span style="margin-right: 20px"
            >筛选条件：距上次给药天数 >= {{ trialsDataInfo.doseday }}</span
          >
          <span style="margin-right: 20px"
            >距上次访视天数 >= {{ trialsDataInfo.trialday }}</span
          >
          <span>距上次筛选天数 >= {{ trialsDataInfo.filterday }}</span>
        </div>
      </div> -->

      <div style="position: relative">
        <el-tabs v-model="activeName" class="demo-tabs" @click="tabsChange">
          <el-tab-pane
            :label="type == 3 ? '身份证核对' : '身份证筛选'"
            name="1"
          />
          <el-tab-pane :label="type == 3 ? '刷脸核对' : '刷脸筛选'" name="2" />
        </el-tabs>
        <div
          style="
            flex: 1;
            display: flex;
            justify-content: flex-end;
            position: absolute;
            right: 0;
            top: -5px;
            z-index: 999;
          "
        >
          <el-button
            v-if="type == 3"
            style="
              font-size: 16px;
              color: #517bff;
              height: 38px;
              line-height: 38px;
            "
            type="primary"
            plain
            @click="peopleHistoryChange()"
            >人员核对历史记录</el-button
          >
          <el-button
            v-else
            style="
              font-size: 16px;
              color: #517bff;
              height: 38px;
              line-height: 38px;
            "
            type="primary"
            plain
            @click="showFilterTestInfo = true"
            >已完成筛选（{{ tableData.length }}）</el-button
          >
        </div>
      </div>

      <!-- <div style="margin: 10px 0; display: flex; align-items: center">
        <div style="border-radius: 8px; overflow: hidden">
          <el-button
            :class="activeName == '1' ? 'custom-primary' : 'custom-info'"
            style="margin: 0; border-radius: 8px 0 0 8px"
            @click="tabsChange('1')"
            >身份证筛选</el-button
          >
          <el-button
            :class="activeName == '2' ? 'custom-primary' : 'custom-info'"
            style="margin: 0; border-radius: 0 8px 8px 0"
            @click="tabsChange('2')"
            >刷脸筛选</el-button
          >
        </div>

        <div style="flex: 1; display: flex; justify-content: flex-end">
          <el-button
            style="font-size: 16px"
            type="text"
            @click="showFilterTestInfo = true"
            >当前筛选志愿者（{{ tableData.length }}）</el-button
          >
        </div>
        <el-button
          type="primary"
          :plain="activeName == '2' ? true : false"
          @click="tabsChange(), (activeName = '1')"
          >身份证筛选</el-button
        >
        <el-button
          type="primary"
          :plain="activeName == '1' ? true : false"
          @click="tabsChange(), (activeName = '2')"
          >刷脸筛选</el-button
        >
      </div> -->
      <div v-if="activeName == '1'">
        <div style="display: flex">
          <div
            class="testmodal-content"
            style="width: 300px; margin-right: 20px; min-width: 300px"
          >
            <div class="testmodal-title">基本信息</div>
            <el-form
              :model="userInfo"
              ref="formRule"
              :rules="rules"
              label-width="80px"
              label-position="left"
              class="filterForm"
            >
              <el-row :gutter="10">
                <el-col :span="24">
                  <el-form-item label="身份证号" style="width: 100%">
                    <el-input
                      v-model="userInfo.IdCard"
                      placeholder="请读取"
                      clearable
                      style="width: 100%"
                      :disabled="true"
                      v-if="showEye"
                    >
                      <template #suffix>
                        <img
                          src="@/assets/subjects/eyeOpen.png"
                          style="
                            transform: scale(1.28);
                            cursor: pointer;
                            margin: 0 5px;
                            width: 14px;
                            height: 14px;
                          "
                          v-if="showEye"
                          @click.stop="showEye = false"
                        />
                      </template>
                    </el-input>
                    <el-input
                      v-model="userInfo.idcard"
                      placeholder="请读取"
                      clearable
                      style="width: 100%"
                      :disabled="true"
                      v-if="!showEye"
                    >
                      <template #suffix>
                        <img
                          src="@/assets/subjects/eyeClose.png"
                          style="
                            transform: scale(1.28);
                            cursor: pointer;
                            margin: 0 5px;
                            width: 14px;
                            height: 14px;
                          "
                          v-if="!showEye"
                          @click="showEye = true"
                        />
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="姓名" style="width: 100%">
                    <el-input
                      v-model="userInfo.Name"
                      placeholder="请读取"
                      clearable
                      style="width: 100%"
                      :disabled="true"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="24">
                  <el-form-item label="民族" style="width: 100%">
                    <el-input
                      v-model="userInfo.Nation"
                      placeholder="请读取"
                      clearable
                      style="width: 100%"
                      :disabled="true"
                    />
                  </el-form-item>
                </el-col>
                <!-- <el-col
                  :span="6"
                  style="position: absolute; top: 66px; right: 48px"
                >
                  <el-image
                    :src="'data:image/png;base64,' + userInfo.PicBase64String"
                    alt="用户照片"
                    style="width: 130px; height: 170px"
                  >
                    <template #error>
                      <div class="image-slot">
                        <img
                          src="@/assets/filter/defaultIdcard.svg"
                          alt=""
                          srcset=""
                          style="width: 77px; height: 77px"
                        />
                      </div>
                    </template>
                  </el-image>
                </el-col> -->
                <el-col :span="24">
                  <el-form-item label="性别" style="width: 100%">
                    <el-input
                      v-model="userInfo.Sex"
                      placeholder="请读取"
                      clearable
                      style="width: 100%"
                      :disabled="true"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="年龄" style="width: 100%">
                    <el-input
                      v-model="userInfo.Age"
                      placeholder="请读取"
                      clearable
                      style="width: 100%"
                      :disabled="true"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="出生年月" style="width: 100%">
                    <el-input
                      v-model="userInfo.Birthday"
                      placeholder="请读取"
                      clearable
                      style="width: 100%"
                      :disabled="true"
                    />
                  </el-form-item>
                </el-col>
                <!-- <el-col :span="24">
                  <el-form-item label="地址" style="width: 100%">
                    <el-input
                      v-model="userInfo.Address"
                      placeholder="请读取"
                      clearable
                      style="width: 100%"
                      :disabled="true"
                    />
                  </el-form-item>
                </el-col> -->
                <!-- <el-col :span="24" v-if="props.type == 1">
                  <el-form-item label="筛选号" style="width: 100%">
                    <el-input
                      v-model="userInfo.filterno"
                      :disabled="userInfo.IdCard ? false : true"
                      placeholder="请读取后输入"
                      clearable
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24" v-else>
                  <el-form-item label="筛选号" style="width: 100%">
                    <el-input
                      v-model="userInfo.filterno"
                      :disabled="true"
                      placeholder="请读取后输入"
                      clearable
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label="联系电话"
                    prop="telephone"
                    style="width: 100%"
                  >
                    <el-input
                      v-model="userInfo.telephone"
                      :disabled="userInfo.IdCard ? false : true"
                      placeholder="请读取后输入"
                      clearable
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col> -->
                <!-- <el-col :span="24" v-if="props.type == 2">
                  <el-form-item label="筛选号" style="width: 100%; opacity: 0">
                    <div style="height: 32px; width: 100%"></div>
                  </el-form-item>
                </el-col> -->
                <!-- <el-col :span="12">
                  <el-form-item
                    label="紧急电话"
                    prop="telephone2"
                    style="width: 100%"
                  >
                    <el-input
                      v-model="userInfo.telephone2"
                      placeholder="请输入"
                      clearable
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="备用电话"
                    prop="telephone3"
                    style="width: 100%"
                  >
                    <el-input
                      v-model="userInfo.telephone3"
                      placeholder="请输入"
                      clearable
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col> -->
              </el-row>
            </el-form>
            <div
              style="
                display: flex;
                justify-content: center;
                transform: translateY(-7px);
              "
            >
              <el-button
                type="primary"
                @click="genSubjectInfo"
                :loading="readLoading"
                v-if="showGenSubjectInfo"
                style="margin-top: 8px"
                >{{ readLoading ? '模拟生成中' : '模拟生成' }}</el-button
              >
              <!-- <el-button
                type="primary"
                @click="submit"
                :loading="readLoading"
                >{{ readLoading ? '读取中' : '读 取' }}</el-button
              > -->
              <!-- <el-button
                type="primary"
                @click="EleMessage.info('敬请期待')"
                plain
                v-if="filterRes.guid"
                >确 定</el-button
              > -->
            </div>
          </div>

          <div style="width: 523px">
            <photoCheck
              ref="idCradPhotoCheckRef"
              type="1"
              :flag="userInfo.IdCard ? false : true"
              :isSign="isSign"
              :filterRes="filterRes"
              :checkLoading="checkLoading"
              :PicBase64String="userInfo.PicBase64String"
              @done="IdentityCheck"
              @base64PhotoChange="filterRes.base64Photo = $event"
            />
          </div>
        </div>
      </div>
      <div style="width: 100%; margin: 10px auto 0" v-if="activeName == '2'">
        <div
          style="
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-family:
              Microsoft YaHei,
              Microsoft YaHei;
            font-weight: 400;
            font-size: 16px;
            color: #4c5c82;
          "
        >
          <div>志愿者身份证号</div>
          <el-input
            v-model="userInfo.IdCard"
            placeholder="请输入志愿者身份证号"
            clearable
            style="margin: 0 20px; width: 325px"
            :disabled="showIdcard"
          />
          <el-button type="primary" @click="faceRecognition" v-if="!showIdcard"
            >开启人脸识别</el-button
          >
          <el-button type="primary" @click="filterNext" v-if="showIdcard"
            >筛选下一位</el-button
          >
        </div>

        <div style="margin: 0 auto; width: 703px">
          <photoCheck
            ref="photoCheckRef"
            type="2"
            :flag="!showIdcard"
            :isSign="isSign"
            :filterRes="filterRes"
            :checkLoading="checkLoading"
            :PicBase64String="cardImg"
            @done="IdentityCheck"
            @base64PhotoChange="filterRes.base64Photo = $event"
          />
        </div>
      </div>

      <div class="test">
        <el-dialog v-model="dialogVisible" title="异 常 提 示" width="600">
          <!-- <div v-if="showSiginMessage" style="font-size: 18px; color: #3b426f"
            ><span
              style="
                margin-right: 5px;
                font-size: 28px;
                line-height: 16px;
                vertical-align: bottom;
              "
              >*</span
            >请志愿者先打开《智募通》小程序进行签到，才能进行筛选
          </div> -->
          <div
            v-if="blackList && blackList.length > 0"
            style="font-size: 18px; color: #3b426f; margin-top: 10px"
            ><span
              style="
                margin-right: 5px;
                font-size: 28px;
                line-height: 16px;
                vertical-align: bottom;
              "
              >*</span
            >不适宜人群</div
          >
          <div style="padding: 10px 0">
            <span
              class="not-people"
              v-for="(item, index) in blackList"
              :key="index"
            >
              <span class="txt1">{{ item.explain }};</span>
              <!--<span class="txt2">有效期至{{ item.indate }}</span>-->
            </span>
          </div>
          <div
            v-if="isContinuousProject"
            style="font-size: 18px; color: #3b426f"
            ><span
              style="
                margin-right: 5px;
                font-size: 28px;
                line-height: 16px;
                vertical-align: bottom;
              "
              >*</span
            >连续参与试验</div
          >
          <div v-if="isContinuousProject" style="margin-top: 10px">
            <span
              style="
                line-height: 38px;
                font-size: 18px;
                color: #ff4d4f;
                padding-left: 15px;
              "
              >该志愿者12个月内参与试验超过3次</span
            >
          </div>
          <div
            v-if="memoList && memoList.length > 0"
            style="font-size: 18px; color: #3b426f; margin-top: 10px"
            ><span
              style="
                margin-right: 5px;
                font-size: 28px;
                line-height: 16px;
                vertical-align: bottom;
              "
              >*</span
            >备忘录</div
          >
          <div style="padding: 10px 0">
            <span
              class="not-people"
              v-for="(item, index) in memoList"
              :key="index"
            >
              <span class="txt1">{{ item.message }}</span>
              <!--<span class="txt2">有效期至{{ item.indate }}</span>-->
            </span>
          </div>
          <!-- <el-tabs v-model="activeDialog" class="demo-tabs">
            <el-tab-pane label="不适宜人群" name="1" />
            <el-tab-pane label="备忘录" name="2" />
          </el-tabs> -->
          <!-- <el-table
            :data="blackList"
            maxHeight="500px"
            v-if="activeDialog == '1'"
            :header-cell-style="{
              backgroundColor: '#f3f6ff',
              color: '#131314'
            }"
            :row-style="tableRowStyle"
          >
            <template #empty>
              <el-empty description="暂无数据" />
            </template>
            <el-table-column label="标记日期" prop="date" width="110" />
            <el-table-column label="有效期至" prop="indate" width="110" />
            <el-table-column
              label="标记原因"
              prop="explain"
              show-overflow-tooltip
            />
          </el-table> -->
          <!-- <el-table
            :data="memoList"
            maxHeight="500px"
            v-if="activeDialog == '2'"
            :header-cell-style="{
              backgroundColor: '#f3f6ff',
              color: '#131314'
            }"
            :row-style="tableRowStyle"
          >
            <template #empty>
              <el-empty description="暂无数据" />
            </template>
            <el-table-column label="创建时间" prop="date" width="110" />
            <el-table-column label="有效期至" prop="indate" width="110" />
            <el-table-column
              label="备忘录"
              prop="message"
              show-overflow-tooltip
            />
          </el-table> -->

          <!-- <template #footer>
            <div
              class="dialog-footer"
              style="display: flex; justify-content: space-between"
            >
              <span style="line-height: 38px; font-size: 16px; color: #c72727"
                >注意：该志愿者有标记不适宜/备忘录信息{{
                  isContinuousProject ? '，12个月内参与试验超过3次' : ''
                }}</span
              >
            </div>
          </template> -->
        </el-dialog>
      </div>
    </div>
    <filterTestInfoModal
      :data="tableData"
      v-model="showFilterTestInfo"
      v-if="showFilterTestInfo"
      @done="filterTest"
    />
    <peopleHistoryModal
      :data="HistoryInfo"
      v-model="showPeopleHistoryInfo"
      v-if="showPeopleHistoryInfo"
    />
    <!-- <template #footer>
      <div
        class="dialog-footer"
        style="display: flex; justify-content: space-between"
      >
        <div style="margin-left: 20px" v-if="activeName == 1">
          <span
            style="line-height: 38px; font-size: 16px"
            v-if="filterRes.filterResult"
          >
            <span>筛选结果：</span>
            <span
              :style="
                filterRes.filterResult == '合格' ? 'color:#00B050' : 'color:red'
              "
            >
              {{ filterRes.filterResult.replace('，', '') }}
            </span>
          </span>
        </div>
        <div style="margin-left: 20px" v-if="activeName == 2">
          <span
            style="line-height: 38px; font-size: 16px"
            v-if="filterRes.filterResult && showIdcard"
          >
            <span>筛选结果：</span>
            <span
              :style="
                filterRes.filterResult == '合格' ? 'color:#00B050' : 'color:red'
              "
            >
              {{ filterRes.filterResult.replace('，', '') }}
            </span>
          </span>
        </div>
        <div class="dialog-footer flexCenter">
          <el-button type="info" @click="updateModelValue(false)"
            >取 消</el-button
          >
        </div>
      </div>
    </template> -->
    <el-dialog
      v-model="dialogVisible2"
      :show-close="false"
      title="确认信息"
      width="600"
      append-to-body
    >
      <div>
        <!-- <div
          style="
            margin: 20px 0;
            font-size: 20px;
            font-weight: bold;
            padding: 10px 0px;
          "
        >
          <span>{{ userInfo.Name }}</span>
          <span style="margin-left: 20px">
            {{ $PhoneOrIdCrad(userInfo.IdCard, 'idCard') }}
          </span>
        </div> -->
        <el-form
          :model="userInfo"
          ref="formRule"
          :rules="rules"
          label-width="200px"
          label-position="left"
          class="filterForm filterForm2"
        >
          <el-col :span="24">
            <el-form-item prop="Name" style="width: 100%" class="formConfirm">
              <template #label>
                <span style="font-size: 22px">姓名</span>
              </template>
              <span style="font-size: 22px; font-weight: bold">{{
                userInfo.Name
              }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="IdCard" style="width: 100%" class="formConfirm">
              <template #label>
                <span style="font-size: 22px">身份证号</span>
              </template>
              <span style="font-size: 22px; font-weight: bold">{{
                $PhoneOrIdCrad(userInfo.IdCard, 'idCard')
              }}</span>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24" v-else>
            <el-form-item label="筛选号" style="width: 100%" class="formConfirm">
              <el-input
                v-model="userInfo.filterno"
                :disabled="true"
                placeholder="请输入筛选号"
                clearable
                style="width: 100%; height: 50px"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="24">
            <el-form-item
              prop="telephone"
              style="width: 100%"
              class="formConfirm"
            >
              <template #label>
                <span style="font-size: 22px"
                  >联系电话<span style="color: #ff4d4f">（必填）</span></span
                >
              </template>
              <phoneInput
                ref="myInput"
                v-model="userInfo.telephone"
                placeholder="请输入联系电话"
                clearable
                :style="{ width: '100%', height: '50px' }"
                maxlength="13"
                :disabled="userInfo.IdCard ? false : true"
              />
              <!-- <el-input
                ref="myInput"
                v-model="userInfo.telephone"
                :disabled="userInfo.IdCard ? false : true"
                placeholder="请输入联系电话"
                clearable
                style="width: 100%; height: 50px"
              /> -->
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              prop="telephone2"
              style="width: 100%"
              class="formConfirm"
            >
              <template #label>
                <span style="font-size: 22px">紧急电话</span>
              </template>
              <phoneInput
                ref="myInput2"
                v-model="userInfo.telephone2"
                placeholder="请输入紧急电话"
                clearable
                :style="{ width: '100%', height: '50px' }"
                maxlength="13"
                :disabled="userInfo.IdCard ? false : true"
              />
              <!-- <el-input
                ref="myInput"
                v-model="userInfo.telephone2"
                :disabled="userInfo.IdCard ? false : true"
                placeholder="请输入紧急电话"
                clearable
                style="width: 100%; height: 50px"
              /> -->
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="props.type == 1">
            <el-form-item style="width: 100%" class="formConfirm">
              <template #label>
                <span style="font-size: 22px">筛选号</span>
              </template>
              <el-input
                v-model="userInfo.filterno"
                :disabled="userInfo.IdCard ? false : true"
                placeholder="请输入筛选号"
                clearable
                style="width: 100%; height: 50px"
              />
            </el-form-item>
          </el-col>
        </el-form>
      </div>
      <template #footer>
        <div
          class="dialog-footer"
          style="display: flex; align-items: center; justify-content: center"
        >
          <el-button
            style="height: 38px; font-size: 16px; width: 80px"
            @click="dialogVisible2 = false"
            >取 消</el-button
          >
          <el-button
            type="primary"
            style="height: 38px; font-size: 16px; width: 80px"
            @click="onComfirm()"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </ele-modal>
</template>

<script setup>
  import {
    ref,
    nextTick,
    getCurrentInstance,
    onMounted,
    onBeforeUnmount,
    watch,
    computed
  } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { Icon } from '@iconify/vue';
  import dayjs from 'dayjs';
  import { throttle } from 'lodash';
  import {
    trialsInfo,
    joinFilterCheck,
    idCardCheck,
    certificateCheck,
    filter,
    joinFilter,
    subjecttrialInfo,
    queryIdcard,
    release,
    personnelCheck,
    personnelCheckUpdate
  } from '@/api/subjects/studyManage/index.js';
  import photoCheck from './photoCheck.vue';
  import { systemConfig } from '@/api/login';
  import { CLIENT_ID } from '@/config/setting';
  import filterTestInfoModal from './filterTestInfoModal.vue';
  import peopleHistoryModal from './peopleHistoryModal.vue';
  import { ElMessageBox, ElMessage } from 'element-plus';
  import phoneInput from '@/views/subjects/components/phoneInput/index.vue';
  import { useUserStore } from '@/store/modules/user';
  const userStore = useUserStore();
  const loginUser = computed(() => userStore.info ?? {});

  // 是否开启模拟生成
  const showGenSubjectInfo = ref(false);
  // 是否签到
  const isSign = ref(false);
  //客户端控件基本信息
  const clientTool = ref({
    downloadUrl:
      'https://ibank.frp.520gcp.com/sms/Farinfo.ToolServices_Setup_2023.1.1.1.exe',
    version: '2023.1.1.1'
  });
  //基本信息表单校验
  const formRule = ref(null);
  const tableRowStyle = (row) => {
    return row.rowIndex % 2 === 0
      ? { background: '#ffffff' }
      : { background: '#ffffff' };
  };
  const { proxy } = getCurrentInstance();
  const isMobileRequired = proxy.$subjectsMobileRequired;
  const rules = ref({
    telephone: [
      {
        required: false,
        message: '请输入手机号码',
        trigger: ['blur']
      },
      {
        validator: (rule, value, callback) => {
          if (value) {
            const regex = /^1[3-9]\d{9}$/;
            if (regex.test(value)) {
              callback();
              return;
            }
            callback(new Error('手机号格式不正确'));
            return;
          }
          callback();
        },
        trigger: ['blur', 'change']
      }
    ],
    telephone2: [
      {
        validator: (rule, value, callback) => {
          if (value) {
            const regex = /^1[3-9]\d{9}$/;
            if (regex.test(value)) {
              callback();
              return;
            }
            callback(new Error('手机号格式不正确'));
            return;
          }
          callback();
        },
        trigger: ['blur', 'change']
      }
    ],
    telephone3: [
      {
        validator: (rule, value, callback) => {
          if (value) {
            const regex = /^1[3-9]\d{9}$/;
            if (regex.test(value)) {
              callback();
              return;
            }
            callback(new Error('手机号格式不正确'));
            return;
          }
          callback();
        },
        trigger: ['blur', 'change']
      }
    ]
  });
  //筛选志愿者弹框
  const showFilterTestInfo = ref(false);
  //人员核对历史记录弹窗
  const showPeopleHistoryInfo = ref(false);
  //筛选志愿者弹框回调
  const filterTest = (value) => {
    tableData.value = value;
  };
  //顶部tabs 1=身份证筛选 2=刷脸筛选
  const activeName = ref('1');
  //备忘录、标记不适宜弹框
  const dialogVisible = ref(false);
  const dialogVisible2 = ref(false);
  //备忘录、标记不适宜弹框tabs 2=备忘录 1=标记不适宜弹框
  const activeDialog = ref('1');
  //用来存储用户信息（身份证读卡后的信息，刷脸筛选后接口返回的信息）
  //信息主要来自接口filter、certificateCheck，用户切换tabs、重新刷身份证、点击开启人脸识别按钮会调用reset()方法清空
  const userInfo = ref({});
  //用来存储一些结果类信息（摄像头拍照后的base64图片、核对结果、筛选结果），
  //信息主要来自接口filter、certificateCheck，用户切换tabs、重新刷身份证、点击开启人脸识别按钮会调用reset()方法清空
  const filterRes = ref({});
  //身份证筛选-基本信息-身份证号右边小眼睛（查看脱敏身份那种）
  const showEye = ref(false);
  //刷脸筛选显示拍照区域
  const showIdcard = ref(false);
  const cardImg = ref('');
  //刷脸筛选下一位
  const filterNext = () => {
    userInfo.value.IdCard = '';
    cardImg.value = '';
    showIdcard.value = false;
    reset();
  };
  const HistoryInfo = ref({});
  const peopleHistoryChange = () => {
    showPeopleHistoryInfo.value = true;
    HistoryInfo.value = {
      trialsGuid: props.data.guid,
      organizationGuid: loginUser.value.organizationGuid
    };
  };
  //刷脸筛选按钮(执行完本方法后会依次执行其他方法),获取身份证信息以及一些判断条件
  const faceRecognition = () => {
    reset();
    if (!userInfo.value.IdCard) {
      EleMessage.warning('请输入身份证号');
      return;
    }
    queryIdcard({ idcard: userInfo.value.IdCard }).then((res) => {
      if (res.code == 200) {
        let portrait = res.data.portrait;
        if (portrait.includes('/portrait/')) {
          portrait = import.meta.env.VITE_APP_BASE_OSS + portrait;
        }
        cardImg.value = res.data ? portrait : '';
        if (props.type != 3) {
          if (!res.data) {
            EleMessage.error(
              `身份证【${userInfo.value.IdCard}】的受试者未通过系统进行筛选，无法使用刷脸操作！`
            );
            return;
          }
        }
        /*
          *拟造身份证读卡器返回的数据（以便可以共用之前身份证筛选的逻辑）
          };*/
        const user = {
          IdCard: res.data.idcard,
          Name: res.data.name,
          Sex: res.data.sex,
          Nation: res.data.nation,
          Birthday: res.data.birthday,
          Address: res.data.address,
          Region: res.data.region,
          PicBase64String: portrait,
          filterno: res.data.filterno,
          telephone: res.data.telephone,
          telephone2: res.data.telephone2,
          telephone3: res.data.telephone3
        };
        if (props.type != 3) {
          sendJoinFilterCheck(user);
        } else {
          showIdcard.value = true;
        }
        return;
      }
      EleMessage.error(res.msg);
    });
  };
  //切换tab重置信息
  const tabsChange = () => {
    const e = activeName.value;
    if (e == '1') {
      try {
        photoCheckRef.value.closeCamera();
      } catch (error) {
        /* empty */
      }
    }
    if (e == '2') {
      try {
        idCradPhotoCheckRef.value.closeCamera();
      } catch (error) {
        /* empty */
      }
    }
    userInfo.value = {};
    showIdcard.value = false;
    reset();
    activeName.value = e;
  };
  //连接WebSocket 与客户端通讯，主要用来读取身份证信息
  const ws = new WebSocket('ws://127.0.0.1:54321');
  ws.onopen = () => {
    console.log('WebSocket 连接已建立！');
    if (ws.readyState === WebSocket.OPEN) {
      console.log('已连接');
      const guid = Date.parse(new Date()); //建议使用Guid
      const data = { api: 'SetTagId', id: guid, body: [guid] };
      ws.send(JSON.stringify(data));
      GetVersion();
      // submit();
    } else {
      console.error('Websocket未连接');
      // Websocket连接失败
    }
  };
  const driveErr = ref(false);
  // 监听连接失败事件
  ws.onerror = function (error) {
    console.log('WebSocket 连接失败:', error);
    driveErr.value = true;
    readLoading.value = false;

    driveError();
  };
  // 驱动连接失败
  const driveError = () => {
    ElMessageBox.alert(
      `<div style="font-size: 18px">请确保驱动服务已下载，并
    <a href="farinfotoolservicesstartup://" target="_blank">运行</a> 驱动服务！<br />如已运行请刷新当前页面；<br /><br />
    如未下载，请点击
    <a href="${clientTool.value.downloadUrl}" target="_blank">下载驱动服务</a> 安装后，<br />如何安装，
    <a href="/install" target="_blank">请点击参考</a>，安装并运行后，请刷新网页使用！</div>`,
      '连接【驱动服务】失败！',
      {
        dangerouslyUseHTMLString: true,
        customStyle: {
          maxWidth: '600px'
        },
        callback: (action) => {
          if (action === 'confirm') {
            //location.reload();
            this.$msgbox.close();
          }
        }
      }
    );
  };

  // 监听连接关闭事件
  ws.onclose = function (event) {
    if (event.wasClean) {
      console.log('WebSocket 连接已正常关闭');
    } else {
      console.log('WebSocket 连接异常关闭');
    }
  };
  //测试读取身份证
  const idCardRead = () => {
    const guid = Date.parse(new Date()); //建议使用Guid
    const data = {
      api: useidCardRead2.value ? 'Api_IdCard_Read2' : 'Api_IdCard_Read',
      id: guid,
      body: []
    };
    ws.send(JSON.stringify(data));
  };
  //获取版本号
  const GetVersion = () => {
    const guid = Date.parse(new Date()); //建议使用Guid
    const data = { api: 'GetVersion', id: guid, body: [] };
    ws.send(JSON.stringify(data));
  };
  //备忘录、不适宜数组，用来在弹框中展示
  const blackList = ref([]);
  const memoList = ref([]);
  //photoCheck子组件的回调方法(返回一个拍照后的图片信息)，用来证照核对
  const checkLoading = ref(false);

  const IdentityCheck2 = async (image2) => {
    checkLoading.value = true;
    const data = {
      trialGuid: props.data.guid,
      subjectidcard: userInfo.value.IdCard,
      image1: userInfo.value.PicBase64String,
      image2: image2,
      type: props.type,
      filterType: activeName.value
    };
    const res = await certificateCheck(data);
    if (res.code != 200) {
      EleMessage.error(res.msg);
      setTimeout(() => {
        checkLoading.value = false;
      }, 1000);
      return;
    }
    let filterData = {
      trialsGuid: props.data.guid,
      idcard: userInfo.value.IdCard,
      name: userInfo.value.Name,
      sex: userInfo.value.Sex,
      nation: userInfo.value.Nation,
      birthday: userInfo.value.Birthday,
      address: userInfo.value.Address,
      region: userInfo.value.Region,
      portrait: userInfo.value.PicBase64String,
      telephone: userInfo.value.telephone,
      telephone2: userInfo.value.telephone2,
      telephone3: userInfo.value.telephone3,
      filterno: userInfo.value.filterno,
      type: props.type,
      similaritytype: res.data.result,
      faceFailureFlag: false,
      similarity: res.data.similarity
    };
    filterRes.value.similaritytype = res.data.result;
    if (filterData.similaritytype == 1) {
      if (activeName.value == '2' && res.data.remaining < 0) {
        ElMessageBox.confirm(
          `多次比对，相似度不高，请携带证件后进行刷身份证操作`,
          `证件核对结果`,
          {
            center: true,
            showClose: false,
            closeOnClickModal: false,
            showConfirmButton: true,
            showCancelButton: false,
            confirmButtonText: '确 定'
          }
        ).then(() => {
          checkLoading.value = false;
          // reset();
        });

        return;
      } else {
        sendFilter(filterData);
      }
    } else {
      if (activeName.value == '2') {
        if (res.data.remaining < 0) {
          ElMessageBox.confirm(
            `多次比对，相似度不高，请携带证件后进行刷身份证操作`,
            `证件核对结果`,
            {
              center: true,
              showClose: false,
              closeOnClickModal: false,
              showConfirmButton: true,
              showCancelButton: false,
              confirmButtonText: '确 定'
            }
          ).then(() => {
            checkLoading.value = false;
            // reset();
          });

          return;
        } else {
          ElMessageBox.confirm(
            `<span style="color:#FF4D4F">相似度为${
              filterData.similarity ? filterData.similarity + '%' : '0'
            } < 80%，是否重新拍照？（还剩余${res.data.remaining}次机会）</span>`,
            `证件核对结果`,
            {
              dangerouslyUseHTMLString: true,
              center: true,
              showClose: false,
              closeOnClickModal: false,
              showConfirmButton: true,
              showCancelButton: true,
              confirmButtonText: '重新拍照',
              cancelButtonText: '否',
              customStyle: {
                maxWidth: '600px'
              }
            }
          )
            .then(() => {
              filterRes.value.similaritytype = '';
              checkLoading.value = false;
            })
            .catch(() => {
              checkLoading.value = false;
              filterData.faceFailureFlag = true;
              sendFilter(filterData);
            });
          return;
        }
      }
      setTimeout(() => {
        appendChildToBtns();
      }, 100);
      ElMessageBox.confirm(
        `<span style="color:#FF4D4F">相似度为${
          filterData.similarity ? filterData.similarity + '%' : '0'
        } < 80%，请人工核对？</span>`,
        `证件核对结果`,
        {
          dangerouslyUseHTMLString: true,
          center: true,
          showClose: false,
          closeOnClickModal: false,
          showConfirmButton: true,
          showCancelButton: true,
          confirmButtonText: '是本人',
          cancelButtonText: '不是本人',
          customStyle: {
            maxWidth: '600px'
          }
        }
      )
        .then(() => {
          sendFilter(filterData);
        })
        .catch(() => {
          filterData.faceFailureFlag = true;
          sendFilter(filterData);
        });
    }
  };
  const imageInfo = ref('');

  const onComfirm = async () => {
    //if (activeName.value == '1') {
    try {
      const validateRes = await formRule.value.validate();
      console.log(validateRes);
    } catch (error) {
      EleMessage.warning('请填写联系电话');
      return;
    }
    if (!userInfo.value.telephone) {
      EleMessage.warning('请填写联系电话');
      return;
    }
    //}
    dialogVisible2.value = false;
    await IdentityCheck2(imageInfo.value);
  };
  const IdentityCheck3 = async (image2) => {
    checkLoading.value = true;

    const data = {
      trialsGuid: props.data.guid,
      subjectidcard: userInfo.value.IdCard,
      organizationGuid: loginUser.value.organizationGuid,
      image: image2,
      filterType: activeName.value
    };
    const res = await personnelCheck(data);
    if (res.code != 200) {
      EleMessage.error(res.msg);
      setTimeout(() => {
        checkLoading.value = false;
      }, 1000);
      return;
    }
    let filterData = {
      trialsGuid: props.data.guid,
      idcard: userInfo.value.IdCard,
      name: userInfo.value.Name,
      sex: userInfo.value.Sex,
      nation: userInfo.value.Nation,
      birthday: userInfo.value.Birthday,
      address: userInfo.value.Address,
      region: userInfo.value.Region,
      portrait: userInfo.value.PicBase64String,
      telephone: userInfo.value.telephone,
      telephone2: userInfo.value.telephone2,
      telephone3: userInfo.value.telephone3,
      filterno: userInfo.value.filterno,
      type: props.type,
      similaritytype: res.data.result,
      faceFailureFlag: false,
      similarity: res.data.similarity
    };
    filterRes.value.similaritytype = res.data.result;
    // if (filterData.similaritytype == 1) {
    //   sendFilter(filterData);
    // } else {
    if (activeName.value == '2') {
      if (res.data.remaining < 0) {
        ElMessageBox.confirm(
          `多次比对，相似度不高，请携带证件后进行刷身份证操作`,
          `证件核对结果`,
          {
            center: true,
            showClose: false,
            closeOnClickModal: false,
            showConfirmButton: true,
            showCancelButton: false,
            confirmButtonText: '确 定'
          }
        ).then(() => {
          checkLoading.value = false;
          // reset();
        });

        return;
      } else {
        if (filterData.similarity < 80) {
          ElMessageBox.confirm(
            `<span style="color:#FF4D4F">相似度为${
              filterData.similarity ? filterData.similarity + '%' : '0'
            } < 80%，是否重新拍照？（还剩余${res.data.remaining}次机会）</span>`,
            `证件核对结果`,
            {
              dangerouslyUseHTMLString: true,
              center: true,
              showClose: false,
              closeOnClickModal: false,
              showConfirmButton: true,
              showCancelButton: true,
              confirmButtonText: '重新拍照',
              cancelButtonText: '否',
              customStyle: {
                maxWidth: '600px'
              }
            }
          )
            .then(() => {
              filterRes.value.similaritytype = '';
              checkLoading.value = false;
            })
            .catch(() => {
              checkLoading.value = false;
              if (props.type != 3) {
                filterData.faceFailureFlag = true;
                sendFilter(filterData);
              }
            });
          return;
        } else {
          ElMessageBox.confirm(
            `<span style="color:#00B050">核对通过</span>`,
            `证件核对结果`,
            {
              dangerouslyUseHTMLString: true,
              center: true,
              showClose: false,
              closeOnClickModal: false,
              showConfirmButton: false,
              showCancelButton: true,
              // confirmButtonText: '关 闭',
              cancelButtonText: '核对下一位',
              customStyle: {
                maxWidth: '600px'
              }
            }
          )
            .then(() => {
              checkLoading.value = false;
              userInfo.value = {};
              cardImg.value = '';
              showIdcard.value = false;
              reset();
            })
            .catch(() => {
              console.log('点击了取消按钮');
              checkLoading.value = false;
              showIdcard.value = false;
              userInfo.value = {};
              cardImg.value = '';
              reset();
              // 手动调用关闭方法
              ElMessageBox.close();
            });
        }
      }
    }
    if (filterData.similarity < 80) {
      setTimeout(() => {
        appendChildToBtns();
      }, 100);
      ElMessageBox.confirm(
        `<span style="color:#FF4D4F">相似度为${
          filterData.similarity ? filterData.similarity + '%' : '0'
        } < 80%，请人工核对？</span>`,
        `证件核对结果`,
        {
          dangerouslyUseHTMLString: true,
          center: true,
          showClose: false,
          closeOnClickModal: false,
          showConfirmButton: true,
          showCancelButton: true,
          confirmButtonText: '是本人',
          cancelButtonText: '不是本人',
          customStyle: {
            maxWidth: '600px'
          }
        }
      )
        .then(() => {
          if (props.type != 3) {
            sendFilter(filterData);
          } else {
            personnelCheckUpdate({
              guid: res.data.guid
            }).then((res2) => {
              if (res2.code == 200) {
                ElMessage.success('操作成功');
                checkLoading.value = false;
              } else {
                ElMessage.error(res2.msg);
                checkLoading.value = false;
              }
            });
          }
        })
        .catch(() => {
          if (props.type != 3) {
            filterData.faceFailureFlag = true;
            sendFilter(filterData);
          }
          checkLoading.value = false;
          // else {
          //   personnelCheckUpdate({
          //     guid: res.data.guid
          //   }).then((res2) => {
          //     if (res2.code == 200) {
          //       ElMessage.success('操作成功');
          //       checkLoading.value = false;
          //     } else {
          //       ElMessage.error(res2.msg);
          //       checkLoading.value = false;
          //     }
          //   });
          // }
        });
    } else {
      ElMessageBox.confirm(
        `<span style="color:#00B050">核对通过</span>`,
        `证件核对结果`,
        {
          dangerouslyUseHTMLString: true,
          center: true,
          showClose: false,
          closeOnClickModal: false,
          showConfirmButton: false,
          showCancelButton: true,
          // confirmButtonText: '关 闭',
          cancelButtonText: '核对下一位',
          customStyle: {
            maxWidth: '600px'
          }
        }
      )
        .then(() => {
          checkLoading.value = false;
          showIdcard.value = false;
          userInfo.value = {};
          cardImg.value = '';
          reset();
        })
        .catch(() => {
          checkLoading.value = false;
          showIdcard.value = false;
          userInfo.value = {};
          cardImg.value = '';
          reset();
          // 手动调用关闭方法
          ElMessageBox.close();
        });
    }
  };
  const myInput = ref(null);
  const IdentityCheck = async (image2) => {
    if (props.type == 3) {
      console.log('点击证件核对按钮');
      imageInfo.value = image2;
      IdentityCheck3(image2);
    } else {
      imageInfo.value = image2;
      dialogVisible2.value = true;
      nextTick(() => {
        // myInput.value.focus();
      });
    }
  };
  watch(
    () => dialogVisible2.value,
    (newVal) => {
      if (newVal) {
        setTimeout(() => {
          const input = myInput.value?.$el?.querySelector('input');
          if (input && !input.disabled) {
            input.focus();
          }
        }, 100); // 适当延迟确保DOM渲染完成
      }
    },
    { immediate: true } // 添加immediate确保初始状态也处理
  );
  //用来弥补ElMessageBox.confirm方法只有两个按钮的方法
  const appendChildToBtns = () => {
    // 获取div元素
    let btns = document.querySelector('.el-message-box__btns');
    // 创建一个新的btn元素
    let btn = document.createElement('button');
    // 为btn元素设置文本内容 添加饿了么组件按钮样式class
    btn.className = 'el-button el-button--default';
    btn.textContent = '重新拍照';
    btn.style.whiteSpace = 'nowrap';
    btn.style.marginLeft = '8px'; // 可以根据需要调整按钮之间的间距
    // 将btn元素插入到btns的第一个位置
    btns.insertBefore(btn, btns.firstChild);
    // 点击按钮关闭提示框
    btn.onclick = () => {
      filterRes.value.similaritytype = '';
      checkLoading.value = false;
      ElMessageBox.close();
    };
  };
  //筛选接口，主流程最后一步
  const sendFilter = (data) => {
    const api = props.type == '1' ? filter : joinFilter;
    api(data).then((res) => {
      if (res.code != 200) {
        checkLoading.value = false;
        EleMessage.error(res.msg);
        return;
      }
      checkLoading.value = false;
      const filterResult = getFilterResultStyle(res.data.filterResult);
      filterRes.value.filterResult = filterResult ? '合格' : '不合格，';
      res.data.filterResult = filterRes.value.filterResult;
      res.data.result = filterRes.value.filterResult;
      filterRes.value.isFirst = res.data.isFirst;
      filterRes.value.guid = res.data.guid;
      gettableData(res.data);
    });
  };

  //筛选接口后返回的guid查询信息填充到表格中
  const gettableData = (data) => {
    if (!data.guid) {
      if (data.filterResult == '合格') {
        let filterText = '筛选';
        let isFirst = data.isFirst;
        if (isFirst == true) {
          filterText = '首次筛选';
        }
        console.log('filterText====>', filterText);
        ElMessageBox.confirm(
          `<span style="color:#00B050">${
            props.type == '2' ? '入组前筛选' : filterText
          }${data.filterResult}</span>${
            data.sysRemarks ? data.sysRemarks : ''
          }`,
          `筛选结果`,
          {
            dangerouslyUseHTMLString: true,
            center: true,
            showClose: false,
            showCancelButton: false,
            closeOnClickModal: false,
            confirmButtonText: '筛选下一位',
            customStyle: {
              maxWidth: '600px'
            }
          }
        ).then(() => {
          showIdcard.value = false;
          userInfo.value = {};
          reset();
        });
      } else {
        ElMessageBox.confirm(
          `<span style="color:#FF4D4F">${
            props.type == '2' ? '入组前筛选' : '筛选'
          }${data.filterResult}</span>${
            data.sysRemarks ? data.sysRemarks : ''
          }`,
          `筛选结果`,
          {
            dangerouslyUseHTMLString: true,
            center: true,
            showClose: false,
            showCancelButton: false,
            closeOnClickModal: false,
            confirmButtonText: '筛选下一位',
            customStyle: {
              maxWidth: '600px'
            }
          }
        ).then(() => {
          showIdcard.value = false;
          userInfo.value = {};
          reset();
        });
      }
      return;
    }
    subjecttrialInfo(data).then((res) => {
      const subjecttrialData = {
        ...res.data,
        isFirst: data.isFirst,
        filterResult: data.filterResult,
        similaritytype: filterRes.value.similaritytype,
        sysRemarks2: data.sysRemarks
      };
      console.log('subjecttrialData====>', subjecttrialData, data);
      tableData.value.push(subjecttrialData);
      if (data.filterResult.includes('不合格')) {
        greenLight(subjecttrialData);
      } else {
        let filterText = '筛选';
        let isFirst = data.isFirst;
        if (isFirst == true) {
          filterText = '首次筛选';
        }
        console.log('filterText====>', filterText);
        if (data.result == '合格') {
          ElMessageBox.confirm(
            `<span style="color:#00B050">${
              props.type == '2' ? '入组前筛选' : filterText
            }${data.result}</span>`,
            '筛选结果',
            {
              center: true,
              showClose: false,
              showCancelButton: false,
              closeOnClickModal: false,
              dangerouslyUseHTMLString: true,
              confirmButtonText: '筛选下一位',
              customStyle: {
                maxWidth: '600px'
              },
              //cancelButtonText: '取消',
              callback: (action) => {
                if (action === 'confirm') {
                  showIdcard.value = false;
                  userInfo.value = {};
                  reset();
                }
                // 若点击取消按钮，可在此添加相应逻辑
              }
            }
          );
        } else {
          ElMessageBox.confirm(
            `<span style="color:#FF4D4F">${
              props.type == '2' ? '入组前筛选' : filterText
            }${data.result}</span>`,
            '筛选结果',
            {
              center: true,
              showClose: false,
              showCancelButton: false,
              closeOnClickModal: false,
              dangerouslyUseHTMLString: true,
              confirmButtonText: '筛选下一位',
              //cancelButtonText: '取消',
              customStyle: {
                maxWidth: '600px'
              },
              callback: (action) => {
                if (action === 'confirm') {
                  showIdcard.value = false;
                  userInfo.value = {};
                  reset();
                }
                // 若点击取消按钮，可在此添加相应逻辑
              }
            }
          );
        }
      }
    });
  };
  //筛选不合格后检测是否可放行
  const greenLight = (data) => {
    if (
      (data.status == 0 || data.status == 8) &&
      data.sysremarks.includes('上家机构建议')
    ) {
      console.log('可放行', data.sysremarks, data);
      ElMessageBox.confirm(
        `由于上家机构建议试验间隔期原因导致<span style="color:#FF4D4F">初查不合格</span>，是否对该受试者放行？<br/><br/>${data.sysRemarks2}`,
        `筛选提示`,
        {
          dangerouslyUseHTMLString: true,
          center: true,
          showClose: false,
          closeOnClickModal: false,
          showCancelButton: true,
          confirmButtonText: '放行',
          cancelButtonText: '筛选下一位',
          customStyle: {
            maxWidth: '600px'
          }
        }
      )
        .then(() => {
          release(data).then((res) => {
            if (res.code == 200) {
              tableData.value.forEach((item) => {
                if (item.guid.toUpperCase() == data.guid.toUpperCase()) {
                  item.status = 1;
                  item.filterResult = '合格';
                  filterRes.value.filterResult = '合格';
                }
              });
              EleMessage.success('放行成功');
              return;
            }
            EleMessage.error(res.msg);
          });
        })
        .catch(() => {
          showIdcard.value = false;

          userInfo.value = {};
          reset();
        });
    } else {
      console.log(data); //
      let filterText = '筛选';
      let isFirst = data.isFirst;
      if (isFirst == true) {
        filterText = '首次筛选';
      }
      console.log('filterText====>', filterText);
      if (data.filterResult == '合格') {
        ElMessageBox.confirm(
          `<span style="color:#9bcb61">${
            props.type == '2' ? '入组前筛选' : filterText
          }${data.filterResult}</span>${
            data.sysremarks ? data.sysremarks : ''
          }`,
          `筛选结果`,
          {
            dangerouslyUseHTMLString: true,
            center: true,
            showClose: false,
            showCancelButton: false,
            closeOnClickModal: false,
            confirmButtonText: '筛选下一位',
            customStyle: {
              maxWidth: '600px'
            }
          }
        ).then(() => {
          showIdcard.value = false;
          userInfo.value = {};
          reset();
        });
      } else {
        ElMessageBox.confirm(
          `<span style="color:#FF4D4F">${
            props.type == '2' ? '入组前筛选' : filterText
          }${data.filterResult}</span>${
            data.sysRemarks2 ? data.sysRemarks2 : ''
          }`,
          `筛选结果`,
          {
            dangerouslyUseHTMLString: true,
            center: true,
            showClose: false,
            showCancelButton: false,
            closeOnClickModal: false,
            confirmButtonText: '筛选下一位',
            customStyle: {
              maxWidth: '600px'
            }
          }
        ).then(() => {
          showIdcard.value = false;
          userInfo.value = {};
          reset();
        });
      }
      // EleMessage.error(`筛选结果：${data.sysremarks}`);
    }
  };
  //筛选检验接口-筛选第一步
  let ElmessageRef = ref(null);
  const sendJoinFilterCheck = (user) => {
    dialogVisible.value = false;
    if (ElmessageRef.value) {
      ElMessageBox.close(ElmessageRef.value); // 关闭当前的确认框
    }
    joinFilterCheck({
      idcard: user.IdCard,
      trialsGuid: props.data.guid,
      type: props.type
    }).then((res) => {
      if (res.code == 200) {
        if (res.msg == 'success') {
          if (activeName.value == '2') {
            showIdcard.value = true;
          }
          sendUIdCardCheck(user);
          return;
        }
        ElmessageRef.value = ElMessageBox.confirm(res.msg, '筛选提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          showClose: false,
          center: true,
          customStyle: {
            maxWidth: '600px'
          }
        }).then(() => {
          if (activeName.value == '2') {
            showIdcard.value = true;
          }
          sendUIdCardCheck(user);
        });
        return;
      }
      EleMessage.error(res.msg);
    });
  };
  const isContinuousProject = ref(false);
  const showSiginMessage = ref(false);
  //查询不适宜、备忘录-筛选第二步
  const sendUIdCardCheck = (user) => {
    user.idcard = user.IdCard.replace(/(\d{6})\d+(\d{4})/, '$1**********$2');

    const params = {
      type: props.type,
      idcard: user.IdCard,
      trialsGuid: props.data.guid,
    };
    idCardCheck(params).then((res) => {
      if (res.code == '200') {
        if (res.data.telephone) {
          user.telephone = res.data.telephone;
        }
        if (res.data.telephone2) {
          user.telephone2 = res.data.telephone2;
        }
        if (res.data.telephone3) {
          user.telephone3 = res.data.telephone3;
        }
        userInfo.value = user;
        if (
          (res.data.blackList && res.data.blackList.length != 0) ||
          (res.data.memoList && res.data.memoList.length != 0)
        ) {
          blackList.value = res.data.blackList;
          memoList.value = res.data.memoList;
          if (memoList.value.length > 0) {
            activeDialog.value = '2';
          } else if (blackList.value.length > 0) {
            activeDialog.value = '1';
          }
          if (props.type != 3) {
            dialogVisible.value = true;
          }
        }
        if (res.data && res.data.isContinuousProject) {
          isContinuousProject.value = res.data.isContinuousProject;
        }

        // if (!res?.data?.isSignin) {
        //   isSign.value = false;
        //   dialogVisible.value = true;
        //   showSiginMessage.value = true;
        // } else {
        //   isSign.value = true;
        //   showSiginMessage.value = false;
        // }

        return;
      }
      EleMessage.error(res.msg || '出现未知问题');
    });
  };
  //监听WebSocket客户端返回的身份证信息
  const isPortOpenTips = ref(false);
  const interval = ref(null);
  const useidCardRead2 = ref(false);

  ws.onmessage = (event) => {
    readLoading.value = false;
    const data = JSON.parse(event.data);
    if (data.api == 'GetVersion') {
      // const body = JSON.parse(data.body);
      // if (
      //   body['Farinfo.ToolServices.WebSockets.IdCard'] !=
      //   clientTool.value.version
      // ) {
      //   ElMessageBox.alert(
      //     `<div style="font-size: 18px">驱动服务版本不一致！<br />请卸载当前驱动服务后，点击<a href="${clientTool.value.downloadUrl}" target="_blank">下载</a>安装使用！`,
      //     '更新驱动服务',
      //     {
      //       dangerouslyUseHTMLString: true,
      //       customStyle: {
      //         maxWidth: '600px'
      //       },
      //       callback: (action) => {
      //         if (action === 'confirm') {
      //           //location.reload();
      //           this.$msgbox.close();
      //         }
      //       }
      //     }
      //   );
      //   return;
      // }
      interval.value = setInterval(() => {
        idCardRead();
      }, 1000);
      return;
    }
    // 调用Api_IdCard_Read，如果出现“读卡失败”，就调用Api_IdCard_Read2
    if (
      data.api == 'Api_IdCard_Read' &&
      typeof data.body.Message === 'string' &&
      data.body.Message.includes('读卡失败')
    ) {
      // 调用 Api_IdCard_Read2
      useidCardRead2.value = true;
    }
    if (data.api.includes('Api_IdCard_Read') && activeName.value == '1') {
      if (data.body.Result) {
        const user = data.body.Data;
        user.birthday = dayjs(user.birthday).format('YYYY-MM-DD');
        user.Birthday = dayjs(user.Birthday).format('YYYY-MM-DD');
        EleMessage.success('读取成功');
        if (props.type == 3) {
          sendUIdCardCheck(user);
        } else {
          sendJoinFilterCheck(user);
        }
        return;
      }
      console.log(
        data.body.Message,
        '----------666666',
        data.body.Message.includes(
          '无法加载 DLL“Termb.dll”: 找不到指定的模块。'
        )
      );
      if (
        data.body.Message.includes(
          '无法加载 DLL“Termb.dll”: 找不到指定的模块。'
        )
      ) {
        return EleMessage.warning('驱动连接异常，请重启电脑');
      }
      // if (data.body.Message.includes('读取成功')) {
      // EleMessage.warning(data.body.Message);
      // }

      // 如果是“端口打开失败，请检测相应的端口或者重新连接读卡器！”，则只提示一次
      if (
        data.body.Message ===
        '端口打开失败，请检测相应的端口或者重新连接读卡器！'
      ) {
        if (!isPortOpenTips.value) {
          EleMessage.warning(data.body.Message);
          isPortOpenTips.value = true;
        } else {
          return;
        }
      } else if (
        data.api == 'Api_IdCard_Read' &&
        typeof data.body.Message === 'string' &&
        data.body.Message.includes('读卡失败')
      ) {
        return;
      } else if (
        data.body.Message &&
        !data.body.Message.includes('未放卡或者卡未放好')
      ) {
        EleMessage.warning(data.body.Message);
      }
    }
  };

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object,
    type: String,
    activeName: String
  });

  const trialsType = computed(() => {
    return props?.type || '1';
  });

  //储存弹框顶部的项目信息
  const trialsDataInfo = ref({});
  console.log('props.data===>', props.data);
  nextTick(() => {
    trialsInfo(props.data).then((res) => {
      trialsDataInfo.value = res.data;
    });
  });
  const readLoading = ref(false);
  //重置信息发送socket给客户端，读取身份证信息
  onMounted(() => {
    if (props.type == 3) {
      isSign.value = true;
    }
    // 添加页面可见性变化事件监听
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 是否开启模拟生成
    systemConfig(CLIENT_ID).then((res) => {
      showGenSubjectInfo.value = res.data.apiActive != 'production';
    });

    nextTick(() => {
      console.log('模拟检查筛选流程');
    });
  });
  const submit = throttle(() => {
    if (driveErr.value) {
      driveError();
      return;
    }
    readLoading.value = true;
    reset();
    // try {
    const guid = Date.parse(new Date()); //建议使用Guid
    const data = {
      api: useidCardRead2.value ? 'Api_IdCard_Read2' : 'Api_IdCard_Read',
      id: guid,
      body: []
    };
    ws.send(JSON.stringify(data));
    // setTimeout(submit, 5000);
    // } catch (error) {
    //   setTimeout(submit, 1000);
    // }
  }, 1000);
  //重置信息方法
  const reset = () => {
    blackList.value = [];
    memoList.value = [];
    filterRes.value = {};
  };
  const idCradPhotoCheckRef = ref(null);
  const photoCheckRef = ref(null);
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('done');
    emit('update:modelValue', value);
    try {
      photoCheckRef.value.closeCamera();
    } catch (error) {
      /* empty */
    }
    try {
      idCradPhotoCheckRef.value.closeCamera();
    } catch (error) {
      /* empty */
    }
  };
  const tableData = ref([]);
  const getFilterResultStyle = (str) => {
    try {
      const statusList = [
        '人脸比对失败',
        '重复试验',
        '待入组',
        '已经入组',
        '筛选期内',
        '安全期内',
        '安全期内2',
        '未筛选',
        '入组失败'
      ];
      const suggestList = ['建议安全期内'];
      const passList = ['初筛合格', '筛选合格', '入组成功', '筛查通过'];
      if (statusList.some((status) => str.includes(status))) {
        return false;
      } else if (suggestList.some((status) => str.includes(status))) {
        return false;
      } else if (passList.some((status) => str.includes(status))) {
        return true;
      }
      return false;
    } catch (error) {
      return '';
    }
  };
  // 添加页面可见性相关的函数和变量
  const handleVisibilityChange = () => {
    console.log(111, document.hidden);
    if (document.hidden) {
      // 页面不可见时，清除轮询
      if (interval.value) {
        clearInterval(interval.value);
        interval.value = null;
      }
    } else {
      // 页面可见时，重新开启轮询
      if (!interval.value && !driveErr.value) {
        interval.value = setInterval(() => {
          idCardRead();
        }, 1000);
      }
    }
  };
  onBeforeUnmount(() => {
    // 移除页面可见性变化事件监听
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    clearInterval(interval.value);
    ws.close();
  });

  // 生成受试者信息
  const genSubjectInfo = () => {
    readLoading.value = true;

    let idCard = getIdNo();
    let idCardInfo = analyzeIDCard(idCard);
    let sex = idCardInfo.sex;

    userInfo.value.idcard = idCard.replace(
      /(\d{6})\d+(\d{4})/,
      '$1**********$2'
    );
    userInfo.value.IdCard = idCard;
    userInfo.value.Name = getName();
    userInfo.value.Nation = '汉族';
    userInfo.value.Address = '测试地址';
    userInfo.value.telephone = getMoble();
    userInfo.value.Birthday = idCardInfo.birthday;
    userInfo.value.Region = idCardInfo.province;
    userInfo.value.Sex = idCardInfo.sex;
    userInfo.value.Age = idCardInfo.age;
    if (sex == '男') {
      userInfo.value.PicBase64String =
        '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';
    } else {
      userInfo.value.PicBase64String =
        '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';
    }
    showEye.value = false;
    readLoading.value = false;
    isSign.value = true;
  };

  const getName = () => {
    let familyNames =
      '赵钱孙李周吴郑王冯陈褚卫蒋沈韩杨朱秦尤许何吕施张孔曹严华金魏陶姜';
    let givenNames =
      '子璇淼国栋夫子瑞堂甜敏尚浩曼皓曼国贤贺祥晨涛昊轩易轩辰益帆冉瑾春瑾昆春齐杨文昊东雄霖浩晨熙涵溶溶冰枫欣宜豪欣慧建政美欣淑慧文轩杰欣源忠林榕润欣汝慧嘉新建建林亦菲林冰洁佳欣涵涵禹辰淳美泽惠伟洋涵越润丽翔淑华晶莹凌晶苒溪雨涵嘉怡佳毅子辰佳琪紫轩瑞辰昕蕊萌明远欣宜泽远欣怡佳怡佳惠晨茜晨璐运昊汝鑫淑君晶滢润莎榕汕佳钰佳玉晓庆一鸣语晨添池添昊雨泽雅晗雅涵清妍诗悦嘉乐晨涵天赫玥傲佳昊天昊萌萌若萌测试身份证号大全和名由程序随机组合而成所有信息均为虚构生成不会泄密真实公民隐私信息也非现实生活中真实的身份证号码和真实名身份证号码所属年龄均为岁以上均已通过校验身份证号码和名仅供测试或用在必须输入身份证号码和名的网站上请不要将身份证号码和名用于任何非法用途且自行承担使用本工具的任何后果和责任';

    let i = parseInt(familyNames.length * Math.random());
    let familyName = familyNames.substr(i, 1);
    let len = givenNames.length;
    let j = parseInt(len * Math.random());
    let k = parseInt(len * Math.random());
    let givenName = givenNames.substr(j, 1) + givenNames.substr(k, 1);
    let name = familyName + givenName;
    return name;
  };

  const getIdNo = () => {
    let coefficientArray = [
      '7',
      '9',
      '10',
      '5',
      '8',
      '4',
      '2',
      '1',
      '6',
      '3',
      '7',
      '9',
      '10',
      '5',
      '8',
      '4',
      '2'
    ]; // 加权因子
    let lastNumberArray = [
      '1',
      '0',
      'X',
      '9',
      '8',
      '7',
      '6',
      '5',
      '4',
      '3',
      '2'
    ]; // 校验码
    let address = '210102'; // 住址

    let birthday =
      '' +
      (parseInt(40 * Math.random()) + 1960) +
      '0' +
      (parseInt(9 * Math.random()) + 1) +
      (parseInt(20 * Math.random()) + 10); // 生日
    let s =
      Math.floor(Math.random() * 10).toString() +
      Math.floor(Math.random() * 10).toString() +
      Math.floor(Math.random() * 10).toString();
    let array = (address + birthday + s).split('');
    let total = 0;
    for (let i = 0; i < array.length; i++) {
      total = total + parseInt(array[i]) * parseInt(coefficientArray[i]);
    }
    let lastNumber = lastNumberArray[parseInt(total % 11)];
    let id_no_String = address + birthday + s + lastNumber;
    return id_no_String;
  };

  const getMoble = () => {
    let prefixArray = new Array(
      '135',
      '136',
      '137',
      '138',
      '139',
      '150',
      '151',
      '152',
      '157',
      '158',
      '159',
      '182',
      '183',
      '187',
      '188',
      '198',
      '130',
      '131',
      '132',
      '155',
      '156',
      '186',
      '181',
      '189'
    );
    let i = parseInt(10 * Math.random());
    let prefix = prefixArray[i];
    for (let j = 0; j < 8; j++) {
      prefix = prefix + Math.floor(Math.random() * 10);
    }
    return prefix;
  };

  const analyzeIDCard = (IDCard) => {
    let sexAndAge = {};
    // 获取用户身份证号码
    let userCard = IDCard;
    // 如果身份证号码为undefind则返回空
    if (!userCard) {
      return sexAndAge;
    }
    let province = {
      11: '北京市',
      12: '天津市',
      13: '河北省',
      14: '山西省',
      15: '内蒙古自治区',
      21: '辽宁省',
      22: '吉林省',
      23: '黑龙江省',
      31: '上海省',
      32: '江苏省',
      33: '浙江省',
      34: '安徽省',
      35: '福建省',
      36: '江西省',
      37: '山东省',
      41: '河南省',
      42: '湖北省',
      43: '湖南省',
      44: '广东省',
      45: '广西壮族自治区',
      46: '海南省',
      50: '重庆省',
      51: '四川省',
      52: '贵州省',
      53: '云南省',
      54: '西藏自治区',
      61: '陕西省',
      62: '甘肃省',
      63: '青海省',
      64: '宁夏回族自治区',
      65: '新疆维吾尔自治区',
      71: '台湾省',
      81: '香港特别行政区',
      82: '澳门特别行政区'
    };
    // 获取省份
    sexAndAge.province = province[parseInt(userCard.substr(0, 2))];
    // 获取性别
    if (parseInt(userCard.substr(16, 1), 10) % 2 === 1) {
      sexAndAge.sex = '男';
    } else {
      sexAndAge.sex = '女';
    }
    // 获取出生年月日
    sexAndAge.birthday =
      userCard.substring(6, 10) +
      '-' +
      userCard.substring(10, 12) +
      '-' +
      userCard.substring(12, 14);
    let yearBirth = userCard.substring(6, 10);
    let monthBirth = userCard.substring(10, 12);
    let dayBirth = userCard.substring(12, 14);
    // 获取当前年月日并计算年龄
    let myDate = new Date();
    let monthNow = myDate.getMonth() + 1;
    let dayNow = myDate.getDay();
    let age = myDate.getFullYear() - yearBirth;
    if (
      monthNow < monthBirth ||
      (monthNow === monthBirth && dayNow < dayBirth)
    ) {
      age -= 1;
    }
    // 得到年龄
    sexAndAge.age = age;
    // 返回
    return sexAndAge;
  };
</script>
<style lang="scss" scoped>
  :deep(.formConfirm .el-form-item__label) {
    height: 50px; /* 设置你想要的固定高度 */
    line-height: 50px; /* 设置行高与高度一致，确保文字垂直居中 */
  }
  :deep(.filterForm2 .el-input__inner) {
    font-size: 22px !important;
    color: #000 !important;
  }
  .not-people {
    // display: flex;
    // justify-content: space-between;
    // align-items: center;
    padding-left: 15px;
    .txt1 {
      color: #ff4d4f;
      font-size: 18px;
    }
    .txt2 {
      color: #3b426f;
      font-size: 18px;
    }
  }
</style>
<style>
  .testmodal-content {
    position: relative;
  }
  .image-slot {
    width: 130px;
    height: 100%;
    box-sizing: border-box;
    border: 1px solid #94aed9;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background: #f9f9f9;
  }
  .testmodal-title {
    margin-top: 10px;
    margin-bottom: 25px;
    color: #517bff;
    font-family:
      Microsoft YaHei,
      Microsoft YaHei;
    font-weight: 400;
    font-size: 18px;
    color: #3b426f;
    text-align: left;
  }
  .test .el-dialog__body {
    padding-top: 0 !important;
  }
  .testModal .ele-modal-body.is-form {
    padding-top: 0;
  }
  .testModalProjectName {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 3px 10px;
    font-size: 14px;
    background: linear-gradient(-270deg, #517bff 0%, #a8ccff 100%);
    color: #ffffff;
    line-height: 28px;
  }
  .el-message-box__btns {
    display: flex;
    flex-wrap: nowrap; /* 确保按钮不换行 */
    justify-content: center; /* 按钮靠右对齐 */
  }
  .filterForm .el-form-item {
    margin-bottom: 25px;
  }
  .el-dialog__header .el-dialog__title {
    color: #4f5e84 !important;
    font-weight: 700 !important;
    font-size: 20px !important;
  }
  .el-message-box .el-message-box__header .el-message-box__title {
    text-align: center !important;
    color: #4f5e84 !important;
    font-weight: 700 !important;
    font-size: 20px !important;
  }
  /* .el-message-box__message {
    font-size: 22px !important;
  } */
</style>
