<template>
  <ele-modal
    form
    :width="1300"
    :model-value="modelValue"
    title="全部项目"
    :body-style="{ marginTop: '-40px' }"
    @update:modelValue="updateModelValue"
  >
    <ele-card flex-table style="margin: 0px; padding: 0px">
      <ele-pro-table
        ref="tableRef"
        row-key="projectId"
        :columns="columns"
        :datasource="datasource"
        highlight-current-row
        cache-key="smoAllTable"
      >
        <template #isPreference="{ row }">
          <ele-dot
            :text="row.isPreference === '1' ? '是' : '否'"
            :type="row.isPreference === '1' ? 'success' : 'info'"
          />
        </template>
        <template #action="{ row }">
          <el-space>
            <el-link type="primary" :underline="false" @click="viewFun(row)">
              查看
            </el-link>
          </el-space>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-modal>
</template>
<script setup>
  import { ref, computed } from 'vue';
  import { getFollowProjectStatistics } from '@/api/project/index';
  import { useRouter } from 'vue-router';
  const router = useRouter();
  defineProps({
    modelValue: Boolean
  });
  const emit = defineEmits(['update:modelValue']);
  // 搜索组件实例
  const searchRef = ref();

  // 表格实例
  const tableRef = ref(null);

  // 搜索表单是否展开
  const searchExpand = ref(false);

  // 表格列配置
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        label: `序号`,
        width: 70,
        align: 'center',
        fixed: 'left'
      },

      {
        label: `项目类型`,
        prop: 'projectRecruitType',
        width: '120',
        align: 'center',
        formatter: (row) => (row.projectRecruitType == 1 ? '健康' : '患者')
      },
      {
        label: `项目名称`,
        prop: 'projectRecruitName',
        minWidth: '200',
        showOverflowTooltip: true
      },
      {
        label: `通知`,
        prop: 'notifyNumber',
        width: '80',
        align: 'center'
      },
      {
        label: `浏览`,
        prop: 'browseNumber',
        width: '80',
        align: 'center'
      },
      {
        label: `分享`,
        prop: 'shareNumber',
        width: '80',
        align: 'center'
      },
      {
        label: `咨询`,
        prop: 'consultNumber',
        width: '80',
        align: 'center'
      },
      {
        label: `报名`,
        prop: 'applyNumber',
        width: '80',
        align: 'center'
      },
      {
        label: `待审核`,
        prop: 'pendingReviewNumber',
        width: '80',
        align: 'center'
      },
      {
        label: `入组`,
        prop: 'joinGroupNumber',
        width: '80',
        align: 'center'
      },
      {
        label: `出组`,
        prop: 'outGroupNumber',
        width: '80',
        align: 'center'
      },
      {
        label: `失败`,
        prop: 'failNumber',
        width: '80',
        align: 'center'
      }
    ];
  });

  // 表格数据源
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getFollowProjectStatistics({
      ...where,
      ...orders,
      ...filters,
      page: page,
      limit: limit
    });
  };

  /* 搜索 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };
  /* 搜索 */
  const reloadSearch = (where) => {
    searchRef.value?.onSearchFilterSubmit(where);
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  // 查看
  const viewFun = (row) => {
    // updateModelValue(false);
    router.push({
      name: 'site_cooperation_smo_details',
      params: { id: row.companyId }
    });
  };
</script>

<style lang="scss" scoped>
  .ele-modal-header {
    margin-bottom: 0px !important;
  }
</style>
