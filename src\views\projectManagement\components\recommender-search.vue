<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 16px 0px 16px', overflow: 'hidden' }">
    <el-form label-width="80px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <!-- <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="项目ID">
            <el-input
              clearable
              v-model.trim="form.projectId"
              placeholder="请输入项目ID"
            />
          </el-form-item>
        </el-col> -->
        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="项目名称">
            <el-input
              clearable
              v-model.trim="form.projectName"
              placeholder="请输入项目名称"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="项目类型">
            <dict-data
              code="recruit_type"
              v-model="form.recruitType"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label="项目状态">
            <dict-data
              code="project_recruit_status"
              v-model="form.recruitStatus"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="8" :md="8" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { ref } from 'vue';
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    projectId: '',
    projectName: '',
    recruitType: null,
    recruitStatus: null
  });

  const projectTypeOptions = ref([
    {
      label: '健康',
      value: '1'
    },
    {
      label: '患者',
      value: '2'
    }
  ]);

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };

  defineExpose({ resetFields });
</script>
