<!-- 用户信息 -->
<template>
  <ele-dropdown
    :items="[
      // {
      //   title: t('layout.header.profile'),
      //   command: 'profile',
      //   icon: UserOutlined
      // },
      {
        title: t('layout.header.password'),
        command: 'password',
        icon: LockOutlined,
        iconStyle: { transform: 'translateY(-1px)' }
      },
      {
        title: t('layout.header.logout'),
        command: 'logout',
        icon: LogoutOutlined
      }
    ]"
    :icon-props="{ size: 15 }"
    :popper-options="{
      modifiers: [{ name: 'offset', options: { offset: [0, 5] } }]
    }"
    popper-class="header-user-popper"
    @command="handleUserDropClick"
  >
    <div class="header-avatar">
      <span
        style="
          margin-left: 4px;
          color: #ffffff;
          font-size: 16px;
          margin-right: 6px;
        "
      >
        {{ loginUser.nickName }}
      </span>
      <el-icon :size="16" style="margin: -2px 0 0 2px; color: #ffffff">
        <arrow-down />
      </el-icon>
    </div>
  </ele-dropdown>
  <!-- 修改密码弹窗 -->
  <password-modal v-model="passwordVisible" />
</template>

<script setup>
  import { computed, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { useI18n } from 'vue-i18n';
  import { ElMessageBox } from 'element-plus/es';
  import {
    ArrowDown,
    UserOutlined,
    LockOutlined,
    LogoutOutlined
  } from '@/components/icons';
  import { useUserStore } from '@/store/modules/user';
  import { logout } from '@/utils/common';
  import PasswordModal from './password-modal.vue';

  const { t } = useI18n();
  const { push } = useRouter();
  const userStore = useUserStore();

  /** 是否显示修改密码弹窗 */
  const passwordVisible = ref(false);

  /** 当前用户信息 */
  const loginUser = computed(() => userStore.info ?? {});

  /** 用户信息下拉点击 */
  const handleUserDropClick = (command) => {
    if (command === 'password') {
      passwordVisible.value = true;
    } else if (command === 'profile') {
      push('/user/profile');
    } else if (command === 'logout') {
      // 退出登录
      ElMessageBox.confirm(
        t('layout.logout.message'),
        t('layout.logout.title'),
        { type: 'warning', draggable: true, center: true }
      )
        .then(() => {
          logout(false);
        })
        .catch(() => {});
    }
  };
</script>

<style lang="scss">
  .header-avatar {
    display: flex;
    align-items: center;
    position: relative;
    height: 100%;
    outline: none;
  }
  .header-user-popper {
    &.el-dropdown__popper.ele-popper.ele-dropdown
      .el-dropdown-menu__item:hover {
      background: #5280fb !important;
      color: #ffffff !important;
      border-radius: 0px !important;
    }

    &.el-dropdown__popper.ele-popper.el-popper.is-light {
      min-width: 144px !important;
      background: rgba(255, 255, 255) !important;
      box-shadow: 0px 6px 10px 0px rgba(121, 121, 121, 0.52) !important;
      border-radius: 4px !important;
      border: 1px solid #5280fb !important;
      padding: 15px 6px 10px 6px !important;
      box-sizing: border-box;
      margin-top: 15px;
    }

    &.ele-dropdown .el-dropdown-menu__item {
      display: flex;
      justify-content: center;
      margin-bottom: 5px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 15px !important;
      border-radius: 0px !important;
      color: #4f5e84;
    }

    &.ele-dropdown .el-popper__arrow::before {
      width: 0px !important;
      height: 0px !important;
      background: transparent !important;
      border-top: 20px solid transparent !important;
      border-bottom: 20px solid transparent !important;
      border-right: 20px solid transparent !important;
      border-left: 20px solid #ffffff !important;
      right: -22px !important;
      top: -5px !important;
      transform: rotate(40deg) !important;
    }
  }
</style>
