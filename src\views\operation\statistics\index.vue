<template>
  <ele-page :flex-table="true" hide-footer>
    <operation-search ref="searchRef" @search="reload" />
    <ele-card :flex-table="true" :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        highlight-current-row
        :footer-style="{ paddingBottom: '16px' }"
        cache-key="statisticsDataTable"
      >
        <template #action="{ row }">
          <el-link
            v-if="row.status == 0"
            type="primary"
            :underline="false"
            @click="toAuditDialog(row, 1)"
          >
            初审
          </el-link>
          <el-link
            v-if="row.status == 1"
            type="primary"
            :underline="false"
            @click="toAuditDialog(row, 2)"
          >
            复审
          </el-link>
          <el-divider v-if="[0, 1].includes(row.status)" direction="vertical" />
          <el-link
            v-if="row.offFlag == 1"
            type="primary"
            :underline="false"
            @click="offLine(row)"
          >
            上架
          </el-link>
          <el-link
            v-if="row.offFlag == 0"
            type="primary"
            :underline="false"
            @click="offLine(row)"
          >
            下架
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            type="primary"
            :underline="false"
            @click="showCircleDetails(row)"
          >
            详情
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup name="operationStatistics">
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import OperationSearch from '../components/operation-search.vue';

  import { userBehaviorStatistics } from '@/api/statistics/index';

  const props = defineProps({
    /** 字典类型 */
    dictType: String
  });

  const currnetID = ref('');

  const currentData = ref({});

  /** 搜索栏实例 */
  const searchRef = ref(null);

  const isFirstAudit = ref(false);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center'
    },
    {
      prop: 'date',
      label: '日期',
      align: 'center',
      sortable: 'custom',
      minWidth: 200,
      showOverflowTooltip: true
    },
    {
      prop: 'code',
      label: '页面code',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'name',
      label: '页面名称',
      width: 200,
      showOverflowTooltip: true
    },
    {
      prop: 'type',
      label: '类型',
      minWidth: 110,
      align: 'center',
      formatter: (row) => (row.type == 1 ? '按钮' : '页面')
    },
    {
      prop: 'resource',
      label: '用户来源',
      minWidth: 110,
      align: 'center'
    },
    {
      prop: 'isLogin',
      label: '是否登录',
      minWidth: 110,
      align: 'center',
      formatter: (row) => (row.isLogin == 1 ? '已登录' : '未登录')
    },
    {
      prop: 'clickNumber',
      label: '点击次数',
      minWidth: 110,
      align: 'center'
    },
    {
      prop: 'stayDuration',
      label: '停留时间',
      minWidth: 180,
      align: 'center'
    }
  ]);

  /** 是否显示审核弹窗 */
  const showAuditDialog = ref(false);

  const showCircleDetailsDialog = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders }) => {
    return userBehaviorStatistics({
      ...where,
      // ...orders,
      ...pages,
      orderBy: orders.sort,
      isAsc: orders.order
    });
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  const offLine = async (row) => {
    const title =
      row?.offFlag == 0
        ? `确认是否下架 ‘${row?.title}’ 帖子吗？`
        : `确认是否上架 ‘${row?.title}’ 帖子吗？`;
    try {
      await ElMessageBox.confirm(title, '提示', {
        type: 'warning',
        draggable: true,
        customStyle: {
          maxWidth: '600px'
        }
      });
      // const params = {
      //   postId: row?.postId,
      //   offFlag: row?.offFlag == 0 ? 1 : 0
      // };
      const formData = new FormData();
      formData.append('postId', row?.postId);
      formData.append('offFlag', row?.offFlag == 0 ? 1 : 0);
      await postOffLine(formData);
      EleMessage.success('操作成功');
      reload();
    } catch (e) {
      if (e != 'cancel') {
        EleMessage.error(e?.message);
      }
    }
  };

  const showCircleDetails = (row) => {
    currnetID.value = row?.postId ?? null;
    Object.assign(currentData.value, row);
    showCircleDetailsDialog.value = true;
  };

  const toAuditDialog = (row, audit) => {
    showAuditDialog.value = true;
    currnetID.value = row?.postId + '' ?? null;
    isFirstAudit.value = audit == 1 ? true : false;
  };
</script>
