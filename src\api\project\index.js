import request from '@/utils/request-business';

/**
 * 修改招募项目
 * @param {*} data
 * @returns
 */
export async function saveProjectInformedInfo(data) {
  const res = await request.post(
    `/projectRcruit/saveProjectInformedInfo`,
    data
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 获取机构科室项目数量统计
 */ export async function getSiteDeptProjectStatistics(params) {
  const res = await request.get('/site/index/getSiteDeptProjectStatistics', {
    params
  });
  if (res.data.code === 200 && res.data) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询机构招募项目详细信息列表
 */
export async function getSiteRecruitDetailsList(params) {
  const res = await request.get('/projectRcruit/getSiteRecruitDetailsList', {
    params
  });
  if (res.data.code === 200 && res.data) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 获取机构所在地区志愿者数据统计
 */
export async function getSiteAreaUserData(params) {
  const res = await request.get('/site/index/getSiteAreaUserData', {
    params
  });
  if (res.data.code === 200 && res.data) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 招募进度根据科室进行汇总
 */
export async function getRecruitProgressSummaryByDept(params) {
  const res = await request.get('/site/index/getRecruitProgressSummaryByDept', {
    params
  });
  if (res.data.code === 200 && res.data) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 招募进度汇总统计
 */
export async function getRecruitProgressSummary(params) {
  const res = await request.get('/site/index/getRecruitProgressSummary', {
    params
  });
  if (res.data.code === 200 && res.data) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 机构项目统计
 */
export async function siteProjectStatistics(params) {
  const res = await request.get('/site/index/siteProjectStatistics', {
    params
  });
  if (res.data.code === 200 && res.data) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询当前组织创建的招募项目
 */
export async function getOrgPageList(params) {
  const res = await request.get('/projectRcruit/getOrgPageList', { params });
  if (res.data.code === 200 && res.data) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 获取招募项目的详情信息
 *  @param {*} data
 *  @returns
 */
export async function getProjectRcruitDetails(params) {
  const res = await request.get('/projectRcruit/getProjectRcruitDetails', {
    params
  });
  if (res.data.code === 200 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询招募中心下拉选择列表
 *  @param {*} data
 *  @returns
 */
export async function getSiteSelectList(params) {
  const res = await request.get('/siteInfo/getSiteSelectList', {
    params
  });
  if (res.data.code === 200 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 修改招募项目
 * @param {*} data
 * @returns
 */
export async function updateRcruitProject(data) {
  const res = await request.post(`/projectRcruit/update`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 新增招募项目
 * @param {*} data
 * @returns
 */
export async function addRcruitProject(data) {
  const res = await request.post(`/projectRcruit/add`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 改变项目招募状态
 * @param {*} data 招募状态 1-草稿 2-招募中 3-审核失败 4-已关闭
 * @returns
 */
export async function changeProjectRcruitStatus(data) {
  const res = await request.post(
    `/projectRcruit/changeProjectRcruitStatus`,
    data
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 删除项目
 */
export async function deleteProject(projectId) {
  const res = await request.delete('/projectRcruit/delete/' + projectId);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询健康项目的报名列表
 * @param {*} data
 * @returns
 */
export async function getHealthProjectApplyPageList(data) {
  const res = await request.post(
    `/projectApply/getHealthProjectApplyPageList`,
    data
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 添加公司
 * @param {*} data
 * @returns
 */
export async function addCompany(data) {
  const res = await request.post(`/company/addCompany`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 查询公司信息集合
 *  @param {*} data
 *  @returns
 */
export async function queryCompanyList(params) {
  const res = await request.get('/company/queryCompanyList', {
    params
  });
  if (res.data.code === 200 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 获取所有疾病信息
 *  @param {*} data
 *  @returns
 */
export async function getAllDiseaseInfo(params) {
  const res = await request.get('/homePage/getAllDiseaseInfo', {
    params
  });
  if (res.data.code === 200 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询所有项目的列表
 *  @param {*} data
 *  @returns
 */
export async function getAllProjectList(params) {
  const res = await request.get('/projectRcruit/getAllProjectList', {
    params
  });
  if (res.data.code === 200 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询公司信息集合
 *  @param {*} data
 *  @returns
 */
export async function getPageList(params) {
  const res = await request.get('/userReward/manage/getPageList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 确认打款
 * @param {*} data
 * @returns
 */
export async function confirmPayment(data) {
  const res = await request.post(`/userReward/confirmPayment`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 项目推广
 *  @param {*} data
 *  @returns
 */
export async function projectPromotion(data) {
  const res = await request.post('/projectRcruit/projectPromotion', data);
  if (res.data.code === 200) {
    return res.data?.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 确认推广计划
 *  @param {*} data
 *  @returns
 */
export async function confirmPromotion(params) {
  const res = await request.get('/projectRcruit/confirmPromotion', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 项目推广
 *  @param {*} data
 *  @returns
 */
export async function followProject(data) {
  const res = await request.post('/projectRcruit/followProject', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 统计关注项目各业务数据
 *  @param {*} data
 *  @returns
 */
export async function getFollowProjectStatistics(params) {
  const res = await request.get(
    '/index/statistics/getFollowProjectStatistics',
    {
      params
    }
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 招募项目各状态统计
 *  @param {*} data
 *  @returns
 */
export async function projectRcruitStatusStatistics(params) {
  const res = await request.get(
    '/projectRcruit/projectRcruitStatusStatistics',
    {
      params
    }
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询推荐记录统计数据
 *  @param {*} data
 *  @returns
 */
export async function getRecommendStatistics(params) {
  const res = await request.get('/index/statistics/getRecommendStatistics', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询每个项目推荐记录统计集合
 *  @param {*} data
 *  @returns
 */
export async function getProjectRecommendListStatistics(params) {
  const res = await request.get(
    '/index/statistics/getProjectRecommendListStatistics',
    {
      params
    }
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询最近的聊天列表
 *  @param {*} data
 *  @returns
 */
export async function getRecentChatList(params) {
  const res = await request.get('/index/statistics/getRecentChatList', {
    params
  });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询CRC申请岗位记录详情
 * @param {*}
 * @returns
 */
export async function getAllAreaInfo() {
  const res = await request.get('/homePage/getAllAreaInfo');
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 根据姓名和身份证查询用户间隔期天数
 * @param {*}
 * @returns
 */
export async function getUserIntervalDay(params) {
  const res = await request.get('/meshScreen/getUserIntervalDay', { params });
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 用户间隔期检查
 *  @param {*} data
 *  @returns
 */
export async function userIntervalCheck(data) {
  const res = await request.post('/meshScreen/userIntervalCheck', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 运营后台报名项目
 *  @param {*} data
 *  @returns
 */
export async function managerApplyProject(data) {
  const res = await request.post('/projectApply/managerApplyProject', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 解析方案文本并返回项目详情
 *  @param {*} data
 *  @returns
 */
export async function parseProjectDetails(data) {
  const res = await request.post('/projectRcruit/parseProjectDetails', data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导入健康项目
 */
export async function importHealthyProjects(file) {
  const formData = new FormData();
  formData.append('file', file);
  const res = await request.post(
    '/projectRcruit/importHealthyProjects',
    formData
  );
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 导入患者项目
 */
export async function importPatientProjects(file) {
  const formData = new FormData();
  formData.append('file', file);
  const res = await request.post(
    '/projectRcruit/importPatientProjects',
    formData
  );
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
