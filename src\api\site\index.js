import request from '@/utils/request-business';

/**
 * 获取机构的信息
 * @param {*} data
 * @returns
 */
export async function getSiteInfo(params) {
  const res = await request.get('/siteInfo/getInfo', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 修改机构基本信息
 * @param {*} data
 * @returns
 */
export async function updateBasicInfo(data) {
  const res = await request.put('/siteInfo', data);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}
