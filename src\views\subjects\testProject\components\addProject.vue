<template>
  <div>
    <ele-modal
      form
      width="1200px"
      :model-value="modelValue"
      :title="
        isUpdate ? (isCopyAdd ? '复制新增' : '编辑试验项目') : '新增试验项目'
      "
      @update:modelValue="updateModelValue"
      class="simplebar-modal"
      align-center
    >
      <div data-simplebar class="modal-body" style="padding: 0 15px">
        <el-form
          ref="formRef"
          label-position="left"
          label-width="155px"
          :model="form"
          :rules="rules"
        >
          <div class="title-top">试验项目信息</div>
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item
                label="方案编号"
                prop="schemeCode"
                class="formRequired"
              >
                <el-input
                  v-model="form.schemeCode"
                  placeholder="请输入方案编号"
                  clearable
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="试验编号" prop="code" class="formRequired">
                <el-input
                  v-model="form.code"
                  placeholder="请输入试验编号"
                  clearable
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="试验名称" prop="name" class="formRequired">
                <el-input
                  v-model="form.name"
                  placeholder="请输入试验名称"
                  clearable
                  show-word-limit
                  maxlength="200"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="试验类别" prop="type">
                <el-select
                  v-model="form.type"
                  placeholder="请选择试验类别"
                  clearable
                >
                  <el-option label="BE" value="BE" />
                  <el-option label="PK" value="PK" />
                  <el-option label="PD" value="PD" />
                  <el-option label="SAD" value="SAD" />
                  <el-option label="MAD" value="MAD" />
                  <el-option label="其他" value="其他" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="志愿者类别"
                prop="subjectType"
                class="formRequired"
              >
                <el-select
                  v-model="form.subjectType"
                  placeholder="请选择志愿者类别"
                  clearable
                >
                  <el-option label="健康受试者" :value="Number(1)" />
                  <el-option label="患者受试者" :value="Number(2)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="试验周期" prop="cycle">
                <el-input
                  type="number"
                  v-model="form.cycle"
                  placeholder="请输入试验周期"
                  clearable
                  min="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="药品类别" prop="drugtype">
                <el-select
                  v-model="form.drugtype"
                  placeholder="请选择药品类别"
                  clearable
                >
                  <el-option label="大分子药物" value="大分子药物" />
                  <el-option label="小分子药物" value="小分子药物" />
                  <el-option label="中药" value="中药" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="给药途径" prop="route">
                <el-select
                  v-model="form.route"
                  placeholder="请选择给药途径"
                  clearable
                >
                  <el-option label="口服给药" value="口服给药" />
                  <el-option label="皮下注射给药" value="皮下注射给药" />
                  <el-option label="其他途径给药" value="其他途径给药" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="采血量分级" prop="drawblood">
                <el-select
                  v-model="form.drawblood"
                  placeholder="请选择采血量分级"
                  clearable
                >
                  <el-option label="无采血" value="无采血" />
                  <el-option label="0<采血量≤50" value="0<血量≤50" />
                  <el-option label="50<采血量≤100" value="50<采血量≤100" />
                  <el-option label="100<采血量≤150" value="100<采血量≤150" />
                  <el-option label="150<采血量≤200" value="150<采血量≤200" />
                  <el-option label="200<采血量≤250" value="200<采血量≤250" />
                  <el-option label="250<采血量≤300" value="250<采血量≤300" />
                  <el-option label="300<采血量≤400" value="300<采血量≤400" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="专业" prop="professional">
                <!-- <el-input
                  v-model="form.professional"
                  placeholder="请输入专业"
                  clearable
                /> -->
                <el-select
                  v-model="form.professional"
                  placeholder="请选择或输入专业"
                  clearable
                  filterable
                  allow-create
                >
                  <el-option
                    v-for="(dict, index) in zyList"
                    :key="index"
                    :label="dict"
                    :value="dict"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="半衰期" prop="halflife">
                <el-input
                  type="number"
                  v-model="form.halflife"
                  placeholder="请输入半衰期"
                  clearable
                  min="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="病种" prop="illness">
                <el-input
                  v-model="form.illness"
                  placeholder="请输入病种"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="试验计划开始时间"
                prop="sdate"
                class="formRequired"
              >
                <el-date-picker
                  v-model="form.sdate"
                  type="date"
                  placeholder="请选择试验计划开始时间"
                  clearable
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="试验计划结束时间"
                prop="edate"
                class="formRequired"
              >
                <el-date-picker
                  v-model="form.edate"
                  type="date"
                  placeholder="请选择试验计划结束时间"
                  clearable
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="试验计划入组时间"
                prop="joindate"
                class="formRequired"
              >
                <el-date-picker
                  v-model="form.joindate"
                  type="date"
                  placeholder="请选择试验计划入组时间"
                  clearable
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="下次试验间隔天数"
                prop="interval"
                class="formRequired"
              >
                <el-input
                  type="number"
                  v-model="form.interval"
                  placeholder="请输入建议下次试验间隔期（天）"
                  clearable
                  min="0"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="计划入组例数"
                prop="plannum"
                class="formRequired"
              >
                <el-input
                  type="number"
                  v-model="form.plannum"
                  placeholder="请输入计划入组例数"
                  clearable
                  min="1"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否为测试试验" prop="isTest">
                <el-select
                  v-model="form.isTest"
                  placeholder="请选择是否为测试试验"
                  clearable
                >
                  <el-option label="否" :value="Number(0)" />
                  <el-option label="是" :value="Number(1)" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="title-top">申办方/CRO信息</div>
          <el-row
            style="padding: 5px 10px 0 10px; margin-right: 10px"
            :gutter="40"
          >
            <el-col :span="12">
              <el-form-item
                label=" 申办方公司名称"
                prop="sponsorname"
                class="formRequired"
              >
                <div style="display: flex; width: 100%; position: relative">
                  <el-select
                    v-model="form.sponsorname"
                    placeholder="请选择，搜索不到请点击查询"
                    @keydown.space.prevent=""
                    filterable
                    remote
                    reserve-keyword
                    remote-show-suffix
                    clearable
                    style="flex: 1"
                    :remote-method="remoteMethod"
                    class="testSelect"
                  >
                    <el-option
                      v-for="(dict, index) in testTypeList"
                      :key="index"
                      :label="dict.name"
                      :value="dict.name"
                    />
                  </el-select>
                  <span class="selectBtn">
                    <Icon
                      icon="basil:search-outline"
                      style="
                        transform: scale(1.28);
                        font-size: 20px;
                        font-weight: bold;
                        color: #718ebf;
                      "
                      @click="
                        (skyEyeIndex = index),
                          (skyEyeType = 'sponsor'),
                          (showSkyEye = true)
                      "
                    />
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="申办方联系人/电话"
                prop="sponsorcontact"
                class="formRequired"
              >
                <div class="sbf-cantus">
                  <el-input
                    v-model="form.sponsorcontact"
                    placeholder="请输入联系人"
                    clearable
                    style="flex: 1; margin-right: 20px"
                    maxlength="20"
                  />
                  <el-form-item label="" prop="sponsorphone">
                    <!-- <el-input
                      v-model="form.sponsorphone"
                      placeholder="请输入电话"
                      clearable
                      style="width: 150px"
                    /> -->
                    <phoneInput
                      v-model="form.sponsorphone"
                      placeholder="请输入电话"
                      clearable
                      :style="{ width: '150px' }"
                      maxlength="13"
                    />
                  </el-form-item>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="CRO公司名称" prop="croname">
                <div style="display: flex; width: 100%; position: relative">
                  <el-select
                    v-model="form.croname"
                    placeholder="请选择，搜索不到请点击查询"
                    @keydown.space.prevent=""
                    filterable
                    remote
                    reserve-keyword
                    remote-show-suffix
                    clearable
                    style="flex: 1"
                    :remote-method="remoteMethod2"
                    class="testSelect"
                  >
                    <el-option
                      v-for="(dict, index) in testTypeList2"
                      :key="index"
                      :label="dict.name"
                      :value="dict.name"
                    />
                  </el-select>
                  <span class="selectBtn">
                    <Icon
                      icon="basil:search-outline"
                      style="
                        transform: scale(1.28);
                        font-size: 20px;
                        font-weight: bold;
                        color: #718ebf;
                      "
                      @click="
                        (skyEyeIndex = index),
                          (skyEyeType = 'cro'),
                          (showSkyEye = true)
                      "
                    />
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="CRO联系人/电话" prop="crocontact">
                <div class="sbf-cantus">
                  <el-input
                    v-model="form.crocontact"
                    placeholder="请输入联系人"
                    clearable
                    style="flex: 1; margin-right: 20px"
                    maxlength="20"
                  />
                  <el-form-item label="" prop="crophone">
                    <!-- <el-input
                      v-model="form.crophone"
                      placeholder="请输入电话"
                      clearable
                      style="width: 150px"
                    /> -->
                    <phoneInput
                      v-model="form.crophone"
                      placeholder="请输入电话"
                      clearable
                      :style="{ width: '150px' }"
                      maxlength="13"
                    />
                  </el-form-item>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="title-top">筛选条件信息</div>
          <el-row
            style="padding: 5px 10px 0 10px; margin-right: 10px"
            :gutter="40"
          >
            <el-col :span="12">
              <el-form-item
                label="距末次给药天数"
                prop="doseday"
                class="formRequired"
              >
                <el-input
                  type="number"
                  min="0"
                  v-model="form.doseday"
                  placeholder="请输入距末次给药天数"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="距末次访视天数"
                prop="trialday"
                class="formRequired"
              >
                <el-input
                  type="number"
                  min="0"
                  v-model="form.trialday"
                  placeholder="请输入距末次访视天数"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="距末次筛选天数"
                prop="filterday"
                class="formRequired"
              >
                <el-input
                  type="number"
                  min="0"
                  v-model="form.filterday"
                  placeholder="请输入距末次筛选天数"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div style="color: #b90000"
        >注：“方案编号”用于标识多个子试验同属一个项目。如果不区分：“方案编号”与“试验编号”一致</div
      >
      <template #footer>
        <div class="footer-btn">
          <el-button @click="updateModelValue(false)" type="info"
            >取 消</el-button
          >
          <el-button type="primary" :loading="loading" @click="save">
            确 定
          </el-button>
        </div>
      </template>
    </ele-modal>
    <skyEyeModal v-model="showSkyEye" @done="skyEyeReload" v-if="showSkyEye" />
    <el-dialog
      v-model="centerDialogVisible"
      title="重 要 消 息"
      width="1000"
      top="8vh"
      style="color: red !important"
      center
    >
      <div style="display: flex; justify-content: center; margin-top: 15px">
        <el-image
          style="width: 780px; height: 560px"
          :src="isSiginUrl"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="circleDetailsImages"
          show-progress
          :initial-index="6"
          :url-list="circleDetailsImages"
          fit="cover"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="toDownload()"> 下载图片 </el-button>
          <el-button type="primary" @click="centerDialogVisible = false">
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { ref, reactive, watch, nextTick, computed } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { Search } from '@element-plus/icons-vue';
  import skyEyeModal from './skyEyeModal.vue';
  import { Icon } from '@iconify/vue';
  import phoneInput from '@/views/subjects/components/phoneInput/index.vue';
  import {
    sponsorList,
    trialsSave,
    trialsUpdate,
    trialsProjectInfo,
    professionalList
  } from '@/api/subjects/testProject/index.js';
  import { useUserStore } from '@/store/modules/user';
  const userStore = useUserStore();
  const loginUser = computed(() => userStore.info ?? {});
  const centerDialogVisible = ref(false);
  const isSiginUrl = ref(
    'https://ibank.gcpcrc.com/zmt/warn-create-project.png'
  );
  const circleDetailsImages = ref([
    'https://ibank.gcpcrc.com/zmt/warn-create-project.png'
  ]);
  const emit = defineEmits(['done', 'update:modelValue']);
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    data: Object,
    isCopyAdd: {
      type: Boolean,
      default: () => false
    }
  });

  // 表单实例
  const formRef = ref(null);
  const isUpdate = ref(false);
  const loading = ref(false);
  const showSkyEye = ref(false);
  const skyEyeIndex = ref(0);
  // 表单数据
  const { form, resetFields, assignFields } = useFormData({
    schemeCode: undefined,
    code: undefined,
    name: undefined,
    type: undefined,
    subjectType: undefined,
    cycle: undefined,
    drugtype: undefined,
    route: undefined,
    drawblood: undefined,
    professional: undefined,
    halflife: undefined,
    illness: undefined,
    interval: 90,
    sdate: undefined,
    edate: undefined,
    joindate: undefined,
    plannum: undefined,
    isTest: 0,
    sponsorname: undefined,
    sponsorcontact: undefined,
    sponsorphone: undefined,
    croname: undefined,
    crocontact: undefined,
    crophone: undefined,
    doseday: 90,
    trialday: 90,
    filterday: 5
  });
  // 表单验证规则
  const rules = reactive({
    schemeCode: [
      { required: true, message: '方案编号不能为空', trigger: 'change' }
    ],
    code: [{ required: true, message: '试验编号不能为空', trigger: 'change' }],
    name: [{ required: true, message: '试验名称不能为空', trigger: 'change' }],
    subjectType: [
      { required: true, message: '志愿者类别不能为空', trigger: 'change' }
    ],
    interval: [
      {
        required: true,
        message: '建议下次试验间隔期(天)不能为空',
        trigger: 'change'
      }
    ],
    sdate: [
      {
        required: true,
        message: '试验计划开始时间不能为空',
        trigger: 'change'
      }
    ],
    edate: [
      {
        required: true,
        message: '试验计划结束时间不能为空',
        trigger: 'change'
      }
    ],
    joindate: [
      {
        required: true,
        message: '试验计划入组时间不能为空',
        trigger: 'change'
      }
    ],
    plannum: [
      {
        required: true,
        message: '计划入组例数不能为空',
        trigger: 'change'
      }
    ],
    sponsorname: [
      {
        required: true,
        message: '申办方不能为空',
        trigger: 'change'
      }
    ],
    sponsorcontact: [
      {
        required: true,
        message: '申办方联系人不能为空',
        trigger: 'change'
      }
    ],
    sponsorphone: [
      {
        required: true,
        message: '申办方电话不能为空',
        trigger: 'change'
      }
      // {
      //   pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      //   message: '请输入正确的手机号码',
      //   trigger: 'change'
      // }
    ],
    doseday: [
      {
        required: true,
        message: '距末次给药天数不能为空',
        trigger: 'change'
      }
    ],
    trialday: [
      {
        required: true,
        message: '距末次访视天数不能为空',
        trigger: 'change'
      }
    ],
    filterday: [
      {
        required: true,
        message: '距末次筛选天数不能为空',
        trigger: 'change'
      }
    ]
  });
  const testTypeList = ref([]);
  const testTypeList2 = ref([]);
  const testTypeCache = ref([]);
  const testTypeCache2 = ref([]);
  const zyList = ref([]);
  const skyEyeType = ref('');

  const remoteMethod = (query) => {
    if (query) {
      sponsorList({
        keywords: query,
        pageNumber: 1,
        pageSize: 200,
        type: 1,
        orderBy: '',
        orderItem: 'asc'
      }).then((res) => {
        testTypeList.value = res.data.list;
      });
    } else {
      testTypeList.value = testTypeCache.value;
    }
  };
  const remoteMethod2 = (query) => {
    if (query) {
      sponsorList({
        keywords: query,
        pageNumber: 1,
        pageSize: 200,
        type: 2,
        orderBy: '',
        orderItem: 'asc'
      }).then((res) => {
        testTypeList2.value = res.data.list;
      });
    } else {
      testTypeList2.value = testTypeCache2.value;
    }
  };
  const getInitData = async () => {
    sponsorList({
      pageNumber: 1,
      pageSize: 200,
      type: 1,
      orderBy: '',
      orderItem: 'asc',
      keywords: ''
    }).then((res) => {
      if (res.code == 200) {
        testTypeList.value = res.data.list;
        testTypeCache.value = res.data.list;
      }
    });
    sponsorList({
      pageNumber: 1,
      pageSize: 200,
      type: 2,
      orderBy: '',
      orderItem: 'asc',
      keywords: ''
    }).then((res) => {
      if (res.code == 200) {
        testTypeList2.value = res.data.list;
        testTypeCache2.value = res.data.list;
      }
    });
    professionalList().then((res) => {
      if (res.code == 200) {
        zyList.value = res.data;
      }
    });
  };
  const getDetailInfo = () => {
    if (props.data.guid) {
      trialsProjectInfo(props.data.guid).then((res) => {
        assignFields(res.data);
      });
    }
  };

  const toDownload = () => {
    const link = document.createElement('a');
    link.href = isSiginUrl.value;
    link.download = '智募通签到指引.png';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const skyEyeReload = (data) => {
    console.log('skyEyeReload===>', data, skyEyeType.value);
    if (skyEyeType.value == 'sponsor') {
      if (testTypeList.value.some((item) => item.value === data.value)) {
        return EleMessage.error('请勿重复添加相同公司!');
      }
      testTypeList.value.push(data);
      EleMessage.success('添加成功');
      form.sponsorname = data.label;
    }
    if (skyEyeType.value == 'cro') {
      if (testTypeList2.value.some((item) => item.value === data.value)) {
        return EleMessage.error('请勿重复添加相同公司!');
      }
      testTypeList2.value.push(data);
      EleMessage.success('添加成功');
      form.croname = data.label;
    }
  };
  nextTick(async () => {
    getInitData();
    if (props.data) {
      await getDetailInfo();
      isUpdate.value = true;
    } else {
      isUpdate.value = false;
    }
  });
  watch(
    () => props.modelValue,
    async (modelValue) => {
      if (modelValue) {
        await getInitData();
        if (props.data) {
          await getDetailInfo();
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      if (isUpdate.value) {
        if (props.isCopyAdd) {
          form.guid = '';
          //复制新增
          if (form.drawblood && form.drawblood.includes('<')) {
            form.drawblood = form.drawblood.replace(/</g, '&lt;');
          }
          const startDate = new Date(form.sdate);
          const endDate = new Date(form.edate);
          if (!(startDate < endDate))
            return EleMessage.error('试验计划结束时间要大于试验计划开始时间');
          if (form.crophone) {
            const reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/;
            if (!reg.test(form.crophone)) {
              return EleMessage.error('cro联系人手机号码格式不正确');
            }
          }
          loading.value = true;
          trialsSave(form).then((res) => {
            if (res.code == 200) {
              EleMessage.success('操作成功');
              updateModelValue(false);
              emit('done');
              resetFields();
              loading.value = false;
              if (loginUser.value.filtersignin == 0) {
                centerDialogVisible.value = true;
              }
            } else {
              EleMessage.error(res.msg);
              loading.value = false;
            }
          });
        } else {
          //编辑
          form.guid = props.data.guid;
          if (form.drawblood && form.drawblood.includes('<')) {
            form.drawblood = form.drawblood.replace(/</g, '&lt;');
          }
          const startDate = new Date(form.sdate);
          const endDate = new Date(form.edate);
          if (!(startDate < endDate))
            return EleMessage.error('试验计划结束时间要大于试验计划开始时间');
          if (form.crophone) {
            const reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/;
            if (!reg.test(form.crophone)) {
              return EleMessage.error('cro联系人手机号码格式不正确');
            }
          }
          loading.value = true;
          trialsUpdate(form).then((res) => {
            if (res.code == 200) {
              EleMessage.success('修改成功');
              updateModelValue(false);
              emit('done');
              resetFields();
              loading.value = false;
            } else {
              EleMessage.error(res.msg);
              loading.value = false;
            }
          });
        }
      } else {
        //新增
        if (form.drawblood && form.drawblood.includes('<')) {
          form.drawblood = form.drawblood.replace(/</g, '&lt;');
        }
        const startDate = new Date(form.sdate);
        const endDate = new Date(form.edate);
        if (!(startDate < endDate))
          return EleMessage.error('试验计划结束时间要大于试验计划开始时间');
        if (form.crophone) {
          const reg = /^1[3|4|5|6|7|8|9][0-9]\d{8}$/;
          if (!reg.test(form.crophone)) {
            return EleMessage.error('cro联系人手机号码格式不正确');
          }
        }
        loading.value = true;
        trialsSave(form).then((res) => {
          if (res.code == 200) {
            EleMessage.success('操作成功');
            updateModelValue(false);
            emit('done');
            resetFields();
            loading.value = false;
            if (loginUser.value.filtersignin == 0) {
              centerDialogVisible.value = true;
            }
          } else {
            EleMessage.error(res.msg);
            loading.value = false;
          }
        });
      }
    });
  };
</script>

<style lang="scss" scoped>
  .footer-btn {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .title-top {
    font-family: Microsoft YaHei;
    font-size: 16px;
    font-weight: bold;
    line-height: 20px;
    letter-spacing: 0px;
    font-variation-settings: 'opsz' auto;
    color: #3b426f;
    margin-bottom: 20px;
    .title-txt {
      // color: #000;
      // font-weight: bold;
      // padding: 0 5px;
      // background: #fff;
      font-family:
        PingFang SC,
        PingFang SC;
      font-weight: 400;
      font-size: 16px;
      color: #507aff;
    }
  }
  .modal-body {
    max-height: 560px;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .sbf-cantus {
    width: 100%;
    display: flex;
  }
  :deep(.formRequired .el-form-item__label) {
    transform: translateX(-10px);
  }
  :deep(.testSelect.el-select .el-select__suffix) {
    width: 56px;
  }

  .selectBtn {
    width: 38px;
    height: 38px;
    color: #408bfe;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #edf5ff;
    border: 1px solid #94aed9;
    box-sizing: border-box;
    border-radius: 0 4px 4px 0;
    position: absolute;
    top: 0px;
    right: 0px;
  }
  :has(> .is-focused) + .selectBtn,
  :has(> .is-hovering) + .selectBtn {
    border: 1px solid #507aff !important;
  }
  .el-form-item.is-error .selectBtn {
    border: 1px solid #e62929 !important;
  }
</style>
