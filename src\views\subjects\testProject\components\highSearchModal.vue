<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1200"
    :model-value="modelValue"
    title="高级检索"
    :align-center="false"
    @update:modelValue="updateModelValue"
  >
    <div>
      <el-form :model="params" label-width="110px" label-position="left">
        <el-row :gutter="40">
          <el-col :span="8">
            <el-form-item label="试验名称：" style="width: 100%">
              <el-input
                v-model="params.name"
                placeholder="请输入试验名称"
                clearable
                style="width: 100%"
                maxlength="200"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="试验编号：" style="width: 100%">
              <el-input
                v-model="params.code"
                placeholder="请输入试验编号"
                clearable
                style="width: 100%"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="试验状态：" style="width: 100%">
              <el-select
                v-model="params.status"
                placeholder="请选择试验状态"
                clearable
                style="width: 100%"
              >
                <el-option label="进行中" value="1" />
                <el-option label="已完成" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="方案编号：" style="width: 100%">
              <el-input
                v-model="params.schemeCode"
                placeholder="请输入方案编号"
                clearable
                style="width: 100%"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="试验类型：" style="width: 100%">
              <el-select
                v-model="params.type"
                placeholder="请选择试验类型"
                clearable
                style="width: 100%"
              >
                <el-option label="BE" value="BE" />
                <el-option label="PK" value="PK" />
                <el-option label="PD" value="PD" />
                <el-option label="SAD" value="SAD" />
                <el-option label="MAD" value="MAD" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="志愿者类别：" style="width: 100%">
              <el-select
                v-model="params.subjectType"
                placeholder="请选择志愿者类别"
                clearable
                style="width: 100%"
              >
                <el-option label="健康受试者" value="1" />
                <el-option label="患者受试者" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="试验周期：" style="width: 100%">
              <el-input
                type="number"
                v-model="params.cycle"
                placeholder="请输入试验周期"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="采血量分级：" style="width: 100%">
              <el-select
                v-model="params.drawblood"
                placeholder="请选择采血量分级"
                clearable
                style="width: 100%"
              >
                <el-option label="无采血" value="无采血" />
                <el-option label="0<采血量≤50" value="0<血量≤50" />
                <el-option label="50<采血量≤100" value="50<采血量≤100" />
                <el-option label="100<采血量≤150" value="100<采血量≤150" />
                <el-option label="150<采血量≤200" value="150<采血量≤200" />
                <el-option label="200<采血量≤250" value="200<采血量≤250" />
                <el-option label="250<采血量≤300" value="250<采血量≤300" />
                <el-option label="300<采血量≤400" value="300<采血量≤400" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="药品类型：" style="width: 100%">
              <el-select
                v-model="params.drugtype"
                placeholder="请选择药品类型"
                clearable
                style="width: 100%"
              >
                <el-option label="大分子药物" value="大分子药物" />
                <el-option label="小分子药物" value="小分子药物" />
                <el-option label="中药" value="中药" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="给药途径：" style="width: 100%">
              <el-select
                v-model="params.route"
                placeholder="请选择给药途径"
                clearable
                style="width: 100%"
              >
                <el-option label="口服给药" value="口服给药" />
                <el-option label="皮下注射给药" value="皮下注射给药" />
                <el-option label="其他途径给药" value="其他途径给药" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="病种：" style="width: 100%">
              <el-input
                v-model="params.illness"
                placeholder="请输入病种"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="半衰期：" style="width: 100%">
              <el-input
                type="number"
                v-model="params.halflife"
                placeholder="请输入半衰期"
                clearable
                style="width: 100%"
                min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专业：" style="width: 100%">
              <el-input
                v-model="params.professional"
                placeholder="请输入专业"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="试验间隔期：" style="width: 100%">
              <el-input
                type="number"
                v-model="params.interval"
                placeholder="请输入试验间隔期(天)"
                clearable
                style="width: 100%"
                min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="日期范围：" style="width: 100%">
              <el-date-picker
                v-model="timedateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 260px"
                @change="change"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit"
          >搜 索</el-button
        >
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref } from 'vue';
  const emit = defineEmits(['done', 'update:modelValue']);

  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object
  });
  const loading = ref(false);
  const timedateRange = ref([]);
  const change = () => {
    if (timedateRange.value && timedateRange.value.length > 0) {
      params.value.startDate = timedateRange.value[0];
      params.value.endDate = timedateRange.value[1];
    } else {
      params.value.startDate = '';
      params.value.endDate = '';
    }
  };
  const close = () => {
    updateModelValue(false);
  };
  const submit = () => {
    emit('done', params.value);
    updateModelValue(false);
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const params = ref({});
  nextTick(async () => {
    params.value = JSON.parse(JSON.stringify(props.data));
    timedateRange.value = [params.value.startDate, params.value.endDate];
  });
</script>

<style lang="scss" scoped>
  .modal-dialog {
    width: 100%;
    height: 600px;
    overflow-y: auto;
    overflow-x: hidden;
  }
</style>
