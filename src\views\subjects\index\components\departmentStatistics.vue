<template>
  <ele-card style="height: 410px">
    <template #header>
      <div class="d-flex" style="height: 25px">
        <div style="display: flex; align-items: center"
          >全院科室项目统计图
        </div>
      </div>
    </template>
    <div v-if="loading">
      <ele-loading :loading="loading" text="数据加载中" background="red">
        <div style="width: 100%; height: 500px; background: #ffffff"></div>
      </ele-loading>
    </div>
    <div
      style="
        width: 100%;
        height: 340px;
        padding: 0 20px;
        box-sizing: border-box;
      "
    >
      <div ref="main" style="width: 100%; height: 100%"></div>
    </div>
  </ele-card>
</template>
<script setup name="departmentStatistics">
  import { nextTick, onMounted, ref } from 'vue';
  import * as echarts from 'echarts';
  import { Icon } from '@iconify/vue';
  import { getSiteDeptProjectStatistics } from '@/api/project/index';
  import {
    getYAxisMaxValue,
    formatYAxisLabel,
    FormattedNumber
  } from '@/utils/common.js';
  const emit = defineEmits(['done']);
  const caseTimedateRange = ref([]);
  const monthType = ref(3);

  const main = ref(null); // 使用ref创建虚拟DOM引用，使用时用main.value
  const option = ref({});
  const tableData = ref([]);
  const loading = ref(false);
  const chartInitData = async () => {
    const myChart = echarts.init(main.value);
    loading.value = true;

    await getSiteDeptProjectStatistics().then((res) => {
      tableData.value = res.data || [];
      loading.value = false;
    });
    emit('done', 'numchangeLoading');
    option.value = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
        }
      },
      legend: {},
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: tableData.value.map((val) => val.deptName)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '健康项目',
          type: 'bar',
          stack: 'total',
          label: {
            show: true
          },
          barWidth: '50%',
          itemStyle: {
            color: '#5280FB'
          },
          emphasis: {
            focus: 'series'
          },
          data: tableData.value.map((val) => val.healthNumber)
        },
        {
          name: '患者项目',
          type: 'bar',
          stack: 'total',
          label: {
            show: true
          },
          barWidth: '50%',
          itemStyle: {
            color: '#31B5F5'
          },
          emphasis: {
            focus: 'series'
          },
          data: tableData.value.map((val) => val.patientNumber)
        }
      ]
    };
    // option.value = {
    //   tooltip: {
    //     trigger: 'axis',
    //     axisPointer: {
    //       type: 'cross',
    //       crossStyle: {
    //         color: '#999'
    //       }
    //     },
    //     formatter: function (params) {}
    //   },
    //   legend: {
    //     data: ['健康项目', '患者项目']
    //   },
    //   xAxis: [
    //     {
    //       type: 'category',
    //       data: tableData.value.map((val) => val.deptName),
    //       axisPointer: {
    //         type: 'shadow'
    //       },
    //       axisLabel: {
    //         interval: 0, // 显示所有标签
    //         rotate: 15, // 旋转标签
    //         formatter: function (value) {
    //           // 设置最大长度
    //           var maxLength = 4; // 最大字符数
    //           return value.length > maxLength
    //             ? value.substring(0, maxLength) + '...'
    //             : value;
    //         }
    //       }
    //     }
    //   ],
    //   yAxis: [
    //     {
    //       type: 'value',
    //       min: 0,
    //       max: getYAxisMaxValue(tableData.value.map((val) => val.projectTotal)),
    //       axisLabel: {
    //         formatter: (value) => `${value}`
    //       },
    //       // name: '人次', // 左 Y 轴显示的文字
    //       nameLocation: 'end', // 文字位置
    //       nameGap: 30 // 文字与坐标轴的间距
    //     }
    //   ],
    //   series: [
    //     {
    //       name: '健康项目',
    //       type: 'bar',
    //       data: tableData.value.map((val) => val.healthNumber),
    //       barWidth: '25%',
    //       barCategoryGap: '30%',
    //       barGap: '20%',
    //       itemStyle: {
    //         color: '#5280FB'
    //       },
    //       label: {
    //         show: false,
    //         formatter: (params) => `${params.value}`
    //       },
    //       xAxisIndex: 0,
    //       z: 1
    //     },
    //     {
    //       name: '患者项目',
    //       type: 'bar',
    //       data: tableData.value.map((val) => val.patientNumber),
    //       barWidth: '25%',
    //       barCategoryGap: '30%',
    //       barGap: '20%',
    //       itemStyle: {
    //         color: '#31B5F5'
    //       },
    //       label: {
    //         show: false,
    //         formatter: (params) => `${params.value}`
    //       },
    //       xAxisIndex: 0,
    //       z: 1
    //     }
    //   ]
    // };

    option.value && myChart.setOption(option.value);
  };

  const initData = () => {
    nextTick(() => {
      chartInitData();
    });
  };

  onMounted(() => {
    initData();
  });
</script>
<style lang="scss" scoped>
  .title {
    color: #718ebf;
    font-size: 18px;
  }

  .content {
    font-weight: 700;
    font-size: 18px;
    color: #5280fb;
  }

  .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .all-title {
    height: 22px;
    font-size: 16px;
    color: #718ebf;
    line-height: 22px;
    cursor: pointer;
  }
</style>
