/**
 * 浏览器检测工具
 * 用于检测用户浏览器类型，如果不是Chrome浏览器则提示用户下载
 */

/**
 * 检测浏览器类型
 * @returns {Object} 包含浏览器信息的对象
 */
export function detectBrowser() {
  const userAgent = navigator.userAgent.toLowerCase();
  const vendor = navigator.vendor?.toLowerCase() || '';

  // 检测各种浏览器 - 严格区分真正的Chrome和其他Chromium内核浏览器
  const browsers = {
    // 只有真正的Chrome浏览器才返回true，排除Edge等Chromium内核浏览器
    chrome:
      /chrome/.test(userAgent) &&
      /google inc/.test(vendor) &&
      !/edg/.test(userAgent) &&
      !/opr/.test(userAgent),
    firefox: /firefox/.test(userAgent),
    safari:
      /safari/.test(userAgent) &&
      /apple computer/.test(vendor) &&
      !/chrome/.test(userAgent),
    edge: /edg/.test(userAgent), // Edge浏览器（基于Chromium）
    ie: /msie|trident/.test(userAgent),
    opera: /opera|opr/.test(userAgent)
  };

  // 获取浏览器版本
  let version = '';
  if (browsers.firefox) {
    const match = userAgent.match(/firefox\/(\d+)/);
    version = match ? match[1] : '';
  } else if (browsers.safari) {
    const match = userAgent.match(/version\/(\d+)/);
    version = match ? match[1] : '';
  } else if (browsers.edge) {
    const match = userAgent.match(/edg\/(\d+)/);
    version = match ? match[1] : '';
  } else if (browsers.chrome) {
    const match = userAgent.match(/chrome\/(\d+)/);
    version = match ? match[1] : '';
  }
  return {
    isChrome: browsers.chrome,
    isFirefox: browsers.firefox,
    isSafari: browsers.safari,
    isEdge: browsers.edge,
    isIE: browsers.ie,
    isOpera: browsers.opera,
    version,
    userAgent: navigator.userAgent
  };
}

/**
 * 检查是否为推荐的浏览器
 * @returns {boolean} 是否为推荐浏览器
 */
export function isRecommendedBrowser() {
  const browser = detectBrowser();
  return browser.isChrome;
}

/**
 * 检查用户是否已选择继续使用当前浏览器
 */
function hasUserDismissedAlert() {
  try {
    return localStorage.getItem('browser-alert-dismissed') === 'true';
  } catch (e) {
    return false;
  }
}

/**
 * 标记用户已选择继续使用当前浏览器
 */
// function markAlertDismissed() {
//   try {
//     localStorage.setItem('browser-alert-dismissed', 'true');
//   } catch (e) {
//     // 忽略localStorage错误
//   }
// }

/**
 * 显示浏览器兼容性提示弹窗
 */
export function showBrowserAlert() {
  const browser = detectBrowser();

  if (browser.isChrome) {
    return; // 只有真正的Chrome浏览器不显示提示
  }

  // 检查用户是否已选择继续使用
  if (hasUserDismissedAlert()) {
    return; // 用户已选择继续使用，不再显示提示
  }

  // 创建弹窗HTML
  const alertHtml = `
    <div id="browser-alert-overlay" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 10000;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    ">
      <div style="
        background: white;
        border-radius: 8px;
        padding: 24px;
        max-width: 480px;
        width: 90%;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        text-align: center;
      ">
        <div style="
          width: 64px;
          height: 64px;
          margin: 0 auto 16px;
          background: #f56565;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 24px;
        ">⚠</div>
        
        <h3 style="
          margin: 0 0 12px 0;
          color: #2d3748;
          font-size: 20px;
          font-weight: 600;
        ">浏览器兼容性提示</h3>
        
        <p style="
          margin: 0 0 20px 0;
          color: #4a5568;
          font-size: 14px;
          line-height: 1.5;
        ">
          检测到您当前使用的不是Google Chrome浏览器。<br>
          本系统仅推荐使用<strong>Google Chrome浏览器</strong>访问，<br>
          其他浏览器（包括Edge、Safari等）可能存在兼容性问题。
        </p>
        
        <div style="
          display: flex;
          gap: 12px;
          justify-content: center;
          flex-wrap: wrap;
        ">
          <a href="https://www.google.cn/intl/zh-CN_ALL/chrome/fallback/" 
             target="_blank"
             style="
               background: #4285f4;
               color: white;
               padding: 10px 20px;
               border-radius: 6px;
               text-decoration: none;
               font-size: 14px;
               font-weight: 500;
               transition: background-color 0.2s;
             "
             onmouseover="this.style.backgroundColor='#3367d6'"
             onmouseout="this.style.backgroundColor='#4285f4'">
            下载Chrome浏览器
          </a>
          
          <button onclick="
                    try { localStorage.setItem('browser-alert-dismissed', 'true'); } catch(e) {}
                    document.getElementById('browser-alert-overlay').remove();
                    document.body.style.overflow = '';
                  "
                  style="
                    background: #e2e8f0;
                    color: #4a5568;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: background-color 0.2s;
                  "
                  onmouseover="this.style.backgroundColor='#cbd5e0'"
                  onmouseout="this.style.backgroundColor='#e2e8f0'">
            继续使用
          </button>
        </div>
        
        <p style="
          margin: 16px 0 0 0;
          color: #718096;
          font-size: 12px;
        ">
          当前浏览器：${getBrowserName(browser)} ${browser.version}
        </p>
      </div>
    </div>
  `;

  // 插入到页面中
  document.body.insertAdjacentHTML('beforeend', alertHtml);

  // 阻止页面滚动
  document.body.style.overflow = 'hidden';

  // 监听关闭事件，恢复页面滚动
  const overlay = document.getElementById('browser-alert-overlay');
  if (overlay) {
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        overlay.remove();
        document.body.style.overflow = '';
      }
    });
  }
}

/**
 * 获取浏览器名称
 * @param {Object} browser 浏览器检测结果
 * @returns {string} 浏览器名称
 */
function getBrowserName(browser) {
  if (browser.isChrome) return 'Chrome';
  if (browser.isFirefox) return 'Firefox';
  if (browser.isSafari) return 'Safari';
  if (browser.isEdge) return 'Edge';
  if (browser.isIE) return 'Internet Explorer';
  if (browser.isOpera) return 'Opera';
  return '未知浏览器';
}

/**
 * 初始化浏览器检测
 * 在页面加载完成后自动执行检测
 */
export function initBrowserDetector() {
  // 确保在DOM加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(showBrowserAlert, 500); // 延迟500ms显示，避免与页面加载冲突
    });
  } else {
    setTimeout(showBrowserAlert, 500);
  }
}

/**
 * 测试浏览器检测功能（开发环境使用）
 * 强制显示浏览器提示弹窗，用于测试
 */
export function testBrowserAlert() {
  // 创建模拟的浏览器信息
  const mockBrowser = {
    isChrome: false,
    isFirefox: true,
    isSafari: false,
    isEdge: false,
    isIE: false,
    isOpera: false,
    version: '100',
    userAgent:
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:100.0) Gecko/20100101 Firefox/100.0'
  };

  // 直接调用弹窗显示逻辑，但使用模拟数据
  const alertHtml = `
    <div id="browser-alert-overlay" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 10000;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    ">
      <div style="
        background: white;
        border-radius: 8px;
        padding: 24px;
        max-width: 480px;
        width: 90%;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        text-align: center;
      ">
        <div style="
          width: 64px;
          height: 64px;
          margin: 0 auto 16px;
          background: #f56565;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 24px;
        ">⚠</div>

        <h3 style="
          margin: 0 0 12px 0;
          color: #2d3748;
          font-size: 20px;
          font-weight: 600;
        ">浏览器兼容性提示</h3>

        <p style="
          margin: 0 0 20px 0;
          color: #4a5568;
          font-size: 14px;
          line-height: 1.5;
        ">
          检测到您当前使用的不是Google Chrome浏览器。<br>
          本系统仅推荐使用<strong>Google Chrome浏览器</strong>访问，<br>
          其他浏览器（包括Edge、Safari等）可能存在兼容性问题。
        </p>

        <div style="
          display: flex;
          gap: 12px;
          justify-content: center;
          flex-wrap: wrap;
        ">
          <a href="https://www.google.cn/intl/zh-CN_ALL/chrome/fallback/"
             target="_blank"
             style="
               background: #4285f4;
               color: white;
               padding: 10px 20px;
               border-radius: 6px;
               text-decoration: none;
               font-size: 14px;
               font-weight: 500;
               transition: background-color 0.2s;
             "
             onmouseover="this.style.backgroundColor='#3367d6'"
             onmouseout="this.style.backgroundColor='#4285f4'">
            下载Chrome浏览器
          </a>

          <button onclick="
                    try { localStorage.setItem('browser-alert-dismissed', 'true'); } catch(e) {}
                    document.getElementById('browser-alert-overlay').remove();
                    document.body.style.overflow = '';
                  "
                  style="
                    background: #e2e8f0;
                    color: #4a5568;
                    padding: 10px 20px;
                    border: none;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: background-color 0.2s;
                  "
                  onmouseover="this.style.backgroundColor='#cbd5e0'"
                  onmouseout="this.style.backgroundColor='#e2e8f0'">
            继续使用
          </button>
        </div>

        <p style="
          margin: 16px 0 0 0;
          color: #718096;
          font-size: 12px;
        ">
          当前浏览器：${getBrowserName(mockBrowser)} ${mockBrowser.version} (测试模式)
        </p>
      </div>
    </div>
  `;

  // 插入到页面中
  document.body.insertAdjacentHTML('beforeend', alertHtml);

  // 阻止页面滚动
  document.body.style.overflow = 'hidden';

  // 监听关闭事件，恢复页面滚动
  const overlay = document.getElementById('browser-alert-overlay');
  if (overlay) {
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        overlay.remove();
        document.body.style.overflow = '';
      }
    });
  }
}

/**
 * 重置浏览器提示状态（开发测试用）
 */
export function resetBrowserAlert() {
  try {
    localStorage.removeItem('browser-alert-dismissed');
    console.log('✅ 浏览器提示状态已重置');
  } catch (e) {
    console.error('❌ 重置失败:', e);
  }
}

// 默认导出主要功能
export default {
  detectBrowser,
  isRecommendedBrowser,
  showBrowserAlert,
  initBrowserDetector,
  testBrowserAlert,
  resetBrowserAlert
};
