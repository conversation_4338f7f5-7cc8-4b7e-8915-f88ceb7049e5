<template>
  <div class="flex-column">
    <div
      class="flex-between"
      style="box-sizing: border-box; align-items: center"
    >
      <span style="font-size: 18px; color: #3b426f">近5年筛选项目数</span>
    </div>
    <div v-if="loading">
      <ele-loading :loading="loading" text="数据加载中" background="red">
        <div style="width: 100%; height: 500px; background: #ffffff"></div>
      </ele-loading>
    </div>
    <div
      style="
        width: 100%;
        height: calc(100vh - 326px - 52px);
        padding: 0 20px;
        box-sizing: border-box;
      "
    >
      <div ref="main" style="width: 100%; height: 100%"></div>
    </div>
  </div>
</template>
<script setup>
  import * as echarts from 'echarts';
  import { nextTick, ref } from 'vue';
  import { filterStatistic } from '@/api/subjects/home/<USER>';
  import { getYAxisMaxValue, formatYAxisLabel } from '@/utils/common.js';
  const emit = defineEmits(['done']);
  const main = ref(null); // 使用ref创建虚拟DOM引用，使用时用main.value
  const option = ref({});
  const radio = ref('5');
  const tableData = ref([]);
  const loading = ref(false);
  const chartInitData = async () => {
    const myChart = echarts.init(main.value);
    loading.value = true;
    await filterStatistic().then((res) => {
      tableData.value = res.subjectTrialsStatisticVoList || [];
      loading.value = false;
    });
    console.log('tableData.value===>', tableData.value);
    emit('done', 'numchangeLoading');
    option.value = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        formatter: function (params) {
          let result = '';
          params.forEach((param) => {
            let seriesColor = param.color;
            let formattedValue;
            if (param.seriesName === '筛选项目数') {
              // 添加千分位分号
              formattedValue = param.value
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            } else {
              formattedValue = param.value;
            }
            result = `<div class="echarts-tooltip-series"><span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: ${seriesColor}; margin-right: 5px;"></span>${param.seriesName}:<span style="font-size:18px;color:#5280fb"> ${formattedValue}</span></div>`;
            result += `<div class="echarts-tooltip-title">${params[0].name}</div>`;
          });
          return result;
        }
      },
      legend: {
        data: ['筛选项目数'],
        bottom: 10 // 将图例放在底部
      },
      xAxis: [
        {
          type: 'category',
          data: tableData.value.map((val) => val.year),
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          min: 0,
          max: getYAxisMaxValue(tableData.value.map((val) => val.projectCount)),
          axisLabel: {
            formatter: (value) => `${value}`
          },
          // name: '个数', // 左 Y 轴显示的文字
          nameLocation: 'end', // 文字位置
          nameGap: 30 // 文字与坐标轴的间距
        }
      ],
      series: [
        {
          name: '筛选项目数',
          type: 'bar',
          data: tableData.value.map((val) => val.projectCount),
          barWidth: '40%',
          itemStyle: {
            color: '#8080FF'
          },
          label: {
            show: false, // 显示标签
            formatter: (params) => `${params.value}` // 添加百分号
          }
        }
      ]
    };

    option.value && myChart.setOption(option.value);
  };

  nextTick(() => {
    chartInitData();
  });
</script>
<style>
  .exportBtn {
    margin-bottom: 10px;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    cursor: pointer;
    color: #507aff;
  }
</style>
