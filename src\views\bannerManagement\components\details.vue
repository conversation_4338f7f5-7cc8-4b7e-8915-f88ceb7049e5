<template>
  <ele-page
    flex-table
    :multi-card="false"
    hide-footer
    style="min-height: 420px; background-color: #ecf2f9"
  >
    <ele-card flex-table style="margin-top: 8px">
      <el-form
        ref="formRef"
        label-position="right"
        label-width="180px"
        :model="form"
        :rules="rules"
        label-suffix="："
      >
        <el-form-item label="小程序横幅图片" prop="bannerOssId">
          <FileUpload
            v-model="form.bannerOssId"
            :limit="1"
            accept=".jpg, .jpeg, .png"
          />
        </el-form-item>
        <el-form-item label="排序" prop="orderNumber">
          <el-input-number v-model="form.orderNumber" placeholder="排序" />
        </el-form-item>
        <el-form-item label="跳转类型" prop="jumpLinkType">
          <el-select v-model="form.jumpLinkType" placeholder="请选择跳转类型">
            <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.jumpLinkType == '1'"
          label="选择项目"
          prop="projectId"
        >
          <el-select v-model="form.projectId" placeholder="选择项目">
            <el-option
              v-for="item in projectOptions"
              :key="item.projectId"
              :label="item.projectRecruitName"
              :value="item.projectId"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.jumpLinkType == '2'"
          label="移动端内部链接"
          prop="jumpLinkContent"
        >
          <el-input v-model="form.jumpLinkContent" />
        </el-form-item>
        <el-form-item
          v-if="form.jumpLinkType == '3'"
          label="外部链接"
          prop="jumpLinkContent"
        >
          <el-input v-model="form.jumpLinkContent" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="save">保存</el-button>
        </el-form-item>
      </el-form>
    </ele-card>
  </ele-page>
</template>
<script setup>
  import { ElMessage } from 'element-plus';
  import { ref, onMounted, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import FileUpload from '@/components/FileUpload/index.vue';
  import { getBannerDetails, addBanner, editBanner } from '@/api/banner/index';
  import { getAllProjectList } from '@/api/project/index';
  import { usePageTab } from '@/utils/use-page-tab';
  import { useRoute } from 'vue-router';
  const { finishPageTab } = usePageTab();
  const route = useRoute();

  const projectOptions = ref([]);

  const rules = ref({
    bannerOssId: {
      required: true,
      message: '请上传图片',
      trigger: 'change'
    },
    orderNumber: {
      required: true,
      message: '请输入排序',
      trigger: 'change'
    },
    bannerStartTime: {
      required: true,
      message: '请选择发布开始时间',
      trigger: 'change'
    },
    bannerEndTime: {
      required: true,
      message: '请选择发布结束时间',
      trigger: 'change'
    },
    bannerJumpContent: {
      required: false,
      message: '不能为空',
      trigger: 'change'
    },
    projectId: {
      required: true,
      message: '请选择项目',
      trigger: 'change'
    }
  });

  const { form, resetFields, assignFields } = useFormData({
    bannerOssId: null,
    orderNumber: null,
    jumpLinkContent: '',
    jumpLinkType: null,
    projectId: null
  });

  const getDetails = async () => {
    try {
      const res = await getBannerDetails(route.query.id);
      Object.assign(form, { ...res });
      form.projectId = Number(res?.jumpLinkContent);
      const imgWechatObj = {
        url: res?.url || null,
        name: res.fileName,
        fileId: res.bannerOssId
      };
      form.bannerOssId = JSON.stringify(imgWechatObj);
    } catch (err) {
      ElMessage.error(err);
    }
  };

  const formRef = ref(null);
  const save = async () => {
    const params = {
      ...form,
      jumpLinkContent:
        form.jumpLinkType == '1' ? form?.projectId : form?.jumpLinkContent,
      bannerOssId: form.bannerOssId ? JSON.parse(form.bannerOssId).ossId : ''
    };
    formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          await addBanner(params);
          ElMessage.success('操作成功！');
          finishPageTab();
        } catch (err) {
          ElMessage.error(err);
        }
      }
    });
  };
  const typeList = ref([
    {
      label: '不跳转',
      value: 0
    },
    {
      label: '项目详情',
      value: 1
    },
    {
      label: '内部链接',
      value: 2
    },
    {
      label: '外部链接',
      value: 3
    }
  ]);

  const getAllProjectOptions = async () => {
    try {
      const res = await getAllProjectList();
      projectOptions.value = res;
    } catch (e) {
      ElMessage.error(e.message);
    }
  };

  onMounted(() => {
    getAllProjectOptions();
    if (route.query && route.query.id) {
      getDetails();
    }
  });

  watch(
    () => form.bannerJumpType,
    (val) => {
      if (val !== '0') {
        rules.value.bannerJumpContent.required = true;
      } else {
        rules.value.bannerJumpContent.required = false;
      }
    }
  );
</script>
