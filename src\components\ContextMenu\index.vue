<template>
  <div
    class="context-menu-wrapper"
    @contextmenu.prevent="handleContextMenu"
    @click="hideMenu"
  >
    <!-- 插槽内容 -->
    <slot></slot>

    <!-- 右键菜单 -->
    <Teleport to="body">
      <div
        v-if="visible"
        ref="menuRef"
        class="context-menu"
        :style="{
          left: x + 'px',
          top: y + 'px'
        }"
        @click.stop
      >
        <div
          v-for="(item, index) in menuItems"
          :key="index"
          class="context-menu-item"
          :class="{
            'context-menu-item--checked': item.checked,
            'context-menu-item--divider': item.divider,
            'context-menu-item--disabled': item.disabled
          }"
          @click="handleItemClick(item)"
          @mouseenter="handleItemHover(item, true)"
          @mouseleave="handleItemHover(item, false)"
        >
          <template v-if="!item.divider">
            <span class="context-menu-item__label">{{ item.label }}</span>
            <span v-if="item.checked" class="context-menu-item__check">✓</span>
          </template>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
  import { ref, nextTick, onMounted, onBeforeUnmount } from 'vue';

  // Props
  const props = defineProps({
    menuItems: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  });

  // Emits
  const emit = defineEmits(['item-click', 'menu-show', 'menu-hide']);

  // 响应式数据
  const visible = ref(false);
  const x = ref(0);
  const y = ref(0);
  const menuRef = ref(null);

  // 显示菜单
  const showMenu = (event) => {
    if (props.disabled) return;

    x.value = event.clientX;
    y.value = event.clientY;
    visible.value = true;

    emit('menu-show', { x: x.value, y: y.value });

    // 下一帧调整菜单位置，防止超出屏幕
    nextTick(() => {
      adjustMenuPosition();
    });
  };

  // 隐藏菜单
  const hideMenu = () => {
    if (visible.value) {
      visible.value = false;
      emit('menu-hide');
    }
  };

  // 调整菜单位置，防止超出屏幕
  const adjustMenuPosition = () => {
    if (!menuRef.value) return;

    const menu = menuRef.value;
    const menuRect = menu.getBoundingClientRect();
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;

    // 调整水平位置
    if (x.value + menuRect.width > windowWidth) {
      x.value = windowWidth - menuRect.width - 10;
    }

    // 调整垂直位置
    if (y.value + menuRect.height > windowHeight) {
      y.value = windowHeight - menuRect.height - 10;
    }

    // 确保不超出屏幕左上角
    if (x.value < 10) x.value = 10;
    if (y.value < 10) y.value = 10;
  };

  // 处理右键菜单
  const handleContextMenu = (event) => {
    event.preventDefault();
    showMenu(event);
  };

  // 处理菜单项点击
  const handleItemClick = (item) => {
    if (item.disabled || item.divider) return;

    emit('item-click', item);
    hideMenu();
  };

  // 处理菜单项悬停
  const handleItemHover = (item, isEnter) => {
    if (item.disabled || item.divider) return;
    // 可以在这里添加悬停效果的逻辑
  };

  // 全局点击事件处理
  const handleGlobalClick = (event) => {
    if (
      visible.value &&
      menuRef.value &&
      !menuRef.value.contains(event.target)
    ) {
      // hideMenu();
    }
  };

  // 生命周期
  onMounted(() => {
    document.addEventListener('click', handleGlobalClick);
    document.addEventListener('contextmenu', handleGlobalClick);
  });

  onBeforeUnmount(() => {
    document.removeEventListener('click', handleGlobalClick);
    document.removeEventListener('contextmenu', handleGlobalClick);
  });

  // 暴露方法
  defineExpose({
    showMenu,
    hideMenu
  });
</script>

<style scoped>
  .context-menu-wrapper {
    display: contents;
  }

  .context-menu {
    position: fixed;
    z-index: 9999;
    background: white;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 4px 0;
    min-width: 160px;
    font-size: 14px;
    user-select: none;
  }

  .context-menu-item {
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s;
  }

  .context-menu-item:hover:not(.context-menu-item--disabled):not(
      .context-menu-item--divider
    ) {
    background-color: #f5f7fa;
  }

  .context-menu-item--checked {
    background-color: #f0f9ff;
  }

  .context-menu-item--divider {
    height: 1px;
    background: #e4e7ed;
    margin: 4px 0;
    padding: 0;
    cursor: default;
  }

  .context-menu-item--disabled {
    color: #c0c4cc;
    cursor: not-allowed;
  }

  .context-menu-item__label {
    flex: 1;
  }

  .context-menu-item__check {
    color: #409eff;
    font-weight: bold;
    margin-left: 8px;
  }

  /* 重置按钮样式 */
  .context-menu-item:last-child {
    color: #909399;
    font-size: 12px;
    border-top: 1px solid #e4e7ed;
    margin-top: 4px;
    padding-top: 8px;
  }
</style>
