import request from '@/utils/request-business';

/**
 * 查询横幅分页列表
 *  @param {*} data
 *  @returns
 */
export async function getPageList(params) {
  const res = await request.get('/system/banner/list', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 新增横幅信息
 *  @param {*} data
 *  @returns
 */
export async function addBanner(data) {
  const res = await request.post('/system/banner/save', data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 修改横幅信息
 *  @param {*} data
 *  @returns
 */
export async function editBanner(data) {
  const res = await request.put('/system/banner/updateBannerStatus', data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 删除横幅
 *  @param {*} data
 *  @returns
 */
export async function deleteBanner(bannerIds) {
  const res = await request.delete(`/system/banner/${bannerIds}`);
  if (res.data.code === 200) {
    return res.data.msg;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 获取横幅详细信息
 *  @param {*} data
 *  @returns
 */
export async function getBannerDetails(bannerId) {
  const res = await request.get(
    `/system/banner/getBannerDetails?id=${bannerId}`
  );
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(res.data.msg);
}
