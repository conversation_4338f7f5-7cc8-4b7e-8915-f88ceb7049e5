<template>
  <ele-page :flex-table="true" hide-footer>
    <recommender-search ref="searchRef" @search="reload" />
    <ele-card :flex-table="true" :body-style="{ paddingTop: '8px' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="dictCode"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        :export-config="{ fileName: '字典数据' }"
        :footer-style="{ paddingBottom: '16px' }"
        cache-key="systemDictDataTable"
      >
        <template #wechat="{ row }">
          <span class="copy" @click="copyToClipboard(row.wechat)">{{
            row.wechat
          }}</span>
        </template>

        <template #action="{ row }">
          <el-link
            v-if="row.auditStatus == 0"
            type="primary"
            :underline="false"
            @click="showAuditDialog(row)"
          >
            审核
          </el-link>
          <el-link
            v-else
            type="primary"
            :underline="false"
            @click="openEdit(row)"
          >
            编辑
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <AuditModal
      v-model="showEdit"
      :isEdit="isEdit"
      :currnetID="currnetID"
      :currentData="currentData"
      @done="reload"
    />
  </ele-page>
</template>

<script setup name="RecommenderManage">
  import { ref, watch } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import RecommenderSearch from '../components/recommender-search.vue';
  import AuditModal from '../components/AuditModal.vue';

  import { useUserStore } from '@/store/modules/user';

  import { recommendApplyPage } from '@/api/recommender/index';

  const props = defineProps({
    /** 字典类型 */
    dictType: String
  });

  const userStore = useUserStore();

  const currnetID = ref('');

  const currentData = ref({});

  /** 搜索栏实例 */
  const searchRef = ref(null);

  const isEdit = ref(false);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'index',
      columnKey: 'index',
      width: 50,
      align: 'center'
    },
    {
      prop: 'userId',
      label: '用户ID',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'userName',
      label: '用户姓名',
      align: 'center',
      minWidth: 110
    },
    {
      prop: 'phone',
      label: '手机号',
      width: 160,
      align: 'center'
    },
    {
      prop: 'wechat',
      label: '微信号',
      width: 160,
      align: 'center',
      slot: 'wechat'
    },
    {
      prop: 'addWechat',
      label: '已添加微信',
      minWidth: 110,
      align: 'center',
      formatter: (row) => (row.addWechat == 1 ? '是' : '否')
    },
    {
      prop: 'applyTime',
      label: '申请时间',
      minWidth: 110,
      align: 'center'
    },
    {
      prop: 'auditTime',
      label: '审核时间',
      minWidth: 110,
      align: 'center'
    },
    {
      prop: 'auditStatusDesc',
      label: '审核结果',
      minWidth: 110,
      align: 'center'
    },
    {
      prop: 'auditName',
      label: '审核人',
      minWidth: 110,
      align: 'center'
    },
    {
      prop: 'auditRemark',
      label: '审核备注',
      minWidth: 110,
      align: 'left'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 130,
      align: 'center',
      slot: 'action',
      hideInPrint: true,
      hideInExport: true
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ pages, where, orders }) => {
    return recommendApplyPage({
      ...where,
      ...orders,
      ...pages
    });
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  const copyToClipboard = (text) => {
    // 使用 Clipboard API 复制文本
    navigator.clipboard
      .writeText(text)
      .then(() => {
        EleMessage.success('已复制');
      })
      .catch((err) => {
        console.error('复制失败: ', err);
      });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    currnetID.value = row?.id ?? null;
    Object.assign(currentData.value, row);
    isEdit.value = true;
    showEdit.value = true;
  };

  const showAuditDialog = (row) => {
    currnetID.value = row?.id ?? null;
    isEdit.value = false;
    showEdit.value = true;
  };

  // 监听字典id变化
  // watch(
  //   () => props.dictType,
  //   () => {
  //     searchRef.value?.resetFields?.();
  //     reload({});
  //   }
  // );
</script>
<style scoped lang="scss">
  .copy {
    border: none;
    cursor: pointer;
    text-decoration: underline;
  }
</style>
