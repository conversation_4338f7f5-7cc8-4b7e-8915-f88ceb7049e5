import request from '@/utils/request-business';
import { download, toFormData, checkDownloadRes } from '@/utils/common';

/**
 * 查询招募中心下拉选择列表
 */
export async function getSiteSelectList(params) {
  const res = await request.get('/siteInfo/getSiteSelectList', { params });
  if (res.data.code === 200 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询当前组织创建的招募项目
 */
export async function getProjectJoinProgress(params) {
  const res = await request.get('/projectApply/getProjectJoinProgress', {
    params
  });
  if (res.data.code === 200 && res.data.data) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 提交项目报名申请
 * @param {*} data
 * @returns
 */
export async function submitProjectApply(data) {
  const res = await request.post(`/projectApply/submitProjectApply`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 查询健康项目的报名列表
 * @param {*} data
 * @returns
 */
export async function getHealthProjectApplyPageList(params) {
  const res = await request.get('/projectApply/getHealthProjectApplyPageList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查询患病项目的报名列表
 * @param {*} data
 * @returns
 */
export async function getDiseaseProjectApplyPageList(params) {
  const res = await request.get(
    '/projectApply/getDiseaseProjectApplyPageList',
    {
      params
    }
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 修改报名人报名状态
 * @param {*} data
 * @returns
 */
export async function updateUserApplyStatus(data) {
  const res = await request.post(`/projectApply/updateUserApplyStatus`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 更正项目报名状态
 * @param {*} data
 * @returns
 */
export async function correctionApplyStatus(data) {
  const res = await request.post(`/projectApply/correctionApplyStatus`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 获取招募项目的开展中心列表
 * @param {*} data
 * @returns
 */
export async function getProjectSiteList(params) {
  const res = await request.get('/projectRcruit/getProjectSiteList', {
    params
  });
  if (res.data.code === 200 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 获取各状态数量数据
 * @param {*} data
 * @returns
 */
export async function getStateNumber(params) {
  const res = await request.get(
    `/projectApply/getStateNumber?siteId=${params.siteId}&projectId=${params.projectId}`
  );
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 修改报名人参与中心
 * @param {*} data
 * @returns
 */
export async function updateUserJoinSite(data) {
  const res = await request.post(`/projectApply/updateUserJoinSite`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 设置计划入组人数和报名人数
 * @param {*} data
 * @returns
 */
export async function setPlanJoinGroupAndApplyNumber(data) {
  const res = await request.post(
    `/projectRcruit/setPlanJoinGroupAndApplyNumber`,
    data
  );
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 导出健康项目报名列表
 */
export async function exportHealthProjectApply(params) {
  const res = await request({
    url: '/projectApply/exportHealthProjectApply',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `${params.projectRecruitName}—联系表.xlsx`);
}

/**
 * 导出问卷内容
 */
export async function exportQuestionnaireContent(params) {
  const res = await request({
    url: '/projectApply/exportQuestionnaireContent',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `job_${Date.now()}.xlsx`);
}

/**
 * 导出患者项目报名列表
 */
export async function exportDiseaseProjectApplyPageList(params) {
  const res = await request({
    url: '/projectApply/exportDiseaseProjectApplyPageList',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `${params.projectRecruitName}—联系表.xlsx`);
}

/**
 * 下载病例
 */
export async function downloadUserMedicalFile(params, userName) {
  const res = await request({
    url: '/userInfo/downloadUserMedicalFile',
    method: 'POST',
    data: toFormData(params),
    responseType: 'blob'
  });
  await checkDownloadRes(res);
  download(res.data, `${userName}-病例.pdf`);
}
