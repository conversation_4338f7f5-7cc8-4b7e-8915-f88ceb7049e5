<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1150"
    :model-value="modelValue"
    :title="`已完成筛选（${tableData.length}）`"
    @update:modelValue="updateModelValue"
  >
    <div class="modal-dialog">
      <el-table
        :data="tableData"
        height="300px"
        class="table-fixed"
        :header-cell-style="{
          backgroundColor: '#f3f6ff',
          color: '#131314'
        }"
        :row-style="tableRowStyle"
      >
        <template #empty>
          <el-empty description="暂无数据" />
        </template>
        <el-table-column type="index" label="序号" fixed="left" width="65" />
        <el-table-column
          prop="name"
          label="姓名"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="acronym"
          label="姓名缩写"
          width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <span class="ellipsis" v-if="!row.edit">{{ row.acronym }}</span>
              <el-input v-model="row.acronym" style="width: 100%" v-else />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="filterno"
          label="筛选号"
          width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <span class="ellipsis" v-if="!row.edit">{{ row.filterno }}</span>
              <el-input v-model="row.filterno" style="width: 100%" v-else />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="telephone"
          label="联系电话"
          width="160"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <span class="ellipsis" v-if="!row.edit">{{
                row.showPhoneEye ? row.telephone : getPhone(row.telephone)
              }}</span>
              <img
                :src="row.showPhoneEye ? eyeOpen : eyeClose"
                style="
                  transform: scale(1.28);
                  cursor: pointer;
                  margin: 0 5px;
                  width: 14px;
                  height: 14px;
                "
                @click="row.showPhoneEye = !row.showPhoneEye"
                v-if="!row.edit"
              />

              <el-input
                v-model="row.telephone"
                style="width: 100%"
                v-if="row.edit"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="idcard"
          label="身份证号"
          width="220"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <span class="ellipsis">{{ row.idcard }}</span>
              <img
                :src="row.showIdcardEye ? eyeOpen : eyeClose"
                style="
                  transform: scale(1.28);
                  cursor: pointer;
                  margin: 0 5px;
                  width: 14px;
                  height: 14px;
                "
                @click="getShowEye(row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="similaritytype"
          label="核对相似度"
          width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span v-if="row.similaritytype == 3">低相似度</span>
            <span v-if="row.similaritytype == 2">中相似度</span>
            <span v-if="row.similaritytype == 1">高相似度</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="筛选结果"
          width="180"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div>{{
              row.status == 0
                ? '筛选不合格'
                : row.status == 1
                  ? '筛选合格'
                  : row.status == 7
                    ? '入组前筛选合格'
                    : row.status == 8
                      ? '入组前筛选不合格'
                      : ''
            }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="sysremarks"
          label="筛选备注"
          width="400"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div
              style="
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ row.sysremarks }}</div
            >
          </template>
        </el-table-column>

        <el-table-column label="操作" width="140" fixed="right">
          <template #default="{ row }">
            <el-space>
              <el-link type="primary" @click="row.edit = true" v-if="!row.edit"
                >编辑</el-link
              >
              <el-link type="primary" @click="save(row)" v-if="row.edit"
                >保存</el-link
              >
              <!--<el-link
                type="primary"
                @click="greenLight(row)"
                v-if="
                  (row.status == 0 || row.status == 8) &&
                  (row.sysremarks || '').includes('上家机构建议')
                "
                >放行</el-link
              >-->
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <!-- <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
      </div> -->
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref } from 'vue';
  import {
    release,
    filterUpdate,
    getCompleteInfo
  } from '@/api/subjects/studyManage/index.js';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { ElMessageBox } from 'element-plus';
  import { Icon } from '@iconify/vue';
  import eyeOpen from '@/assets/subjects/eyeOpen.png';
  import eyeClose from '@/assets/subjects/eyeClose.png';
  const tableRowStyle = (row) => {
    return row.rowIndex % 2 === 0
      ? { background: '#ffffff' }
      : { background: '#ffffff' };
  };
  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Array
  });
  const greenLight = (row) => {
    ElMessageBox.confirm(row.sysremarks, '初查不合格，确定要进行放行操作吗？', {
      confirmButtonText: '是',
      cancelButtonText: '否',
      center: true
    }).then(() => {
      release(row).then((res) => {
        if (res.code == 200) {
          row.status = 1;
          row.filterResult = '合格';
          EleMessage.success('操作成功');
          return;
        }
        EleMessage.error(res.msg);
      });
    });
  };
  const save = (row) => {
    const data = {
      guid: row.guid,
      subjectsGuid: row.subjectsGuid,
      filterno: row.filterno,
      acronym: row.acronym,
      telephone: row.telephone
    };
    filterUpdate(data).then((res) => {
      if (res.code == 200) {
        row.edit = false;
        EleMessage.success('保存成功');
        emit('done', tableData.value);
        return;
      }
      EleMessage.error(res.msg);
    });
  };
  const close = () => {
    emit('done', tableData.value);
    updateModelValue(false);
  };

  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const tableData = ref([]);
  nextTick(async () => {
    tableData.value = JSON.parse(JSON.stringify(props.data));
  });
  const getPhone = (text) => {
    try {
      if (text.length === 11) {
        return text.replace(/(\d{3})\d+(\d{4})/, '$1****$2');
      } else {
        let numLength = text.replace(/\D/g, '').length;
        let regex = new RegExp(`^(\\d{1})(\\d{${numLength - 2}})(\\d{1})$`);
        return text.replace(regex, '$1' + '*'.repeat(numLength - 2) + '$3');
      }
    } catch (error) {
      return '';
    }
  };

  const getShowEye = (row) => {
    row.showIdcardEye = !row.showIdcardEye;
    getCompleteInfo({ guid: row.subjectsGuid, type: '1' }).then((res) => {
      row.idcard = res.msg;
      row.idcard = getCard(row);
    });
  };
  const getCard = (row) => {
    try {
      if (row.showIdcardEye) {
        return row.idcard;
      }
      return row.idcard.replace(/(\d{6})\d+(\d{4})/, '$1********$2');
    } catch (error) {
      console.log(error);
      return row.idcard;
    }
  };
</script>
<style>
  .ellipsis {
    width: 100%;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
