<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1350"
    :model-value="modelValue"
    close-on-click-modal
    title="详情"
    @update:modelValue="updateModelValue"
    style="margin-top: 85px"
    align-center
  >
    <div v-if="loading">
      <ele-loading :loading="loading" text="加载中" background="red">
        <div style="width: 100%; height: 200px; background: #ffffff"></div>
      </ele-loading>
    </div>
    <div class="modal-dialog descriptions_dialog" v-else>
      <el-descriptions border label-width="187">
        <template #title>
          <div>筛选结果 </div>
        </template>

        <el-descriptions-item
          label=" 筛选状态"
          label-align="right"
          :span="3"
          width="208"
        >
          <span style="color: #c72727" v-if="info.status == 0">初查不合格</span>
          <span style="color: #67c32a" v-if="info.status == 1">筛选</span>
          <span style="color: #67c32a" v-if="info.status == 2">入组</span>
          <span style="color: #3ed47b" v-if="info.status == 3">完成</span>
          <span style="color: #999999" v-if="info.status == 4">出组</span>
          <span style="color: #999999" v-if="info.status == 5">未入组</span>
          <span style="color: #999999" v-if="info.status == 6">入组未给药</span>
          <span style="color: #67c32a" v-if="info.status == 7">待入组</span>
          <span style="color: #c72727" v-if="info.status == 8"
            >入组前筛查不合格
          </span>
        </el-descriptions-item>
        <el-descriptions-item
          label="筛选证照核对相似度"
          label-align="right"
          :span="3"
          width="208"
        >
          <span v-if="info.filterSimilarity || info.filterSimilarity == 0"
            >{{ info.filterSimilarity }}%，{{ info.similaritytype == 1 ? '高' : info.similaritytype == 2 ? '中' : info.similaritytype == 3 ? '低' : ''}}</span
          >
        </el-descriptions-item>
        <el-descriptions-item
          label="入组前证照核对相似度"
          label-align="right"
          :span="3"
          width="208"
          v-if="info.status == 2 || info.status == 4 || info.status == 5 || info.status == 6 || info.status == 7 || info.status == 8"
        >
          <span v-if="info.joinSimilarity || info.joinSimilarity == 0"
            >{{ info.joinSimilarity }}%，{{ info.similaritytype2 == 1 ? '高' : info.similaritytype2 == 2 ? '中' : info.similaritytype2 == 3 ? '低' : ''}}</span
          >
        </el-descriptions-item>
        <el-descriptions-item
          label="筛查备注"
          label-align="right"
          :span="3"
          width="208"
          v-if="info.sysremarks"
        >
          <span>{{ info.sysremarks }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label="照片信息"
          label-align="right"
          :span="3"
          width="208"
        >
          <div
            style="
              display: flex;
              flex-direction: column;
              width: 440px;
              padding: 10px 0;
            "
          >
            <div style="display: flex; gap: 25px">
              <div style="text-align: center">
                <div style="margin-bottom: 5px">身份证照片</div>
                <el-image
                  v-if="showImages"
                  style="width: 120px; height: 120px"
                  :src="info.image1"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="[info.image1]"
                  show-progress
                  :initial-index="4"
                  fit="cover"
                />
              </div>
              <div style="text-align: center">
                <div style="margin-bottom: 5px">筛选照片</div>
                <el-image
                  v-if="showImages && info.image2"
                  style="width: 120px; height: 120px"
                  :src="info.image2"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="[info.image2]"
                  show-progress
                  :initial-index="4"
                  fit="cover"
                />
              </div>
              <div
                style="text-align: center"
                v-if="info.status == 2 || info.status == 4 || info.status == 5 || info.status == 6 || info.status == 7 || info.status == 8"
              >
                <div v-if="info.image3" style="margin-bottom: 5px">入组前筛选照片</div>
                <el-image
                  v-if="showImages && info.image3"
                  style="width: 120px; height: 120px"
                  :src="info.image3"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="[info.image3]"
                  show-progress
                  :initial-index="4"
                  fit="cover"
                />
              </div>
            </div>
            <div style="display: flex; justify-content: flex-start">
              <el-button
                style="margin-top: 20px; margin-left: 40px"
                v-if="!showImages"
                @click="toggleImage"
                type="text"
              >
                点击查看
              </el-button>
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions border label-width="140">
        <template #title>
          <div style="margin-top: 30px">志愿者身份信息 </div>
        </template>

        <el-descriptions-item label="身份证号" label-align="right" width="170">
          <div
            style="
              width: 100%;
              position: relative;
              display: flex;
              align-items: center;
            "
          >
            <span class="numberStyle" style="flex: 1; overflow: hidden">{{
              $PhoneOrIdCrad(
                info.isShow ? info.showIdcard : info.idcard,
                'idCard'
              )
            }}</span>
            <img
              :src="info.isShow ? eyeOpen : eyeClose"
              style="
                transform: scale(1.28);
                cursor: pointer;
                margin: 0 5px;
                width: 14px;
                height: 14px;
              "
              @click="getIdcard"
            />
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="姓名" label-align="right" width="170">
          {{ info.name }}
        </el-descriptions-item>
        <el-descriptions-item
          label="姓名缩写"
          :span="1"
          label-align="right"
          width="170"
        >
          {{ info.acronym }}
        </el-descriptions-item>

        <el-descriptions-item label="民族" label-align="right" width="170">
          {{ info.nation }}
        </el-descriptions-item>
        <el-descriptions-item label="性别" label-align="right">
          {{ info.sex }}
        </el-descriptions-item>
        <el-descriptions-item label="年龄" label-align="right">
          <span class="numberStyle">{{ info.age }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="出生年月" label-align="right">
          <span class="numberStyle">{{ info.birthday }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系电话" label-align="right">
          <div
            style="
              width: 100%;
              position: relative;
              display: flex;
              align-items: center;
            "
            v-if="info.telephone"
          >
            <span class="numberStyle" style="overflow: hidden; width: 130px">{{
              $PhoneOrIdCrad(
                info.telephoneShow
                  ? info.telephone
                  : info.telephoneDesensitization,
                'phone'
              )
            }}</span>
            <img
              :src="info.telephoneShow ? eyeOpen : eyeClose"
              style="
                transform: scale(1.28);
                cursor: pointer;
                margin: 0 5px;
                width: 14px;
                height: 14px;
              "
              @click="info.telephoneShow = !info.telephoneShow"
            />
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="紧急电话" label-align="right">
          <div
            style="
              width: 100%;
              position: relative;
              display: flex;
              align-items: center;
            "
            v-if="info.telephone2"
          >
            <span class="numberStyle" style="overflow: hidden; width: 130px">{{
              $PhoneOrIdCrad(
                info.telephone2Show
                  ? info.telephone2
                  : info.telephone2Desensitization,
                'phone'
              )
            }}</span>
            <img
              :src="info.telephone2Show ? eyeOpen : eyeClose"
              style="
                transform: scale(1.28);
                cursor: pointer;
                margin: 0 5px;
                width: 14px;
                height: 14px;
              "
              @click="info.telephone2Show = !info.telephone2Show"
            />
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="地址" label-align="right" :span="3">
          {{ info.address }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions
        label-width="140"
        width="150"
        border
        v-if="data.status == 2 || data.status == 4 || data.status == 5 || data.status == 6 || info.status == 7 || info.status == 8"
      >
        <template #title>
          <div style="margin-top: 30px">志愿者试验信息 </div>
        </template>

        <el-descriptions-item
          label="筛选号"
          :span="1"
          label-align="right"
          width="170"
        >
          <span class="numberStyle"> {{ info.filterno }} </span>
        </el-descriptions-item>
        <el-descriptions-item
          label="筛选时间"
          :span="1"
          label-align="right"
          width="170"
        >
          <span class="numberStyle">{{ info.filterdate }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label="入组前筛选时间"
          :span="1"
          label-align="right"
          width="170"
        >
          <span class="numberStyle">{{ info.joinfilterdate }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="入组号" :span="1" label-align="right">
          <span class="numberStyle"> {{ info.joinno }} </span>
        </el-descriptions-item>
        <el-descriptions-item label="入组时间" :span="1" label-align="right">
          <span class="numberStyle">{{ info.joindate }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label="末次给药时间"
          :span="1"
          label-align="right"
        >
          <span class="numberStyle">{{ info.lastdosedate }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label="末次访视时间"
          :span="1"
          label-align="right"
        >
          <span class="numberStyle">{{ info.trialedate }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="依从性评价" :span="1" label-align="right">
          {{ info.evaluate }}
        </el-descriptions-item>
        <el-descriptions-item
          label="下次试验间隔天数"
          :span="1"
          label-align="right"
        >
          <span class="numberStyle">{{ info.interval }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="3" label-align="right">
          {{ info.remarks }}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="筛查备注" :span="3" label-align="right">
          {{ info.sysremarks }}
        </el-descriptions-item> -->
      </el-descriptions>
      <el-descriptions label-width="170" border v-else>
        <template #title>
          <div style="margin-top: 30px">志愿者试验信息 </div>
        </template>

        <el-descriptions-item
          label="筛选号"
          label-align="right"
          :span="1"
          width="208"
        >
          <span class="numberStyle"> {{ info.filterno }} </span>
        </el-descriptions-item>
        <el-descriptions-item
          label="筛选时间"
          label-align="right"
          width="208"
        >
          <span class="numberStyle">{{ info.filterdate }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label=""
          label-align="right"
          width="208"
        >
          <span class="numberStyle"></span>
        </el-descriptions-item>
        <el-descriptions-item
          label="备注"
          label-align="right"
          :span="3"
          width="208"
        >
          <span>{{ info.remarks }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref } from 'vue';
  import {
    subjecttrialInfo,
    getCompleteInfo,
    pictureData
  } from '@/api/subjects/studyManage/index.js';
  import { Icon } from '@iconify/vue';
  import eyeOpen from '@/assets/subjects/eyeOpen.png';
  import eyeClose from '@/assets/subjects/eyeClose.png';
  const ossBaseUrl = import.meta.env.VITE_APP_BASE_OSS;
  const getIdcard = () => {
    if (info.value.showIdcard) {
      info.value.isShow = !info.value.isShow;
      return;
    }
    getCompleteInfo({ guid: info.value.subjectsGuid, type: '1' }).then(
      (res) => {
        info.value.showIdcard = res.msg;
        info.value.isShow = !info.value.isShow;
      }
    );
  };
  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object
  });
  const loading = ref(true);
  const close = () => {
    updateModelValue(false);
  };

  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const info = ref({});
  nextTick(async () => {
    console.log(props.data);
    subjecttrialInfo({ guid: props.data.guid }).then((res) => {
      info.value = res.data || {};
      loading.value = false;
    });
  });
  const showImages = ref(false); // 控制所有图片的显示状态
  const toggleImage = () => {
    pictureData({ guid: props.data.guid }).then((res) => {
      if(res.data.image1.includes('http')){
        info.value.image1 = res.data.image1;
        info.value.image2 = res.data.image2;
        info.value.image3 = res.data.image3;
      } else {
        if (res.data.image1) {
          info.value.image1 = ossBaseUrl + res.data.image1;
        }
        if (res.data.image2) {
          info.value.image2 = ossBaseUrl + res.data.image2;
        }
        if (res.data.image3) {
          info.value.image3 = ossBaseUrl + res.data.image3;
        } 
      }      
      showImages.value = !showImages.value;
    });
  };
</script>
<style>
  .descriptions_dialog {
    max-height: 600px;
    overflow-y: auto;
  }
  .descriptions_dialog .el-descriptions__title {
    width: 100%;
  }
  .descriptions_dialog
    .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell {
    padding: 8px 5px !important;
  }
</style>
