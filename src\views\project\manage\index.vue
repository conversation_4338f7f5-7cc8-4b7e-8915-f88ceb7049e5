<template>
  <ele-page
    flex-table
    :multi-card="false"
    hide-footer
    style="min-height: 420px; background-color: #ecf2f9"
  >
    <div
      style="
        height: 42px;
        position: relative;
        background: #ffffff;
        margin-bottom: 10px;
        padding: 10px 20px 0 20px;
        border-radius: 6px;
      "
    >
      <el-tabs v-model="currentStatus" @tab-click="currentStatusChange">
        <el-tab-pane label="筛查项目" name="shaicha" />
        <el-tab-pane label="招募项目" name="zhaomu" />
      </el-tabs>
    </div>
    <ele-card
      v-show="currentStatus == 'zhaomu'"
      flex-table
      :body-style="{ padding: '20px', overflow: 'hidden' }"
    >
      <div class="d-flex">
        <div style="display: flex; gap: 10px">
          <el-button
            type="primary"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="addProject()"
          >
            发布项目
          </el-button>
          <el-button
            v-permission="'project:screen:import'"
            class="ele-btn-icon"
            :icon="UploadOutlined"
            @click="openImport"
          >
            项目导入
          </el-button>
        </div>
        <div class="mt-4">
          <el-input
            v-model="keywords"
            placeholder="请输入招募名称"
            style="width: 188px"
          >
            <template #suffix>
              <el-icon @click="search()"><SearchOutlined /></el-icon>
            </template>
          </el-input>

          <el-select
            v-model="recruitType"
            placeholder="招募类型"
            clearable
            style="width: 122px; margin-left: 12px"
          >
            <el-option label="健康项目" :value="1" />
            <el-option label="患者项目" :value="2" />
          </el-select>
          <el-select
            v-model="auditFlag"
            placeholder="报名审核"
            clearable
            style="width: 122px; margin-left: 12px"
          >
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </div>
      </div>
      <!-- Tabs 组件 -->
      <el-tabs v-model="activeTab" class="flex-table-tabs">
        <el-tab-pane label="全部项目" name="tab1">
          <CommonTableList
            v-if="activeTab == 'tab1'"
            ref="commonTableList"
            :model="1"
            :keywords="keywords"
            :recruitType="recruitType"
            :auditFlag="auditFlag"
          />
        </el-tab-pane>
        <el-tab-pane label="招募中" name="tab2">
          <OtherTableList
            v-if="activeTab == 'tab2'"
            :model="2"
            :keywords="keywords"
            :recruitType="recruitType"
            :auditFlag="auditFlag"
          />
        </el-tab-pane>
        <el-tab-pane label="已关闭" name="tab4">
          <OtherTableList
            v-if="activeTab == 'tab4'"
            :model="4"
            :keywords="keywords"
            :recruitType="recruitType"
            :auditFlag="auditFlag"
          />
        </el-tab-pane>
        <el-tab-pane label="审核失败" name="tab5">
          <OtherTableList
            v-if="activeTab == 'tab5'"
            :model="5"
            :keywords="keywords"
            :recruitType="recruitType"
            :auditFlag="auditFlag"
          />
        </el-tab-pane>
        <el-tab-pane label="草稿" name="tab3">
          <OtherTableList
            v-if="activeTab == 'tab3'"
            :model="3"
            :keywords="keywords"
            :recruitType="recruitType"
            :auditFlag="auditFlag"
          />
        </el-tab-pane>
      </el-tabs>
    </ele-card>
    <!-- <ele-card
      flex-table
      :body-style="{ padding: '0 16px', overflow: 'hidden' }"
      style="margin-top: 8px"
      v-if="currentStatus == 'zhaomu'"
    >
    </ele-card> -->
    <div v-if="currentStatus == 'shaicha'">
      <ele-card flex-table :body-style="{ padding: '0px', overflow: 'hidden' }">
        <testProject height="72px" />
      </ele-card>
    </div>
    <!-- 项目导入弹窗 -->
    <project-import v-model="showImport" @done="done" />
  </ele-page>
</template>

<script setup name="zhaomuandsaicha">
  import { ref, nextTick, watch } from 'vue';
  import { EleMessage, toTree } from '@hnjing/zxzy-admin-plus/es';
  import { SearchOutlined, UploadOutlined } from '@/components/icons';
  import { Search } from '@element-plus/icons-vue';
  import { useMobile } from '@/utils/use-mobile';
  // import UserList from './components/user-list.vue';
  import { listDepts } from '@/api/system/dept';
  import CommonTableList from '@/views/project/components/common-table-list.vue';
  import OtherTableList from '@/views/project/components/other-table-list.vue';
  import { useRouter, useRoute } from 'vue-router';
  import testProject from '@/views/subjects/testProject/index.vue';
  import ProjectImport from '../components/project-import.vue';

  const router = useRouter();
  const route = useRoute();
  defineOptions({ name: 'SystemUser' });
  const currentStatus = ref('shaicha');
  /** 是否是移动端 */
  const { mobile } = useMobile();

  /** 分割面板组件 */
  const splitRef = ref(null);

  const commonTableList = ref(null);

  /** 是否显示项目导入弹窗 */
  const showImport = ref(false);

  /** 树组件 */
  const treeRef = ref(null);

  /** 加载状态 */
  const loading = ref(true);

  /** 树形数据 */
  const data = ref([]);

  /** 选中数据 */
  const current = ref(null);

  /** 部门搜索关键字 */
  const keywords = ref('');

  const recruitType = ref('');

  const auditFlag = ref(null);

  const activeTab = ref('tab1');

  const options = [
    {
      value: 1,
      label: '健康项目'
    },
    {
      value: 2,
      label: '患者项目'
    }
  ];

  const openImport = () => {
    showImport.value = true;
  };

  /**
   * 发布项目
   */
  const addProject = () => {
    const path = '/recruit/add-project';
    router.push({
      path
    });
  };

  const done = () => {
    commonTableList.value?.reload();
  };

  /**搜索 */
  const search = () => {
    console.log('#', keywords.value);
  };
  const currentStatusChange = (type) => {
    currentStatus.value = type.props.name;
  };
  nextTick(() => {
    if (route.query.currentStatus) {
      currentStatus.value = route.query.currentStatus;
    }
  });
  watch(
    () => route.query,
    () => {
      console.log('值变化了-----');
      // initData();
      if (route.query.currentStatus) {
        currentStatus.value = route.query.currentStatus;
      }
    },
    { deep: true }
  );
</script>

<style lang="scss" scoped>
  .flex-table-tabs {
    display: flex;
    flex-direction: column-reverse;
    flex: 1;
    overflow: auto;
    min-height: auto !important;
    :deep(.ele-pro-table-footer) {
      padding-bottom: 0 !important;
    }
  }
  .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .custom-input {
    background-color: #f0f0f0; /* 设置底色 */
    border-radius: 4px; /* 可选：设置圆角 */
  }

  :deep(.custom-input .el-input-group__prepend) {
    background-color: var(--el-fill-color-blank);
  }

  :deep(.el-tabs__header) {
    margin: 2px;
  }
</style>
