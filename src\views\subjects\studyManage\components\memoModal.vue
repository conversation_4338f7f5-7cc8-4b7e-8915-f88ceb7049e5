<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="500"
    :model-value="modelValue"
    title="备忘录"
    @update:modelValue="updateModelValue"
  >
    <div>
      <el-form
        :model="userInfo"
        :rules="rules"
        ref="formRule"
        label-width="auto"
        label-position="left"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="姓名" style="width: 100%">
              <el-input
                v-model="userInfo.name"
                placeholder="请输入姓名"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="userInfo.filterno">
            <el-form-item label="筛选号" style="width: 100%">
              <el-input
                v-model="userInfo.filterno"
                placeholder="请输入筛选号"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="userInfo.joinno">
            <el-form-item label="入组号" style="width: 100%">
              <el-input
                v-model="userInfo.joinno"
                placeholder="请输入入组号"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="latestData">
            <el-form-item
              label="有效期至"
              prop="indate"
              style="width: 100%"
              class="formRequired"
            >
              <el-date-picker
                v-model="userInfo.indate"
                type="date"
                placeholder="请选择有效期"
                clearable
                style="width: 100%"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="!latestData">
            <el-form-item
              label="有效期（月）"
              prop="indateType"
              style="width: 100%"
              class="formRequired"
            >
              <el-select
                v-model="userInfo.indateType"
                placeholder="请选择有效期"
                clearable
                style="width: 100%"
              >
                <el-option label="3个月" value="9" />
                <el-option label="6个月" value="8" />
                <el-option label="9个月" value="7" />
                <el-option label="12个月" value="6" />
                <el-option label="24个月" value="5" />
                <el-option label="36个月" value="4" />
                <el-option label="48个月" value="3" />
                <el-option label="60个月" value="2" />
                <el-option label="永久" value="1" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="是否公开"
              prop="ispublic"
              style="width: 100%"
              class="formRequired"
            >
              <el-select
                v-model="userInfo.ispublic"
                placeholder="请选择是否公开"
                clearable
                style="width: 100%"
              >
                <el-option label="否" :value="0" />
                <el-option label="是" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="备忘录信息"
              prop="memo"
              style="width: 100%"
              class="formRequired"
            >
              <el-input
                v-model="userInfo.memo"
                type="textarea"
                :autosize="{
                  minRows: 3,
                  maxRows: 6
                }"
                placeholder="请输入备忘录信息"
                clearable
                style="width: 100%"
                show-word-limit
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit"
          >确 定</el-button
        >
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import {
    setupMemo,
    memoLatest,
    memoUpdate
  } from '@/api/subjects/studyManage/index.js';
  const disabledDate = (time) => {
    const maxDate = new Date('9999-12-31');
    return time.getTime() < Date.now() || time.getTime() > maxDate.getTime(); // - 8.64e7是今天可以选
  };
  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object
  });
  const rules = ref({
    indate: [
      { required: true, message: '请选择有效期限', trigger: ['blur', 'change'] }
    ],
    indateType: [
      { required: true, message: '请选择有效期限', trigger: ['blur', 'change'] }
    ],
    ispublic: [
      { required: true, message: '请选择是否公开', trigger: ['blur', 'change'] }
    ],
    memo: [
      {
        required: true,
        message: '请输入备忘录信息',
        trigger: ['blur', 'change']
      }
    ]
  });
  const loading = ref(false);
  const userInfo = ref({});
  const close = () => {
    updateModelValue(false);
  };
  const formRule = ref(null);

  const submit = () => {
    formRule.value.validate((valid) => {
      if (valid) {
        loading.value = true;
        const data = {
          trialsGuid: userInfo.value.trialsGuid,
          subjectsGuid: userInfo.value.subjectsGuid,
          indateType: userInfo.value.indateType,
          ispublic: userInfo.value.ispublic,
          memo: userInfo.value.memo
        };
        const api = latestData.value ? memoUpdate : setupMemo;
        if (latestData.value) {
          data.guid = userInfo.value.guid;
          data.ispublic = userInfo.value.ispublic;
          data.indate = userInfo.value.indate;
          data.message = userInfo.value.memo;
        }
        api(data).then((res) => {
          loading.value = false;
          if (res.code == 200) {
            EleMessage.success('保存成功');
            emit('done');
            updateModelValue(false);
            return;
          }
          EleMessage.error(res.msg);
        });
        return;
      }
    });
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const latestData = ref(true);

  const getMemoLatest = () => {
    const data = {
      trialsGuid: userInfo.value.trialsGuid,
      subjectsGuid: userInfo.value.subjectsGuid
    };
    memoLatest(data).then((res) => {
      if (res.data) {
        latestData.value = true;
        userInfo.value.memo = res.data.message;
        userInfo.value.indate = res.data.indate;
        userInfo.value.guid = res.data.guid;
        userInfo.value.ispublic = res.data.ispublic;

        return;
      }
      latestData.value = false;
    });
  };
  nextTick(async () => {
    userInfo.value = JSON.parse(JSON.stringify(props.data));
    getMemoLatest();
  });
</script>
<style scoped>
  :deep(.formRequired .el-form-item__label) {
    transform: translateX(-10px);
  }
</style>
