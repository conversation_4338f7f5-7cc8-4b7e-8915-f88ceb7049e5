# 开发环境接口地址
 # 页面标题
# VITE_APP_TITLE = 临床研究协调员(CRC)联网管理系统

VITE_APP_PRODUCT = false

# 开发环境配置
NODE_ENV='development'

# 智摹通系统 /开发环境

# 打包地址
# VITE_APP_BASE_API = '/dev-api/system'
# VITE_APP_BASE_API_URL = 'http://192.168.0.245:8080'

# 本地运行地址
VITE_APP_BASE_API = 'https://recruit-dev.frp.520gcp.com/admin-api'
# VITE_APP_BASE_API = 'http://192.168.0.95:8080'
VITE_APP_BASE_API_URL = 'https://recruit-dev.frp.520gcp.com/admin-api'
# 志愿者数据库筛查相关地址
VITE_APP_SUBJECTS_BASE_API = 'https://req.frp.520gcp.com/subjects/api'
# VITE_APP_SUBJECTS_BASE_API = 'http://192.168.0.152:8991/api'
VITE_APP_SUBJECTS_BASE_API2 ='https://req.frp.520gcp.com/subjects-logger'
# VITE_APP_BASE_API_URL = 'http://192.168.0.94:8082'

# business相关接口
VITE_APP_BUSINESS_BASE_API = '/business-api'
VITE_APP_BUSINESS_BASE_API_URL = 'http://recruit-dev.frp.520gcp.com/business-api'
VITE_APP_BASE_OSS = 'https://iscreen-test.gcpdata.cn'

# sse 开关
VITE_APP_SSE = true
