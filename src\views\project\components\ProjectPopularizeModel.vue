<!-- 修改密码弹窗 -->
<template>
  <ele-modal
    form
    :width="600"
    title="推广项目"
    :append-to-body="true"
    v-model="visible"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="90px"
      label-position="left"
      @submit.prevent=""
    >
      <el-form-item label="推广性别" prop="sex">
        <el-select v-model="form.sex" placeholder="请选择推广性别">
          <el-option
            v-for="item in typeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="推广年龄" prop="startAge">
        <el-row :gutter="20">
          <el-col :span="11">
            <el-input
              v-model="form.startAge"
              type="number"
              placeholder="年龄下限（包含）"
            />
          </el-col>
          <el-col :span="2" style="font-size: 24px">~</el-col>
          <el-col :span="11">
            <el-input
              v-model="form.endAge"
              type="number"
              placeholder="年龄上限（包含）"
            />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="推广区域" prop="areas">
        <el-input
          v-model="form.areas"
          :maxlength="100"
          placeholder="请输入推广区域"
        />
      </el-form-item>
      <el-form-item label="间隔天数" prop="intervalDays">
        <el-input
          v-model="form.intervalDays"
          type="number"
          placeholder="请输入间隔天数"
        />
      </el-form-item>
      <el-form-item label="年份范围" prop="yearRange">
        <el-select v-model="form.yearRange" placeholder="请选择年份范围">
          <el-option
            v-for="item in yearList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div
      ><el-button type="primary" :loading="loading" @click="search">
        查询
      </el-button>
      <span style="margin-left: 24px"
        >本次推广预计发送短信：<span
          v-if="promotionNumber"
          style="font-size: 18px; color: red"
          >{{ promotionNumber }}</span
        >人</span
      >
    </div>

    <template #footer>
      <el-button
        type="primary"
        :loading="otherLoading"
        @click="handleOk"
        :disabled="!promotionNumber"
      >
        确认推荐
      </el-button>
      <el-button @click="handleCancel">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { updatePassword } from '@/api/layout';

  import { projectPromotion, confirmPromotion } from '@/api/project/index';

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交loading */
  const loading = ref(false);

  const otherLoading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  const planId = ref(null);

  const promotionNumber = ref(null);

  const props = defineProps({
    modelValue: Boolean,
    projectId: String
  });

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    areas: '',
    sex: '',
    intervalDays: null,
    startAge: null,
    endAge: null,
    yearRange: null
  });

  const typeList = ref([
    {
      label: '请选择',
      value: ''
    },
    {
      label: '男',
      value: '男'
    },
    {
      label: '女',
      value: '女'
    }
  ]);

  const yearList = ref([
    {
      label: '近一年',
      value: 1
    },
    {
      label: '近两年',
      value: 2
    }
  ]);

  /** 表单验证规则 */
  const rules = reactive({
    areas: [
      {
        required: true,
        message: '请输入推广区域',
        trigger: 'blur'
      }
    ],
    intervalDays: [
      {
        required: true,
        message: '请输入间隔天数',
        trigger: 'blur'
      }
    ],
    startAge: [
      {
        required: true,
        message: '请输入推广年龄',
        trigger: 'blur'
      }
    ]
  });

  /** 关闭弹窗 */
  const handleCancel = () => {
    visible.value = false;
  };

  const search = () => {
    try {
      formRef.value?.validate?.(async (valid) => {
        if (!valid) {
          return;
        }
        if (!form.endAge) {
          EleMessage.error('请输入年龄上限');
          return;
        }
        if (Number(form.startAge) > Number(form.endAge)) {
          EleMessage.error('年龄上限不能小于年龄下限！');
          return;
        }
        loading.value = true;
        const params = {
          ...form,
          projectId: props?.projectId
        };
        const res = await projectPromotion(params);
        promotionNumber.value = res?.promotionCnt;
        planId.value = res?.planId;
        loading.value = false;
      });
    } catch (e) {
      loading.value = false;
      EleMessage.error(e);
    }
  };

  /** 保存修改 */
  const handleOk = async () => {
    try {
      otherLoading.value = true;
      const params = { planId: planId.value };
      const res = await confirmPromotion(params);
      otherLoading.value = false;
      EleMessage.success('推荐成功!');
      promotionNumber.value = null;
      handleClosed();
      handleCancel();
    } catch (e) {
      loading.value = false;
      EleMessage.error(e);
    }
  };

  /** 弹窗关闭事件 */
  const handleClosed = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    loading.value = false;
  };
</script>
