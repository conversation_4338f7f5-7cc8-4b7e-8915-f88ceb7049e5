<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1200"
    :model-value="modelValue"
    title="高级检索"
    :align-center="false"
    @update:modelValue="updateModelValue"
  >
    <div>
      <el-form :model="params" label-position="left" label-width="125px">
        <el-row :gutter="40">
          <el-col :span="8">
            <el-form-item label="姓名：" style="width: 100%">
              <el-input
                v-model="params.name"
                placeholder="请输入姓名"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="身份证号：" style="width: 100%">
              <el-input
                v-model="params.idcard"
                placeholder="请输入身份证号"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label="状态：" style="width: 100%">
              <el-select
                v-model="params.status"
                placeholder="请选择状态"
                clearable
                style="width: 100%"
              >
                <el-option label="初查不合格" value="0" />
                <el-option label="筛选" value="1" />
                <el-option label="入组前筛选不合格" value="8" />
                <el-option label="未入组" value="5" />
                <el-option label="待入组" value="7" />
                <el-option label="入组" value="2" />
                <el-option label="入组未给药" value="6" />
                <el-option label="出组" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="性别：" style="width: 100%">
              <el-select
                v-model="params.sex"
                placeholder="请选择性别"
                clearable
                style="width: 100%"
              >
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年龄：" style="width: 100%">
              <el-input
                type="number"
                v-model="params.age"
                placeholder="请输入年龄"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label="筛选时间：" style="width: 100%">
              <el-date-picker
                v-model="params.filterdate"
                type="date"
                placeholder="请选择筛选时间"
                clearable
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="筛选号：" style="width: 100%">
              <el-input
                v-model="params.filterno"
                placeholder="请输入筛选号"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="依从性评价：" style="width: 100%">
              <el-select
                v-model="params.evaluate"
                placeholder="请选择依从性评价"
                clearable
                style="width: 100%"
              >
                <el-option label="优" value="优" />
                <el-option label="良" value="良" />
                <el-option label="中" value="中" />
                <el-option label="差" value="差" />
                <el-option label="很差" value="很差" />
                <el-option label="空白" value="空白" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入组时间：" style="width: 100%">
              <el-date-picker
                v-model="params.joindate"
                type="date"
                placeholder="请选择入组时间"
                clearable
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入组号：" style="width: 100%">
              <el-input
                v-model="params.joinno"
                placeholder="请输入入组号"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="末次给药时间：" style="width: 100%">
              <el-date-picker
                v-model="params.lastdosedate"
                type="date"
                placeholder="请选择末次给药时间"
                clearable
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="末次访视时间：" style="width: 100%">
              <el-date-picker
                v-model="params.trialedate"
                type="date"
                placeholder="请选择末次访视时间"
                clearable
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="试验间隔期：" style="width: 100%">
              <el-input
                type="number"
                v-model="params.interval"
                placeholder="请输入试验间隔期(天)"
                clearable
                style="width: 100%"
                min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="筛查备注：" style="width: 100%">
              <el-input
                v-model="params.sysremarks"
                placeholder="请输入筛查备注"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit"
          >搜 索</el-button
        >
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref } from 'vue';
  const emit = defineEmits(['done', 'update:modelValue']);

  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object
  });

  const loading = ref(false);

  const close = () => {
    updateModelValue(false);
  };
  const submit = () => {
    emit('done', params.value);
    updateModelValue(false);
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const params = ref({});
  nextTick(async () => {
    params.value = JSON.parse(JSON.stringify(props.data));
  });
</script>

<style lang="scss" scoped>
  .modal-dialog {
    width: 100%;
    height: 600px;
    overflow-y: auto;
    overflow-x: hidden;
  }
</style>
