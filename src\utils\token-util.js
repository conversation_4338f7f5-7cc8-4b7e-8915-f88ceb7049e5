/**
 * token操作封装
 */
import { TOKEN_CACHE_NAME } from '@/config/setting';
import Cookies from 'js-cookie';

/**
 * 获取缓存的token
 */
export function getToken() {
  const token = localStorage.getItem(TOKEN_CACHE_NAME);
  if (!token) {
    return (
      Cookies.get(TOKEN_CACHE_NAME) || sessionStorage.getItem(TOKEN_CACHE_NAME)
    );
  }
  return token;
}
export function getSsoToken() {
  const token = localStorage.getItem('ssotoken');
  if (!token) {
    return Cookies.get('ssotoken') || sessionStorage.getItem('ssotoken');
  }
  return token;
}
/**
 * 缓存token
 * @param token token
 * @param remember 是否永久存储
 */
export function setToken(token, remember) {
  removeToken();
  if (token) {
    if (remember) {
      localStorage.setItem(TOKEN_CACHE_NAME, token);
    } else {
      sessionStorage.setItem(TOKEN_CACHE_NAME, token);
      Cookies.set(TOKEN_CACHE_NAME, token, { expires: 30 });
    }
  }
}
export function setSsoToken(token, remember) {
  removessoToken();
  if (token) {
    if (remember) {
      localStorage.setItem('ssotoken', token);
    } else {
      sessionStorage.setItem('ssotoken', token);
      Cookies.set('ssotoken', token, { expires: 30 });
    }
  }
}
/**
 * 移除token
 */
export function removeToken() {
  localStorage.removeItem(TOKEN_CACHE_NAME);
  sessionStorage.removeItem(TOKEN_CACHE_NAME);
  Cookies.remove(TOKEN_CACHE_NAME);
}

export function removessoToken() {
  localStorage.removeItem('ssotoken');
  sessionStorage.removeItem('ssotoken');
  Cookies.remove('ssotoken');
}
