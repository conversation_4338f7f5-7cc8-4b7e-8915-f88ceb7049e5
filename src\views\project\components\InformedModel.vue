<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="680"
    position="center"
    :model-value="modelValue"
    :body-style="{ paddingLeft: '0px' }"
    title="预知情"
    @update:modelValue="updateModelValue"
  >
    <el-form :rules="rules" ref="formRef" :model="form" label-width="108px">
      <el-form-item label="知情同意书" prop="doorOssId">
        <FileUpload v-model="form.doorOssId" :limit="1" accept=".pdf" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" :loading="loading" @click="save">
        确认
      </el-button>
      <el-button @click="updateModelValue(false)">关闭</el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import FileUpload from '@/components/FileUpload/index.vue';
  import { ref, watch } from 'vue';
  import { saveProjectInformedInfo } from '@/api/project/index';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    projectId: String
  });

  // 提交状态
  const loading = ref(false);

  // 表单实例
  const formRef = ref(null);

  const rules = ref({
    doorOssId: {
      required: true,
      message: '请上传知情同意书',
      trigger: 'change'
    }
  });

  // 表单数据
  const { form, resetFields, assignFields } = useFormData({
    doorOssId: null
  });

  /* 保存编辑 */
  const save = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      try {
        const urlObject = JSON.parse(form?.doorOssId);
        const params = {
          projectId: props?.projectId,
          projectInformedFileUrl: urlObject?.name1
        };

        const res = await saveProjectInformedInfo(params);
        console.log(res);
        loading.value = false;
        EleMessage.success('修改成功');
        updateModelValue(false);
        emit('done');
      } catch (e) {
        loading.value = false;
        EleMessage.error(e);
      }
    });
  };

  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      // 打开面板进行赋值
      if (!modelValue) {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .role-menu-tree {
    box-sizing: border-box;
    width: 100%;
    padding: 6px 0;
    overflow: hidden;
    border: 1px solid var(--el-border-color);
    border-radius: var(--el-border-radius-base);
  }
</style>
