<template>
  <ContextMenu
    :menu-items="menuItems"
    @item-click="handleMenuItemClick"
    @menu-show="handleMenuShow"
    @menu-hide="handleMenuHide"
  >
    <slot></slot>
  </ContextMenu>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import ContextMenu from '../ContextMenu/index.vue';

  // 本地存储键名
  const STORAGE_KEY = 'card-reader-settings';

  // 本地存储工具函数
  const saveSettings = (settings) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
    } catch (error) {
      console.warn('保存读卡设置失败:', error);
    }
  };

  const loadSettings = () => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      console.warn('加载读卡设置失败:', error);
      return null;
    }
  };

  // Props
  const props = defineProps({
    // 手动读取模式
    manualMode: {
      type: Boolean,
      default: false
    },
    // 使用备用API
    useBackupApi: {
      type: Boolean,
      default: false
    },
    // 是否禁用右键菜单
    disabled: {
      type: Boolean,
      default: false
    }
  });

  // Emits
  const emit = defineEmits([
    'update:manualMode',
    'update:useBackupApi',
    'manual-mode-change',
    'api-mode-change',
    'settings-reset'
  ]);

  // 从本地存储加载设置
  const savedSettings = loadSettings();

  // 内部状态 - 优先使用本地存储的设置，其次使用props，最后使用默认值
  const internalManualMode = ref(
    savedSettings?.manualMode ?? props.manualMode ?? false
  );
  const internalUseBackupApi = ref(
    savedSettings?.useBackupApi ?? props.useBackupApi ?? false
  );

  // 监听props变化
  watch(
    () => props.manualMode,
    (newVal) => {
      internalManualMode.value = newVal;
    }
  );

  watch(
    () => props.useBackupApi,
    (newVal) => {
      internalUseBackupApi.value = newVal;
    }
  );

  // 菜单项配置
  const menuItems = computed(() => [
    {
      id: 'manual-mode',
      label: '切换手动读取',
      checked: internalManualMode.value,
      icon: 'hand'
    },
    {
      id: 'backup-api',
      label: '切换备用读卡API',
      checked: internalUseBackupApi.value,
      icon: 'api'
    },
    {
      id: 'reset',
      label: '恢复默认设置',
      icon: 'reset'
    }
  ]);

  // 处理菜单项点击
  const handleMenuItemClick = (item) => {
    switch (item.id) {
      case 'manual-mode':
        toggleManualMode();
        break;
      case 'backup-api':
        toggleApiMode();
        break;
      case 'reset':
        resetSettings();
        break;
    }
  };

  // 切换手动读取模式
  const toggleManualMode = () => {
    const newValue = !internalManualMode.value;
    internalManualMode.value = newValue;

    // 发出事件
    emit('update:manualMode', newValue);
    emit('manual-mode-change', newValue);

    // 显示提示信息
    if (newValue) {
      EleMessage.info(
        '已开启手动读取模式（自动读取模式关闭），请点击“手动读取”按钮进行读卡'
      );
    } else {
      EleMessage.info('已关闭手动读取模式，恢复自动读取模式');
    }
  };

  // 切换API模式
  const toggleApiMode = () => {
    const newValue = !internalUseBackupApi.value;
    internalUseBackupApi.value = newValue;

    // 发出事件
    emit('update:useBackupApi', newValue);
    emit('api-mode-change', newValue);

    // 显示提示信息
    if (newValue) {
      EleMessage.info('已切换备用读卡API（仅支持国内身份证）');
    } else {
      EleMessage.info('已切换默认读卡API（支持国内+港澳台身份证）');
    }
  };

  // 重置设置
  const resetSettings = () => {
    const wasManual = internalManualMode.value;
    const wasBackupApi = internalUseBackupApi.value;

    // 重置为默认值
    internalManualMode.value = false;
    internalUseBackupApi.value = false;

    // 清除本地存储
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn('清除读卡设置失败:', error);
    }

    // 发出事件
    emit('update:manualMode', false);
    emit('update:useBackupApi', false);
    emit('settings-reset', {
      previousManualMode: wasManual,
      previousUseBackupApi: wasBackupApi
    });

    EleMessage.success(
      '已重置为默认设置：自动读取模式 + 默认读卡API（支持国内+港澳台身份证）'
    );
  };

  // 处理菜单显示
  const handleMenuShow = (position) => {
    console.log('读卡设置菜单已显示', position);
  };

  // 处理菜单隐藏
  const handleMenuHide = () => {
    console.log('读卡设置菜单已隐藏');
  };

  // 保存当前设置到本地存储
  const saveCurrentSettings = () => {
    const settings = {
      manualMode: internalManualMode.value,
      useBackupApi: internalUseBackupApi.value,
      timestamp: Date.now() // 添加时间戳用于调试
    };
    saveSettings(settings);
  };

  // 监听设置变化，自动保存
  watch([internalManualMode, internalUseBackupApi], () => {
    saveCurrentSettings();
  });

  // 组件挂载时的初始化
  onMounted(() => {
    // 如果从本地存储加载了设置，需要通知父组件
    if (savedSettings) {
      emit('update:manualMode', internalManualMode.value);
      emit('update:useBackupApi', internalUseBackupApi.value);

      // 如果有保存的设置，显示提示信息
      if (internalManualMode.value || internalUseBackupApi.value) {
        const modes = [];
        if (internalManualMode.value) modes.push('手动读取模式');
        if (internalUseBackupApi.value) modes.push('备用读卡API');
        EleMessage.info(`已加载保存的设置: ${modes.join(' + ')}`);
      }
    }
  });

  // 暴露方法给父组件
  defineExpose({
    toggleManualMode,
    toggleApiMode,
    resetSettings,
    // 获取当前设置
    getCurrentSettings: () => ({
      manualMode: internalManualMode.value,
      useBackupApi: internalUseBackupApi.value
    })
  });
</script>

<style scoped>
  /* 组件本身不需要额外样式，样式由ContextMenu组件提供 */
</style>
