<template>
  <div>
    <ele-modal
      form
      width="500px"
      :model-value="modelValue"
      title="编辑"
      @update:modelValue="updateModelValue"
    >
      <el-form
        ref="formRef"
        label-position="left"
        label-width="105px"
        :model="form"
        :rules="rules"
      >
        <el-form-item label="姓名">
          <el-input
            placeholder="请输入姓名"
            v-model="subjectsName"
            :disabled="true"
            maxlength="99"
          />
        </el-form-item>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="有效期至" prop="indate" class="formRequired">
              <el-date-picker
                type="date"
                v-model="form.indate"
                placeholder="请选择有效期"
                value-format="YYYY-MM-DD"
                clearable
                style="width: 100%"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="是否公开" prop="ispublic" class="formRequired">
              <el-select
                v-model="form.ispublic"
                placeholder="请选择是否公开"
                clearable
                style="width: 100%"
              >
                <el-option label="否" :value="Number(0)" />
                <el-option label="是" :value="Number(1)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备忘录信息" prop="message" class="formRequired">
          <el-input
            type="textarea"
            v-model="form.message"
            placeholder="请输入备忘录信息"
            :autosize="{
              minRows: 3,
              maxRows: 6
            }"
            clearable
            show-word-limit
            maxlength="500"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="footer-btn">
          <el-button @click="updateModelValue(false)" type="info"
            >取 消</el-button
          >
          <el-button type="primary" :loading="loading" @click="save">
            确 定
          </el-button>
        </div>
      </template>
    </ele-modal>
  </div>
</template>

<script setup>
  import { ref, reactive, watch, nextTick } from 'vue';
  import { sysMemoUpdate } from '@/api/subjects/memoInfor/index.js';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  const emit = defineEmits(['done', 'update:modelValue']);
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    data: Object
  });
const disabledDate = (time) => {
    const maxDate = new Date('9999-12-31');
    return time.getTime() < Date.now() || time.getTime() > maxDate.getTime(); // - 8.64e7是今天可以选
  };
  // 表单实例
  const formRef = ref(null);
  const subjectsName = ref('');
  const loading = ref(false);
  // 表单数据
  const { form, resetFields, assignFields } = useFormData({
    guid: undefined,
    ispublic: undefined,
    indate: undefined,
    message: undefined
  });
  // 表单验证规则
  const rules = reactive({
    indate: [
      { required: true, message: '有效期限至不能为空', trigger: 'change' }
    ],
    ispublic: [
      { required: true, message: '是否公开不能为空', trigger: 'change' }
    ],
    message: [
      { required: true, message: '备忘录信息不能为空', trigger: 'change' }
    ]
  });
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      sysMemoUpdate(form).then((res) => {
        if (res.code == 200) {
          EleMessage.success('修改成功');
          updateModelValue(false);
          emit('done');
          resetFields();
          loading.value = false;
        } else {
          EleMessage.error(res.msg);
          loading.value = false;
        }
      });
    });
  };
  nextTick(() => {
    if (props.data) {
      subjectsName.value = props.data.subjectsName;
      assignFields(props.data);
    }
  });
  watch(
    () => props.modelValue,
    async (modelValue) => {
      if (modelValue) {
        if (props.data) {
          subjectsName.value = props.data.subjectsName;
          assignFields(props.data);
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>

<style lang="scss" scoped>
  .footer-btn {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  :deep(.formRequired .el-form-item__label) {
    transform: translateX(-10px);
  }
</style>
