<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1200"
    :model-value="modelValue"
    title="高级检索"
    :align-center="false"
    @update:modelValue="updateModelValue"
  >
    <div>
      <el-form :model="params" label-width="100px" label-position="left">
        <el-row :gutter="40">
          <el-col :span="8" v-if="loginUser.role == 99">
            <el-form-item label="所属机构：" style="width: 100%">
              <el-select
                v-model="params.designativeOrganizationGuid"
                placeholder="请选择或搜索所属机构"
                clearable
                filterable
                remote
                reserve-keyword
                :remote-method="remoteMethod"
                style="width: 100%"
              >
                <el-option
                  v-for="(dict, index) in organizaList"
                  :key="index"
                  :value="dict.guid"
                  :label="dict.fullname"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="姓名：" style="width: 100%">
              <el-input
                v-model="params.subjectsName"
                placeholder="请输入姓名"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号：" style="width: 100%">
              <el-input
                v-model="params.idCard"
                placeholder="请输入身份证号"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建人：" style="width: 100%">
              <el-input
                v-model="params.usersName"
                placeholder="请输入创建人"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间：" style="width: 100%">
              <el-date-picker
                v-model="params.date"
                type="date"
                placeholder="请选择创建时间"
                clearable
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否公开：" style="width: 100%">
              <el-select
                v-model="params.ispublic"
                placeholder="请选择是否公开"
                clearable
                style="width: 100%"
              >
                <el-option label="否" value="0" />
                <el-option label="是" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="有效期至：" style="width: 100%">
              <el-date-picker
                v-model="params.indate"
                type="date"
                placeholder="请选择有效期"
                clearable
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="备忘录：" style="width: 100%">
              <el-input
                v-model="params.message"
                placeholder="请输入备忘录信息"
                clearable
                style="width: 100%"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit"
          >搜 索</el-button
        >
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref, computed } from 'vue';
  import { useUserStore } from '@/store/modules/user';
  import { organizationList } from '@/api/subjects/unsuitPeople/index.js';
  const userStore = useUserStore();
  /** 当前用户信息 */
  const loginUser = computed(() => userStore.info ?? {});
  const emit = defineEmits(['done', 'update:modelValue']);

  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object,
    orgList: Array
  });
  const loading = ref(false);
  const organizaList = ref([]);
  const organizaCache = ref([]);
  const close = () => {
    updateModelValue(false);
  };
  const submit = () => {
    let data = {
      p: params.value,
      o: organizaList.value
    };
    emit('done', data);
    updateModelValue(false);
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const params = ref({});
  const remoteMethod = (query) => {
    if (query) {
      organizationList({ keywords: query, pageNumber: 1, pageSize: 100 }).then(
        (res) => {
          organizaList.value = res.data.list;
        }
      );
    } else {
      organizaList.value = organizaCache.value;
    }
  };
  nextTick(async () => {
    params.value = JSON.parse(JSON.stringify(props.data));
    organizaList.value = props.orgList;
    organizaCache.value = props.orgList;
    // organizationList({
    //   pageNumber: 1,
    //   pageSize: 50,
    //   orderBy: '',
    //   orderItem: 'asc'
    // }).then((res) => {
    //   console.log(res);
    //   organizaList.value = res.data.list;
    //   organizaCache.value = res.data.list;
    // });
  });
</script>

<style lang="scss" scoped>
  .modal-dialog {
    width: 100%;
    height: 600px;
    overflow-y: auto;
    overflow-x: hidden;
  }
</style>
