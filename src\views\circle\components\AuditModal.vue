<!-- 修改密码弹窗 -->
<template>
  <ele-modal
    form
    :width="600"
    :title="props.isFirstAudit ? '初审' : '复审'"
    :append-to-body="true"
    v-model="visible"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent=""
    >
      <el-form-item label="审核结果" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio-button :value="1" label="通过" />
          <el-radio-button :value="2" label="拒绝" />
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item v-if="form.status == 1" label="通过备注">
        <el-input
          v-model="form.rejectReason"
          :maxlength="100"
          :rows="3"
          type="textarea"
          placeholder="请输入通过备注"
        />
      </el-form-item> -->
      <el-form-item v-if="form.status == 2" label="拒绝原因">
        <el-input
          v-model="form.rejectReason"
          :maxlength="100"
          :rows="3"
          type="textarea"
          placeholder="请输入拒绝原因"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleOk">
        保存
      </el-button>
    </template>
  </ele-modal>
</template>

<script setup>
  import { ref, reactive, watch, computed } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { firstAudit, secondAudit } from '@/api/circle/index';

  const emit = defineEmits(['update:modelValue', 'done']);

  const props = defineProps({
    currnetID: String,
    model: String,
    isFirstAudit: Boolean,
    modelValue: Boolean,
    currentData: Object
  });

  /** 弹窗是否打开 */
  const visible = defineModel({ type: Boolean });

  /** 提交loading */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    status: 1,
    rejectReason: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    rejectReason: [
      {
        required: false,
        message: '请输入拒绝原因',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  /** 关闭弹窗 */
  const handleCancel = () => {
    updateModelValue(false);
  };

  /** 保存修改 */
  const handleOk = () => {
    formRef.value?.validate?.(async (valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      try {
        const params = {
          ...form,
          postId: props.currnetID
        };
        const Api = props?.isFirstAudit ? firstAudit : secondAudit;
        await Api(params);
        EleMessage.success('操作成功');
        emit('done');
        updateModelValue(false);
      } catch (e) {
        loading.value = false;
        EleMessage.error(e);
      }
    });
  };

  /** 弹窗关闭事件 */
  const handleClosed = () => {
    resetFields();
    formRef.value?.clearValidate?.();
    loading.value = false;
  };

  // watch(
  //   () => props.modelValue,
  //   (val) => {
  //     if (val) {
  //       form.status = props.currentData?.auditStatus;
  //       form.isAddWechat = props.currentData?.addWechat;
  //       form.isCompanyEmployee = props.currentData?.isCompanyUser;
  //       form.rejectReason = props.currentData?.auditRemark;
  //     }
  //   },
  //   { immediate: true }
  // );
</script>
