import request from '@/utils/request-business';

/**
 * 获取会话聊天列表
 */
export async function getChatList(projectId) {
  const res = await request.get(`/im/getChatList?projectId=${projectId}`);
  if (res.data.code === 200 && res.data.data) {
    return res.data?.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 获取当前用户所属机构的招募项目下拉列表
 */
export async function getImProjectSelects() {
  const res = await request.get(`/im/getImProjectSelects`);
  if (res.data.code === 200 && res.data.data) {
    return res.data?.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查看会话详情
 */
export async function getSessionDetail(params) {
  const res = await request.get('/im/getSessionDetail', { params });
  if (res.data.code === 200 && res.data.data) {
    return res.data?.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 发送消息
 * @param {*} data
 * @returns
 */
export async function pushMessage(data) {
  const res = await request.post(`/im/pushMessage`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}
