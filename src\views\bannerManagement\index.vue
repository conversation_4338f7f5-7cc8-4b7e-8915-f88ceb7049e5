<template>
  <ele-page
    flex-table
    :multi-card="false"
    hide-footer
    style="min-height: 420px; background-color: #ecf2f9"
  >
    <ele-card flex-table style="margin-top: 8px">
      <!-- Tabs 组件 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        highlight-current-row
      >
        <!-- 关键字搜索 -->
        <template #toolbar>
          <el-space>
            <el-button type="primary" icon="Plus" @click="addOredit(null)"
              >新增横幅</el-button
            >
          </el-space>
        </template>

        <template #url="{ row }">
          <img :src="`${row.url}`" alt="" style="height: 80px" />
        </template>
        <template #action="{ row }">
          <el-space>
            <el-link type="primary" :underline="false" @click="addOredit(row)">
              编辑
            </el-link>
            <el-divider direction="vertical" />
            <el-link type="primary" :underline="false" @click="deleteFun(row)">
              删除
            </el-link>
            <template v-if="row.bannerStatus == 1">
              <el-divider direction="vertical" />
              <el-link
                type="primary"
                :underline="false"
                @click="updateStatus(row, 2)"
              >
                上架
              </el-link>
            </template>
            <template v-if="row.bannerStatus == 2">
              <el-divider direction="vertical" />
              <el-link
                type="primary"
                :underline="false"
                @click="updateStatus(row, 1)"
              >
                下架
              </el-link>
            </template>
          </el-space>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ElMessage } from 'element-plus';
  import { ElMessageBox } from 'element-plus';
  import { ref, computed } from 'vue';
  import { getPageList, deleteBanner, editBanner } from '@/api/banner/index';
  import { useRouter } from 'vue-router';

  const router = useRouter();

  const baseUrl = import.meta.env.VITE_APP_BASE_IMG;
  // 搜索组件实例
  const searchRef = ref();

  // 表格实例
  const tableRef = ref(null);

  const showModal = ref(false);

  const current = ref(null);

  // 表格列配置
  const columns = computed(() => {
    return [
      {
        type: 'index',
        columnKey: 'index',
        label: `序号`,
        width: 70,
        align: 'center',
        fixed: 'left'
      },
      {
        label: `小程序横幅图片`,
        prop: 'url',
        slot: 'url',
        width: 240
      },
      {
        label: `排序`,
        prop: 'orderNumber',
        width: 100
      },
      {
        label: `创建时间`,
        prop: 'createTime',
        align: 'center',
        minWidth: 180
      },
      {
        label: `更新时间`,
        prop: 'updateTime',
        align: 'center',
        minWidth: 180
      },
      {
        label: `跳转类别`,
        prop: 'jumpLinkType',
        align: 'center',
        minWidth: 180,
        formatter: (row) =>
          row.jumpLinkType == 0
            ? '无跳转'
            : row.jumpLinkType == 1
              ? '项目详情'
              : row.jumpLinkType == 2
                ? '内部跳转'
                : '外部链接'
      },
      {
        label: `创建人`,
        prop: 'createName',
        minWidth: 140
      },
      {
        columnKey: 'action',
        label: '操作',
        width: 200,
        align: 'center',
        resizable: false,
        slot: 'action'
      }
    ];
  });

  // 表格数据源
  const datasource = ({ page, limit, where, orders, filters }) => {
    return getPageList({
      ...where,
      ...orders,
      ...filters,
      page: page,
      limit: limit
    });
  };

  /* 搜索 */
  const reload = (where) => {
    current.value = null;
    tableRef.value?.reload?.({ page: 1, where });
  };
  /* 搜索 */
  const reloadSearch = (where) => {
    searchRef.value?.onSearchFilterSubmit(where);
  };
  const addOredit = (row = null) => {
    const path = '/banner/add-banner';
    router.push({
      path,
      query: {
        id: row?.id ?? ''
      }
    });
  };
  const deleteFun = async (row) => {
    await ElMessageBox.confirm(`是否确认删除该条数据`, '提示', {
      type: 'warning',
      draggable: true
    });
    try {
      await deleteBanner(row.id);
      ElMessage.success('删除成功！');
      reload();
    } catch (e) {
      ElMessage.error(e.message);
    }
  };

  const updateStatus = async (row, model) => {
    try {
      const params = {
        id: row.id,
        bannerStatus: model
      };
      await editBanner(params);
      ElMessage.success('操作成功！');
      reload();
    } catch (e) {
      ElMessage.error(e.message);
    }
  };
</script>
<style lang="scss" scoped></style>
