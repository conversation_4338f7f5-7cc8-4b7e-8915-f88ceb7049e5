<template>
  <ele-card style="height: 530px" header="全院科室项目报名数排行榜">
    <template #extra>
      <el-radio-group v-model="activeName">
        <el-radio-button label="1">今日</el-radio-button>
        <el-radio-button label="2">本周</el-radio-button>
        <el-radio-button label="3">本月</el-radio-button>
      </el-radio-group>
    </template>
    <div style="font-size: 16px">
      <el-row>
        <el-col :span="3">
          <span style="margin-left: 10px; color: black">排序</span>
        </el-col>
        <el-col :span="12">
          <span style="margin-left: 10px">项目名称</span>
        </el-col>
        <el-col :span="6">
          <span>科室</span>
        </el-col>
        <el-col :span="3" style="text-align: right">
          <span>报名数</span>
        </el-col>
      </el-row>
    </div>
    <div v-if="list.length !== 0">
      <div class="box">
        <div v-for="(item, index) in list" :key="index" class="content backRed">
          <el-row style="font-size: 16px; padding: 5px 10px">
            <el-col :span="3">
              <span
                :class="{
                  orange: index == 0 || index == 1 || index == 2,
                  blue: index !== 0 && index !== 1 && index !== 2
                }"
                >{{ index + 1 }}</span
              >
            </el-col>
            <el-col :span="12">
              <span>{{ item?.projectRecruitName }}</span>
            </el-col>
            <el-col :span="6">
              <span>{{ item?.deptName }}</span>
            </el-col>
            <el-col :span="3" style="text-align: right">
              <span>{{ item?.applyNumber }}</span>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无数据" />
  </ele-card>
</template>
<script setup>
  import { ElMessage } from 'element-plus';
  import { Icon } from '@iconify/vue';
  import { ref, watch } from 'vue';
  import { getSiteRecruitDetailsList } from '@/api/project/index';

  const list = ref([]);
  const listBack = ref([
    {
      projectRecruitName: '注射用ZG006',
      projectId: 143,
      projectRecruitType: '2',
      planJoinGroupTotalPeople: null,
      deptName: '科室1',
      applyNumber: 3,
      projectRecruitDeadline: '2025-06-30'
    },
    {
      projectRecruitName: '其他项目患者',
      projectId: 131,
      projectRecruitType: '2',
      planJoinGroupTotalPeople: null,
      deptName: '科室1',
      applyNumber: 2,
      projectRecruitDeadline: '2025-06-30'
    },
    {
      projectRecruitName: 'ADSO-S9S',
      projectId: 138,
      projectRecruitType: '1',
      planJoinGroupTotalPeople: null,
      deptName: '妇女保健科',
      applyNumber: 2,
      projectRecruitDeadline: '2025-06-29'
    },
    {
      projectRecruitName: 'CM518D1',
      projectId: 133,
      projectRecruitType: '2',
      planJoinGroupTotalPeople: 10,
      deptName: '血液科',
      applyNumber: 1,
      projectRecruitDeadline: '2026-06-28'
    },
    {
      projectRecruitName: '克立硼罗软膏',
      projectId: 139,
      projectRecruitType: '1',
      planJoinGroupTotalPeople: null,
      deptName: '血液科',
      applyNumber: 1,
      projectRecruitDeadline: '2025-06-30'
    },
    {
      projectRecruitName: '高血压测试项目111222',
      projectId: 126,
      projectRecruitType: '1',
      planJoinGroupTotalPeople: null,
      deptName: '科室2',
      applyNumber: 1,
      projectRecruitDeadline: '2025-05-30'
    },
    {
      projectRecruitName: '诺氟沙星胶囊',
      projectId: 127,
      projectRecruitType: '1',
      planJoinGroupTotalPeople: null,
      deptName: null,
      applyNumber: 1,
      projectRecruitDeadline: '2025-06-30'
    },
    {
      projectRecruitName: '诺氟沙星胶囊',
      projectId: 128,
      projectRecruitType: '1',
      planJoinGroupTotalPeople: null,
      deptName: null,
      applyNumber: 1,
      projectRecruitDeadline: '2025-06-30'
    },
    {
      projectRecruitName: '诺氟沙星胶囊',
      projectId: 129,
      projectRecruitType: '1',
      planJoinGroupTotalPeople: null,
      deptName: null,
      applyNumber: 1,
      projectRecruitDeadline: '2025-06-30'
    },
    {
      projectRecruitName: '诺氟沙星胶囊',
      projectId: 130,
      projectRecruitType: '1',
      planJoinGroupTotalPeople: null,
      deptName: null,
      applyNumber: 1,
      projectRecruitDeadline: '2025-06-30'
    }
  ]);
  const activeName = ref('1');

  const getoverallDepartTop10Ranking = () => {
    const params = {
      dateType: activeName.value,
      page: 1,
      limit: 10
    };
    getSiteRecruitDetailsList(params)
      .then((res) => {
        list.value = res.records ?? [];
        console.log(list.value);
      })
      .catch((e) => {
        ElMessage.error(e);
      });
  };
  getoverallDepartTop10Ranking();

  watch(activeName, () => {
    getoverallDepartTop10Ranking();
  });
</script>
<style lang="scss" scoped>
  .box {
    height: 410px;
    overflow: auto;
  }

  .content {
    padding-left: 8px;
    margin-top: 5px;
  }

  .width8 {
    display: inline-block;
    width: 8%;
    padding: 5px 0;
    color: #fff;
  }

  .orange {
    display: inline-block;
    width: 22px;
    height: 22px;
    text-align: center;
    background: #ff881f;
  }

  .blue {
    display: inline-block;
    width: 22px;
    height: 22px;
    text-align: center;
    background: #90b9ff;
  }

  .width60 {
    display: inline-block;
    width: 60%;
  }

  .backRed {
    background: #f1f6ff;
  }

  .cursor {
    cursor: pointer;
  }

  :deep(.ele-card-header) {
    border: none;
  }
</style>
