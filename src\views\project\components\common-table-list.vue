<template>
  <div class="d-flex">
    <!-- 数据列表 -->
    <div class="main-card">
      <ele-loading v-if="data.length > 0" :loading="loading">
        <div
          v-for="item in data"
          :key="item.projectId"
          class="card"
          @click="handleItemClick(item)"
        >
          <div class="list-item">
            <div class="list-item-avatar">
              <div class="list-item-avatar-extra">
                <el-tooltip
                  v-if="item.projectRecruitName.length > 50"
                  class="item-tooltip"
                  effect="dark"
                  :content="item.projectRecruitName"
                  placement="top"
                >
                  <div style="margin-bottom: 2px; margin-left: -8px">
                    <span :style="getTextColor(item?.projectRecruitType)">{{
                      item?.projectRecruitType == 1 ? '【健康】' : '【患者】'
                    }}</span>
                    {{ item?.projectRecruitName.substring(0, 50) }}...
                  </div>
                </el-tooltip>
                <div v-else style="margin-bottom: 2px; margin-left: -8px">
                  <span :style="getTextColor(item?.projectRecruitType)">{{
                    item?.projectRecruitType == 1 ? '【健康】' : '【患者】'
                  }}</span>
                  {{ item?.projectRecruitName }}
                </div>
                <el-row>
                  <el-tooltip
                    v-if="item.indications.length > 50"
                    class="item-tooltip"
                    effect="dark"
                    :content="item.indications"
                    placement="top"
                  >
                    <el-col class="ellipsis t-title" :span="24">
                      适应症：{{ item?.indications.substring(0, 50) }}...
                    </el-col>
                  </el-tooltip>
                  <el-col class="t-title" v-else :span="24">
                    适应症：{{ item?.indications }}
                  </el-col>
                </el-row>
                <el-row>
                  <el-col class="t-title" :span="8"
                    >适应用药：{{ item.drugName }}</el-col
                  >
                  <el-col class="ellipsis t-title" :span="8"
                    >招募中心：{{
                      item?.projectRecruitType == 2
                        ? item.siteNumber
                        : item.siteNames
                    }}</el-col
                  >
                  <el-col class="t-title" :span="8"
                    >招募截止：{{ item.projectRecruitDeadline }}
                  </el-col>
                  <!-- <el-col class="t-title" :span="8"
                    >推荐奖励：{{ item.referralReward }}</el-col
                  >
                  <el-col class="t-title" :span="8"
                    >试验补助：{{ getSubsidyAmount(item) }}</el-col
                  > -->
                </el-row>
              </div>
              <!-- <div>
                <span :style="getTextColor(item?.projectRecruitType)">{{
                  item?.projectRecruitType == 1 ? '【健康】' : '【患者】'
                }}</span>
                {{ item?.projectRecruitName }}
              </div> -->
            </div>

            <div class="list-item-owner">
              {{ item?.applyAudiNumber }}
              <div class="list-item-title">报名审核</div>
            </div>
            <div class="list-item-owner">
              {{ item?.applyNumber }}
              <div class="list-item-title">已报名</div>
            </div>
            <div class="list-item-owner">
              {{
                item?.joinGroupNumber != 0 && item?.joinGroupNumber
                  ? item?.joinGroupNumber +
                    '/' +
                    (item.planJoinGroupTotalPeople || '未设置')
                  : 0
              }}
              <div class="list-item-title">入组进度</div>
            </div>
            <div class="list-item-progress" style="display: block">
              <ele-dot
                v-if="item?.recruitStatus == 1"
                style="color: #f97316; font-size: 16px"
                text="草稿"
                type="warning"
              />
              <ele-dot
                v-if="item?.recruitStatus == 2"
                style="color: #209373; font-size: 16px"
                text="招募中"
                type="success"
              />
              <ele-dot
                v-if="item?.recruitStatus == 3"
                style="color: #9ca3af; font-size: 16px"
                text="审核失败"
                type="info"
              />
              <ele-dot
                v-if="item?.recruitStatus == 4"
                style="color: #9ca3af; font-size: 16px"
                text="已关闭"
                type="info"
              />
            </div>
            <div class="list-item-tools">
              <el-link
                type="primary"
                :underline="false"
                @click.stop="showChatDialog(item)"
              >
                沟通
                <span v-if="item.unreadCount > 0" class="unread-count">{{
                  item.unreadCount
                }}</span>
              </el-link>
              <el-divider direction="vertical" />
              <el-link
                type="primary"
                :underline="false"
                @click.stop="openEdit(item)"
              >
                编辑
              </el-link>
              <el-divider direction="vertical" />
              <ele-dropdown
                :items="
                  [
                    {
                      title: '打开',
                      command: 'open',
                      visible: item?.recruitStatus == 4
                    },
                    {
                      title: '项目推广',
                      command: 'popularize',
                      visible: hasPermission('project:screen:popularize')
                    },
                    {
                      title: '后台报名',
                      command: 'apply',
                      // visible: false // 暂时隐藏
                      visible:
                        hasPermission('project:screen:apply') &&
                        item?.projectRecruitType == 1
                    },
                    {
                      title: '关注',
                      command: 'follow',
                      visible: item?.followFlag == 0
                    },
                    {
                      title: '取消关注',
                      command: 'follow',
                      visible: item?.followFlag == 1
                    },
                    {
                      title: '关闭',
                      command: 'closed',
                      visible: item?.recruitStatus == 2
                    },
                    {
                      title: '发布',
                      command: 'publish',
                      visible: item?.recruitStatus == 1
                    },
                    {
                      title: '删除',
                      command: 'remove',
                      danger: true,
                      visible: true
                    },
                    {
                      title: '预知情',
                      command: 'informed',
                      visible: hasPermission('project:screen:informed')
                    }
                  ].filter((i) => i.visible)
                "
                @command="(command) => dropClick(command, item)"
              >
                <el-link type="primary" :underline="false">
                  <span>更多</span>
                  <el-icon :size="12">
                    <ArrowDown />
                  </el-icon>
                </el-link>
              </ele-dropdown>
            </div>
          </div>
        </div>
        <div
          style="
            width: 100%;
            display: flex;
            justify-content: flex-end;
            padding-right: 20px;
            box-sizing: border-box;
          "
        >
          <ele-pagination
            v-if="data.length > 0"
            class="flexCenter"
            v-model:current-page="detailParams.page"
            v-model:page-size="detailParams.limit"
            :page-sizes="[10, 20, 30, 40]"
            layout="total, sizes, prev, pager, next, jumper"
            :pager-count="4"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </ele-loading>
      <el-empty v-else description="暂无数据" />
    </div>
    <ChatProjectModel
      v-model="chatDialogVisible"
      :projectId="projectId"
      :title="pmTitle"
    />
    <ApplyModel v-model="showBackApply" :projectId="projectId" />

    <ProjectPopularizeModel v-model="showPopularize" :projectId="projectId" />

    <InformedModel v-model="informedDialogVisible" :projectId="projectId" />
  </div>
</template>
<script setup name="commonTable">
  import { ref, nextTick, watch, onMounted, onActivated } from 'vue';
  import { usePermission } from '@/utils/use-permission';
  import {
    getOrgPageList,
    deleteProject,
    followProject,
    changeProjectRcruitStatus
  } from '@/api/project/index';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { usePageTab } from '@/utils/use-page-tab';
  import { useRouter } from 'vue-router';
  import { ElMessageBox } from 'element-plus';
  import ChatProjectModel from './ChatProjectModel.vue';
  import ProjectPopularizeModel from './ProjectPopularizeModel.vue';
  import ApplyModel from './ApplyModel.vue';
  import InformedModel from './InformedModel.vue';

  const { hasPermission } = usePermission();

  const { removePageTab } = usePageTab();

  const { push } = useRouter();

  const total = ref(0);

  const pmTitle = ref('');

  const informedDialogVisible = ref(false);

  const detailParams = ref({
    page: 1,
    limit: 10,
    orderBy: '',
    orderItem: 'asc'
  });

  const props = defineProps({
    /** 文件大小限制, 单位MB */
    model: {
      type: String,
      default: 1
    },
    keywords: {
      type: String,
      default: ''
    },
    recruitType: {
      type: [String, Number],
      default: ''
    },
    auditFlag: {
      type: Boolean,
      default: null
    }
  });
  /** 列表加载状态 */
  const loading = ref(false);

  const data = ref([]);

  const showPopularize = ref(false);

  const showBackApply = ref(false);

  /** 下拉菜单点击事件 */
  const dropClick = async (key, row) => {
    // 关注  取消关注
    if (key == 'follow') {
      const title =
        row?.followFlag == 0
          ? `确认是否关注${row?.drugName}项目吗？`
          : `确认是否取消关注${row?.drugName}项目吗？`;
      try {
        await ElMessageBox.confirm(title, '提示', {
          type: 'warning',
          draggable: true,
          customStyle: {
            maxWidth: '600px'
          }
        });
        const params = {
          projectId: row?.projectId,
          followFlag: row?.followFlag == 0 ? '1' : '0'
        };
        await followProject(params);
        EleMessage.success('操作成功');
        reload();
      } catch (e) {
        if (e != 'cancel') {
          EleMessage.error(e?.message);
        }
      }
    } else if (key === 'popularize') {
      projectId.value = row.projectId;
      showPopularize.value = true;
    } else if (key === 'apply') {
      projectId.value = row.projectId;
      showBackApply.value = true;
    } else if (key === 'remove') {
      try {
        await ElMessageBox.confirm(
          `您确定删除该招募职位吗？删除后不可撤销`,
          '提示',
          {
            type: 'warning',
            draggable: true,
            customStyle: {
              maxWidth: '600px'
            }
          }
        );
        await deleteProject(row?.projectId);
        EleMessage.success('操作成功');
        reload();
      } catch (e) {
        if (e != 'cancel') {
          EleMessage.error(e?.message);
        }
      }
    } else if (['open', 'publish'].includes(key)) {
      try {
        const params = {
          projectId: row?.projectId,
          recruitStatus: 2
        };
        const res = await changeProjectRcruitStatus(params);
        EleMessage.success('操作成功');
        reload();
      } catch (e) {
        EleMessage.error(e?.message);
      }
    } else if (key === 'closed') {
      try {
        const params = {
          projectId: row?.projectId,
          recruitStatus: 4
        };
        const res = await changeProjectRcruitStatus(params);
        EleMessage.success('操作成功');
        reload();
      } catch (e) {
        console.log(e);
        EleMessage.error(e?.message);
      }
    } else if (key === 'informed') {
      projectId.value = row.projectId;
      informedDialogVisible.value = true;
    }
  };

  const fetchData = async () => {
    loading.value = true;
    try {
      let response;
      if (props.model == 1) {
        response = await getOrgPageList({
          recruitName: props.keywords || '',
          recruitType: props.recruitType || '',
          auditFlag: props.auditFlag,
          // page: page.value,
          // limit: limit.value
          ...detailParams.value
        });
      } else {
        // response = await getOtherPageList({
        //   page: page.value,
        //   limit: limit.value
        // });
      }
      data.value = response?.records ?? [];
      total.value = response?.total ?? 0;
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleSizeChange = (size) => {
    detailParams.value.limit = size;
    fetchData();
  };

  const handleCurrentChange = (current) => {
    detailParams.value.page = current;
    fetchData();
  };

  const reload = () => {
    fetchData();
  };

  watch(() => props.type, fetchData);
  watch(
    [() => props.keywords, () => props.recruitType, () => props.auditFlag],
    () => {
      fetchData();
    }
  );

  onMounted(() => {
    fetchData();
  });

  onActivated(() => {
    fetchData();
  });

  const handleItemClick = (item) => {
    const path = '/recruitment';
    removePageTab({ key: path });
    nextTick(() => {
      push({
        path,
        query: item ? { projectId: item.projectId } : void 0
      });
    });
  };

  const openEdit = async (row) => {
    const path = '/recruit/edit-project';
    removePageTab({ key: path });
    nextTick(() => {
      push({
        path,
        query: row ? { projectId: row.projectId } : void 0
      });
    });
  };

  const chatDialogVisible = ref(false);
  const projectId = ref(null);

  const showChatDialog = async (row) => {
    projectId.value = row.projectId;
    pmTitle.value = row.projectRecruitName;
    chatDialogVisible.value = true;
  };

  const toChat = async (row) => {
    const path = '/recruit/chat';
    removePageTab({ key: path });
    nextTick(() => {
      push({
        path,
        query: row ? { projectId: row.projectId } : void 0
      });
    });
  };

  const getTextColor = (type) => {
    if (type == 1) {
      return { color: '#209373' }; // 健康类型为绿色
    } else {
      return { color: '#F97316' }; // 患者类型为蓝色
    }
  };

  /**
   * 获取试验补助
   * @param {Object} data - 包含补助信息的对象
   * @returns {number | string} - 返回相应的补助金额
   */
  const getSubsidyAmount = (data) => {
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid data');
    }
    const {
      trialSubsidyType,
      subsidyAmount,
      maleSubsidyAmount,
      femaleSubsidyAmount
    } = data;
    switch (trialSubsidyType) {
      case '1':
        return subsidyAmount + '元'; // 当 trialSubsidyType 为 1 时，返回 subsidyAmount
      case '2':
        return `男：${maleSubsidyAmount} 元，女：${femaleSubsidyAmount} 元`; // 当 trialSubsidyType 为 2 时，返回男和女的补助金额
      default:
        return null; // 处理其他类型或未定义的情况
    }
  };

  defineExpose({ reload });
</script>
<style lang="scss">
  body {
    font-size: 16px; /* 设置全局字体大小为16px */
  }
</style>
<style lang="scss" scoped>
  .d-flex {
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;

    // height: 62vh;
  }
  .ellipsis {
    display: -webkit-box; /* 使用 flexbox 布局 */
    -webkit-box-orient: vertical; /* 垂直排列子元素 */
    -webkit-line-clamp: 1; /* 显示的行数 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
  .main-card {
    flex: 1;
    width: 100%;
    height: 0px;
    padding-top: 12px;
    box-sizing: border-box;
    overflow: auto;
  }
  .card {
    background: #ffffff;
    box-shadow: 0px 4px 6px 0px #e5e7eb;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #ecf2f9;
    margin-bottom: 10px;
    transition: border 0.3s;
    cursor: pointer; /* 更改鼠标指针为手形 */
    &:hover {
      border-color: #0075ff;
    }
  }
  /* 列表样式 */
  .list-item {
    display: flex;
    align-items: center;
    padding: 16px 8px;
    .list-item-avatar {
      flex: 1;
      display: flex;
      align-items: center;
      :deep(.el-avatar) {
        flex-shrink: 0;
      }
      .list-item-avatar-extra {
        flex: 1;
        padding-left: 12px;
        box-sizing: border-box;
      }
    }
    & > div + div {
      margin-left: 20px;
      flex-shrink: 0;
    }
    .list-item-owner {
      width: 80px;
      color: #5b5b64;
      font-size: 16px;
    }
    .list-item-time {
      width: 160px;
    }
    .list-item-progress {
      width: 180px;
    }
    .list-item-title {
      margin-bottom: 4px;
    }
    .list-item-tools {
      display: flex;
      align-items: center;
    }
  }
  /* 响应式 */
  @media screen and (max-width: 1340px) {
    .list-item {
      & > div + div {
        margin-left: 10px;
      }
      .list-item-owner {
        width: 70px;
        color: #5b5b64;
        font-size: 16px;
      }
      .list-item-time {
        width: 140px;
      }
      .list-item-progress {
        width: 100px;
      }
    }
  }
  @media screen and (max-width: 1100px) {
    .list-item {
      display: block;
      .list-item-owner,
      .list-item-time,
      .list-item-progress {
        width: 100%;
        margin: 8px 0 0 0;
        display: flex;
        align-items: center;
      }
      .list-item-title {
        margin: 0;
        width: 80px;
      }
      .list-item-tools {
        margin-top: 8px;
        justify-content: flex-end;
      }
    }
  }

  .t-title {
    color: #5b5b64;
    font-size: 15px;
  }

  .tt-title {
    color: #5b5b64;
    font-size: 16px;
  }

  .unread-count {
    z-index: 1000;
    position: absolute;
    top: -16px;
    right: -16px;
    background-color: #ff4d4f; /* 红色背景 */
    color: white; /* 白色字体 */
    border-radius: 50%; /* 圆形 */
    padding: 2px 6px; /* 内边距 */
    font-size: 12px; /* 字体大小 */
    min-width: 10px; /* 最小宽度 */
    text-align: center; /* 文本居中 */
  }
</style>
