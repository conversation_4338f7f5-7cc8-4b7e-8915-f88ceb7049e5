<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="500"
    :model-value="modelValue"
    title="标记不适宜"
    @update:modelValue="updateModelValue"
    style="margin-top: 200px"
  >
    <div class="modal-dialog">
      <el-form
        :model="userInfo"
        ref="formRule"
        :rules="rules"
        label-width="auto"
        label-position="left"
      >
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="姓名" style="width: 100%">
              <el-input
                v-model="userInfo.name"
                placeholder="请输入姓名"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="userInfo.filterno">
            <el-form-item label="筛选号" style="width: 100%">
              <el-input
                v-model="userInfo.filterno"
                placeholder="请输入筛选号"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="userInfo.joinno">
            <el-form-item label="入组号" style="width: 100%">
              <el-input
                v-model="userInfo.joinno"
                placeholder="请输入入组号"
                clearable
                style="width: 100%"
                :disabled="true"
                maxlength="99"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="latestData">
            <el-form-item
              label="有效期至"
              prop="indate"
              style="width: 100%"
              class="formRequired"
            >
              <el-date-picker
                v-model="userInfo.indate"
                type="date"
                placeholder="请选择有效期"
                clearable
                style="width: 100%"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="!latestData">
            <el-form-item
              label="有效期（月）"
              prop="indateType"
              style="width: 100%"
              class="formRequired"
            >
              <el-select
                v-model="userInfo.indateType"
                placeholder="请选择有效期"
                clearable
                style="width: 100%"
              >
                <el-option label="3个月" value="9" />
                <el-option label="6个月" value="8" />
                <el-option label="9个月" value="7" />
                <el-option label="12个月" value="6" />
                <el-option label="24个月" value="5" />
                <el-option label="36个月" value="4" />
                <el-option label="48个月" value="3" />
                <el-option label="60个月" value="2" />
                <el-option label="永久" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="标记原因"
              prop="explain"
              style="width: 100%"
              class="formRequired"
            >
              <el-input
                v-model="userInfo.explain"
                type="textarea"
                :autosize="{
                  minRows: 3,
                  maxRows: 6
                }"
                placeholder="请输入标记原因"
                clearable
                style="width: 100%"
                show-word-limit
                maxlength="500"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="密码确认"
              prop="password"
              style="width: 100%"
              class="formRequired"
            >
              <el-input
                v-model="userInfo.password"
                type="password"
                autocomplete="new-password"
                placeholder="请输入密码确认"
                clearable
                style="width: 100%"
                maxlength="16"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submit"
          >确 定</el-button
        >
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref } from 'vue';
  import { encrypt } from '@/utils/jsencrypt';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import {
    markBlacklist,
    subjectblackLatest,
    subjectblackUpdate
  } from '@/api/subjects/studyManage/index.js';
  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Object
  });
  const disabledDate = (time) => {
    const maxDate = new Date('9999-12-31');
    return time.getTime() < Date.now() || time.getTime() > maxDate.getTime(); // - 8.64e7是今天可以选
  };
  const rules = ref({
    indate: [
      { required: true, message: '请选择有效期限', trigger: ['blur', 'change'] }
    ],
    indateType: [
      { required: true, message: '请选择有效期限', trigger: ['blur', 'change'] }
    ],
    explain: [
      { required: true, message: '请输入标记原因', trigger: ['blur', 'change'] }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: ['blur', 'change'] }
    ]
  });
  const loading = ref(false);
  const userInfo = ref({});
  const close = () => {
    updateModelValue(false);
  };
  const formRule = ref(null);

  const submit = () => {
    formRule.value.validate((valid) => {
      if (valid) {
        loading.value = true;
        const data = {
          trialsGuid: userInfo.value.trialsGuid,
          subjectsGuid: userInfo.value.subjectsGuid,
          explain: userInfo.value.explain,
          indateType: userInfo.value.indateType,
          password: encrypt(userInfo.value.password)
        };
        const api = latestData.value ? subjectblackUpdate : markBlacklist;
        if (latestData.value) {
          data.indate = userInfo.value.indate;
          data.guid = userInfo.value.guid;
        }
        api(data).then((res) => {
          loading.value = false;

          if (res.code == 200) {
            EleMessage.success('标记成功');
            emit('done');
            updateModelValue(false);
            return;
          }
          EleMessage.error(res.msg);
        });

        return;
      }
    });
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const latestData = ref(true);
  const getsubjectblackLatest = () => {
    const data = {
      trialsGuid: userInfo.value.trialsGuid,
      subjectsGuid: userInfo.value.subjectsGuid
    };
    subjectblackLatest(data).then((res) => {
      if (res.data) {
        latestData.value = true;
        userInfo.value = {
          ...userInfo.value,
          ...res.data
        };
        return;
      }
      latestData.value = false;
    });
  };
  nextTick(async () => {
    userInfo.value = JSON.parse(JSON.stringify(props.data));
    getsubjectblackLatest();
  });
</script>
<style scoped>
  :deep(.formRequired .el-form-item__label) {
    transform: translateX(-10px);
  }
</style>
