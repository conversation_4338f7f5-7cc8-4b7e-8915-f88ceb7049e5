<template>
  <div>
    <ele-modal
      form
      width="1250px"
      :model-value="modelValue"
      close-on-click-modal
      title="详情"
      @update:modelValue="updateModelValue"
      class="simplebar-modal"
      center
    >
      <div
        data-simplebar
        style="
          max-height: 560px;
          overflow-y: auto;
          overflow-x: hidden;
          padding: 0 15px;
        "
      >
        <el-descriptions border>
          <template #title>
            <div>试验项目信息 </div>
          </template>
          <el-descriptions-item
            label="方案编号"
            label-align="right"
            width="170"
          >
            {{ detailInfo.schemeCode }}
          </el-descriptions-item>
          <el-descriptions-item
            label="试验编号"
            label-align="right"
            width="170"
            :span="2"
          >
            {{ detailInfo.code }}
          </el-descriptions-item>
          <el-descriptions-item
            label="试验名称"
            label-align="right"
            width="170"
            :span="3"
          >
            {{ detailInfo.name }}
          </el-descriptions-item>
          <el-descriptions-item
            label="试验类别"
            label-align="right"
            width="170"
          >
            {{ detailInfo.type }}
          </el-descriptions-item>
          <el-descriptions-item
            label="志愿者类别"
            label-align="right"
            width="170"
          >
            {{
              detailInfo.subjectType == 1
                ? '健康受试者'
                : detailInfo.subjectType == 2
                  ? '患者受试者'
                  : ''
            }}
          </el-descriptions-item>
          <el-descriptions-item
            label="试验周期"
            label-align="right"
            width="170"
          >
            <span class="numberStyle">{{ detailInfo.cycle }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="药品类别"
            label-align="right"
            width="170"
          >
            {{ detailInfo.drugtype }}
          </el-descriptions-item>
          <el-descriptions-item
            label="给药途径"
            label-align="right"
            width="170"
          >
            {{ detailInfo.route }}
          </el-descriptions-item>
          <el-descriptions-item
            label="采血量分级"
            label-align="right"
            width="170"
          >
            {{ detailInfo.drawblood }}
          </el-descriptions-item>
          <el-descriptions-item label="专业" label-align="right" width="170">
            {{ detailInfo.professional }}
          </el-descriptions-item>
          <el-descriptions-item label="半衰期" label-align="right" width="170">
            {{ detailInfo.halflife }}
          </el-descriptions-item>
          <el-descriptions-item label="病种" label-align="right" width="170">
            {{ detailInfo.illness }}
          </el-descriptions-item>
          <el-descriptions-item
            label="试验计划开始时间"
            label-align="right"
            width="170"
          >
            <span class="numberStyle">{{ detailInfo.sdate }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="试验计划结束时间"
            label-align="right"
            width="170"
          >
            <span class="numberStyle">{{ detailInfo.edate }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="试验计划入组时间"
            label-align="right"
            width="170"
          >
            <span class="numberStyle">{{ detailInfo.joindate }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="下次试验间隔天数"
            label-align="right"
            width="170"
          >
            <span class="numberStyle">{{ detailInfo.interval }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="计划入组例数"
            label-align="right"
            width="170"
          >
            <span class="numberStyle">{{ detailInfo.plannum }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="是否为测试试验"
            label-align="right"
            width="170"
          >
            {{
              detailInfo.isTest == 0 ? '否' : detailInfo.isTest == 1 ? '是' : ''
            }}
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions border>
          <template #title>
            <div style="margin-top: 30px">申办方/CRO信息 </div>
          </template>
          <el-descriptions-item
            label="申办方公司名称"
            label-align="right"
            width="170"
          >
            {{ detailInfo.sponsorname }}
          </el-descriptions-item>
          <el-descriptions-item
            label="申办方联系人"
            label-align="right"
            width="170"
          >
            {{ detailInfo.sponsorcontact }}
          </el-descriptions-item>
          <el-descriptions-item
            label="申办方联系电话"
            label-align="right"
            width="170"
          >
            <span class="numberStyle">
              {{ $PhoneOrIdCrad(detailInfo.sponsorphone, 'phone') }}</span
            >
          </el-descriptions-item>
          <el-descriptions-item
            label="CRO公司名称"
            label-align="right"
            width="170"
          >
            {{ detailInfo.croname }}
          </el-descriptions-item>
          <el-descriptions-item
            label="CRO联系人"
            label-align="right"
            width="170"
          >
            {{ detailInfo.crocontact }}
          </el-descriptions-item>
          <el-descriptions-item
            label="CRO联系电话"
            label-align="right"
            width="170"
          >
            <span class="numberStyle">
              {{ $PhoneOrIdCrad(detailInfo.crophone, 'phone') }}</span
            >
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions border>
          <template #title>
            <div style="margin-top: 30px">筛选条件信息 </div>
          </template>
          <el-descriptions-item
            label="距上次给药天数"
            label-align="right"
            width="170"
          >
            <span class="numberStyle"> {{ detailInfo.doseday }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="距上次访视天数"
            label-align="right"
            width="170"
          >
            <span class="numberStyle"> {{ detailInfo.trialday }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label="距上次筛选天数"
            label-align="right"
            width="170"
          >
            <span class="numberStyle"> {{ detailInfo.filterday }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="footer-btn flexCenter">
          <el-button type="info" @click="updateModelValue(false)"
            >取 消</el-button
          >
        </div>
      </template>
    </ele-modal>
  </div>
</template>

<script setup>
  import { ref, watch, nextTick } from 'vue';
  import { trialsProjectInfo } from '@/api/subjects/testProject/index.js';
  const emit = defineEmits(['done', 'update:modelValue']);
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    data: Object
  });
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const detailInfo = ref({});
  const getDetailInfo = () => {
    if (props.data.guid) {
      trialsProjectInfo(props.data.guid).then((res) => {
        detailInfo.value = res.data;
      });
    }
  };
  nextTick(async () => {
    await getDetailInfo();
  });
  watch(
    () => props.modelValue,
    async (modelValue) => {
      if (modelValue) {
        await getDetailInfo();
      }
    }
  );
</script>

<style>
  .descriptions_dialog .el-descriptions__title {
    width: 100%;
  }
  .descriptions_dialog
    .el-descriptions__body
    .el-descriptions__table.is-bordered
    .el-descriptions__cell {
    padding: 8px 5px !important;
  }
</style>
