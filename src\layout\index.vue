<template>
  <ele-watermark
    :content="watermarkText"
    :width="300"
    :height="50"
    :font="{ color: 'rgba(0,0,0,.13)' }"
    style="height: 100%; width: 100%; z-index: 1"
  >
    <ele-pro-layout
      class="main-layout"
      :menus="menus"
      :tabs="tabs"
      :collapse="collapse"
      :compact="compact"
      :maximized="maximized"
      :tab-bar="tabBar ? (tabInHeader ? 'header' : true) : false"
      :breadcrumb="showBreadcrumb"
      :layout="layout"
      :sidebar-layout="sidebarLayout"
      :header-style="headerStyle"
      :sidebar-style="sidebarStyle"
      :style="{
        '--ele-header-height': headerHeight + 'px',
        '--ele-sidebar-width': sidebarWidth + 'px',
        '--ele-sidebox-width': sideboxWidth + 'px',
        '--ele-sidebar-mix-width': sidebarMixWidth + 'px',
        '--el-font-size-base': fontSize + 'px',
        '--el-menu-item-font-size': fontSize + 'px'
      }"
      :tab-style="tabStyle"
      :fixed-header="fixedHeader"
      :fixed-sidebar="fixedSidebar"
      :fixed-body="fixedBody"
      :fluid="fluid"
      :logo-in-header="logoInHeader"
      :colorful-icon="colorfulIcon"
      :unique-opened="uniqueOpened"
      :fixed-home="fixedHome"
      :home-path="HOME_PATH"
      :redirectPath="REDIRECT_PATH"
      :locale="locale"
      :menuSearch="menuSearch"
      :i18n="i18n"
      :tab-sortable="!mobileDevice"
      :tab-context-menu="{
        iconProps: { size: 15 },
        popperOptions: {
          strategy: 'fixed',
          modifiers: [{ name: 'offset', options: { offset: [0, 8] } }]
        }
      }"
      :tab-context-menus="tabContext"
      :nav-trigger="layout === 'top' ? void 0 : menuItemTrigger"
      :box-trigger="menuItemTrigger"
      :keep-alive="TAB_KEEP_ALIVE"
      :transition-name="transitionName"
      :ellipsis-props="{ hideTimeout: 800 }"
      :responsive="responsive"
      @update:collapse="updateCollapse"
      @update:maximized="updateMaximized"
      @tabAdd="addPageTab"
      @tabClick="handleTabClick"
      @tabRemove="removePageTab"
      @tabContextMenu="handleTabContextMenu"
      @tabSortChange="setPageTabs"
      @bodySizeChange="handleBodySizeChange"
    >
      <router-layout />
      <template #logo="{ sidebar }">
        <div
          style="
            display: flex;
            align-items: center;
            box-sizing: border-box;
            transition: width 0.2s;
          "
        >
          <img src="@/assets/logo_left.svg" style="transform: translateY(-1px)" />
        </div>
        <span style="margin-left: 10px; color: #ffffff">{{
          userStore.info.tenantName || ''
        }}</span>
      </template>
      <!-- 顶栏左侧按钮 -->
      <template #left>
        <!-- 折叠侧栏 -->
        <layout-tool
          class="hidden-md-and-up"
          @click="updateCollapse(!collapse)"
        >
          <el-icon style="transform: scale(1.14)">
            <MenuUnfoldOutlined v-if="collapse" />
            <MenuFoldOutlined v-else />
          </el-icon>
        </layout-tool>
      </template>
      <template #bottom>
        <!-- 折叠侧栏 -->
        <!-- <layout-tool
        @click="updateCollapse(!collapse)"
        class="hidden-sm-and-down"
      >
        <el-icon style="transform: scale(1.14)">
          <MenuUnfoldOutlined v-if="collapse" />
          <MenuFoldOutlined v-else />
        </el-icon>
      </layout-tool> -->
      </template>
      <!-- 顶栏右侧按钮 -->
      <template #right>
        <header-tool @click="manual">
          <el-tooltip
            effect="dark"
            content="用户手册"
            placement="top-start"
            :show-after="100"
          >
            <img
              src="@/assets/index/manual.svg"
              alt=""
              style="width: 22px; height: 22px"
            />
          </el-tooltip>
        </header-tool>

        <header-tool @click="question">
          <el-tooltip
            effect="dark"
            content="常见问题"
            placement="top-start"
            :show-after="100"
          >
            <img
              src="@/assets/index/question.png"
              alt=""
              style="width: 22px; height: 22px"
            />
          </el-tooltip>
        </header-tool>

        <!-- 待办 -->
        <header-tool @click="getBacklogList(), (showBacklog = true)">
          <el-tooltip
            effect="dark"
            content="待办事项"
            placement="top-start"
            :show-after="100"
          >
            <div
              style="
                position: relative;
                width: 22px;
                height: 22px;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <img
                src="@/assets/index/bell.png"
                alt=""
                style="width: 22px; height: 22px"
              />
              <div
                class="showBell"
                v-if="userStore.backlogList.length > 0"
              ></div>
            </div>
          </el-tooltip>
        </header-tool>

        <header-tool>
          <el-tooltip
            effect="dark"
            content="客服热线：010-86463913转3"
            placement="top-start"
            :show-after="100"
          >
            <img
              src="@/assets/index/service.png"
              alt=""
              style="width: 22px; height: 22px"
            />
          </el-tooltip>
        </header-tool>
        <!-- 用户信息 -->
        <header-tool>
          <header-user />
        </header-tool>
        <!-- 夜间模式 -->
        <!-- <header-tool ref="darkSwitchRef" class="dark-switch">
        <el-switch
          :active-action-icon="MoonOutlined"
          :inactive-action-icon="SunOutlined"
          :model-value="darkMode"
          @update:modelValue="updateDarkMode"
        />
      </header-tool> -->
      </template>
      <!-- 页签栏右侧下拉菜单 -->
      <template v-if="tabBar && !tabInHeader" #tabExtra="{ active }">
        <tab-dropdown
          :items="tabExtra"
          :dropdown-props="{
            iconProps: { size: 15 },
            popperOptions: {
              strategy: 'fixed',
              modifiers: [{ name: 'offset', options: { offset: [12, 8] } }]
            }
          }"
          @menuClick="(key) => handleTabDropdownMenu(key, active)"
        />
      </template>
      <!-- 折叠双侧栏一级 -->
      <template #boxBottom>
        <div :style="{ flexShrink: 0, padding: roundedTheme ? '4px 8px' : 0 }">
          <layout-tool style="height: 32px" @click="updateCompact(!compact)">
            <el-icon style="transform: scale(1.05)">
              <MenuUnfoldOutlined v-if="compact" />
              <MenuFoldOutlined v-else />
            </el-icon>
          </layout-tool>
        </div>
      </template>
      <!-- 全局页脚 -->
      <!-- <template #footer>
      <page-footer />
    </template> -->
      <!-- 菜单图标 -->
      <template #icon="{ icon, item }">
        <el-icon v-if="icon" v-bind="item.meta?.props?.iconProps || {}">
          <component :is="icon" :style="item.meta?.props?.iconStyle" />
        </el-icon>
      </template>
      <!-- 页签标题 -->
      <template #tabTitle="{ label, item }">
        <el-icon
          v-if="item.meta?.icon"
          class="ele-tab-icon"
          v-bind="item.meta?.props?.iconProps || {}"
        >
          <component
            :is="item.meta.icon"
            :style="item.meta?.props?.iconStyle"
          />
        </el-icon>
        <span :style="item.meta?.icon ? { paddingLeft: '4px' } : {}">
          {{ label }}
        </span>
      </template>
    </ele-pro-layout>
    <el-dialog
      v-model="showBacklog"
      title="待办事项"
      width="1000"
      :before-close="handleClose"
      align-center
    >
      <el-form inline :model="params">
        <el-form-item label="关键词：">
          <el-input
            v-model="params.keywords"
            placeholder="请输入关键词"
            clearable
            style="width: 200px"
            maxlength="99"
          />
        </el-form-item>
        <el-form-item style="transform: translateX(-15px)">
          <el-button class="primary" plain @click="keywords = params.keywords"
            >搜索</el-button
          >
          <el-button
            class="primary" plain
            style="background-color: #ffffff;border-color: #94aed9;color: #718ebf;"
            @click="(params = { keywords: '' }), (keywords = '')"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        height="50vh"
        :data="
          userStore.backlogList.filter(
            (val) =>
              (val.name || '').includes(keywords) ||
              (val.code || '').includes(keywords) ||
              (val.schemecode || '').includes(keywords)
          )
        "
        style="width: 100%"
        :header-cell-style="{
          backgroundColor: '#f3f6ff',
          color: '#131314'
        }"
        :row-style="tableRowStyle"
      >
        <template #empty>
          <el-empty description="暂无数据" />
        </template>
        <el-table-column type="index" label="序号" width="65" />
        <el-table-column
          prop="code"
          label="试验编号"
          width="160"
          show-overflow-tooltip
        />
        <el-table-column
          prop="schemecode"
          label="方案编号"
          width="160"
          show-overflow-tooltip
        />
        <el-table-column prop="name" label="试验名称" show-overflow-tooltip />
        <el-table-column
          prop="waitJoinNumber"
          label="待入组人数"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column label="操作" width="80">
          <template #default="{ row }">
            <el-space>
              <el-link type="primary" @click="markJoin(row)">标记</el-link>
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 主题设置抽屉 -->
    <setting-drawer v-model="settingVisible" />
    <joinModal v-model="showJoin" :data="selectList" v-if="showJoin" />
  </ele-watermark>
</template>

<script setup>
  import joinModal from '@/views/subjects/studyManage/components/joinModal.vue';
  import watermark from '@/utils/warterMark.js';
  import { ref, computed, markRaw, onMounted, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import { useI18n } from 'vue-i18n';
  import {
    HeaderTool,
    LayoutTool,
    TabDropdown,
    requestFullscreen,
    exitFullscreen,
    checkFullscreen,
    EleMessage
  } from '@hnjing/zxzy-admin-plus/es';
  import {
    MenuFoldOutlined,
    MenuUnfoldOutlined,
    ReloadOutlined,
    ExpandOutlined,
    CompressOutlined,
    MoreOutlined,
    CloseOutlined,
    ArrowLeftOutlined,
    ArrowRightOutlined,
    MinusCircleOutlined,
    CloseCircleOutlined
  } from '@/components/icons';
  import {
    PROJECT_NAME,
    HOME_PATH,
    REDIRECT_PATH,
    TAB_KEEP_ALIVE
  } from '@/config/setting';
  import { useUserStore } from '@/store/modules/user';
  import { useThemeStore } from '@/store/modules/theme';
  import { useMobileDevice } from '@/utils/use-mobile';
  import { usePageTab } from '@/utils/use-page-tab';
  import RouterLayout from '@/components/RouterLayout/index.vue';
  import HeaderUser from './components/header-user.vue';
  import HeaderNotice from './components/header-notice.vue';
  import I18nIcon from './components/i18n-icon.vue';
  import PageFooter from './components/page-footer.vue';
  import SettingDrawer from './components/setting-drawer.vue';
  import * as MenuIcons from './menu-icons';
  import SetFontSize from './components/set-font-size.vue';
  import {
    selectWaitJoinStatistic,
    markSubjectTrialList
  } from '@/api/subjects/studyManage/index.js';
  import { ElNotification } from 'element-plus';

  defineOptions({
    name: 'Layout',
    components: MenuIcons
  });

  const { push } = useRouter();
  const { t, locale } = useI18n();
  const {
    addPageTab,
    removePageTab,
    removeAllPageTab,
    removeLeftPageTab,
    removeRightPageTab,
    removeOtherPageTab,
    reloadPageTab,
    setPageTabs
  } = usePageTab();
  const { mobileDevice } = useMobileDevice();
  const userStore = useUserStore();
  const themeStore = useThemeStore();
  // 用户信息
  const info = computed(() => userStore.info);

  /** 菜单数据 */
  const { menus } = storeToRefs(userStore);

  /** 布局风格 */
  const {
    tabs,
    collapse,
    compact,
    maximized,
    tabBar,
    layout,
    sidebarLayout,
    headerStyle,
    sidebarStyle,
    headerHeight,
    sidebarWidth,
    sideboxWidth,
    sidebarMixWidth,
    tabStyle,
    fixedHeader,
    fixedSidebar,
    fixedBody,
    fluid,
    logoInHeader,
    colorfulIcon,
    transitionName,
    uniqueOpened,
    fixedHome,
    tabInHeader,
    fontSize,
    roundedTheme,
    menuSearch,
    menuItemTrigger,
    responsive,
    showBreadcrumb
  } = storeToRefs(themeStore);

  /** 是否全屏 */
  const isFullscreen = ref(false);

  /** 是否显示主题设置抽屉 */
  const settingVisible = ref(false);
  const showBacklog = ref(false);
  const showJoin = ref(false);
  const selectList = ref([]);
  const params = ref({
    keywords: ''
  });
  const keywords = ref('');
  const handleClose = () => {
    keywords.value = '';
    showBacklog.value = false;
  };
  const markJoin = async (row) => {
    selectList.value = await markSubjectTrialList({
      type: '2',
      trialsGuid: row.trialsGuid
    });
    showJoin.value = true;
  };
  /** 页签右键菜单 */
  const tabContext = computed(() => {
    return [
      {
        title: t('layout.tabs.reload'),
        command: 'reload',
        icon: markRaw(ReloadOutlined),
        iconStyle: { transform: 'scale(0.98)' }
      },
      {
        title: t('layout.tabs.close'),
        command: 'close',
        icon: markRaw(CloseOutlined)
      },
      {
        title: t('layout.tabs.closeLeft'),
        command: 'left',
        icon: markRaw(ArrowLeftOutlined),
        divided: true
      },
      {
        title: t('layout.tabs.closeRight'),
        command: 'right',
        icon: markRaw(ArrowRightOutlined)
      },
      {
        title: t('layout.tabs.closeOther'),
        command: 'other',
        icon: markRaw(MinusCircleOutlined),
        divided: true
      },
      {
        title: t('layout.tabs.closeAll'),
        command: 'all',
        icon: markRaw(CloseCircleOutlined)
      }
    ];
  });

  // 用户手册
  const manual = () => {
    window.open('https://iscreen.gcpdata.cn/user_manual.pdf');
  };

  // 常见问题
  const question = () => {
    window.open('/question');
  };

  // 待办事项
  const getBacklogList = () => {
    selectWaitJoinStatistic({}).then((res) => {
      userStore.backlogList = res.data;
    });
  };

  // 驱动安装说明
  const install = () => {
    window.open('/install');
  };

  /** 页签栏右侧下拉菜单 */
  const tabExtra = computed(() => {
    const isMax = maximized.value;
    return [
      {
        title: t(`layout.tabs.${isMax ? 'fullscreenExit' : 'fullscreen'}`),
        command: 'fullscreen',
        icon: isMax ? markRaw(CompressOutlined) : markRaw(ExpandOutlined)
      },
      ...tabContext.value
    ];
  });

  /** 侧栏折叠切换 */
  const updateCollapse = (value) => {
    themeStore.setCollapse(value);
  };

  /** 双侧栏一级折叠切换 */
  const updateCompact = (value) => {
    themeStore.setCompact(value);
  };

  /** 内容区全屏切换 */
  const updateMaximized = (value) => {
    themeStore.setMaximized(value);
  };

  /** 页签点击事件 */
  const handleTabClick = (option) => {
    const { key, active, item } = option;
    const path = item?.fullPath || key;
    if (key !== active && path) {
      push(path);
    }
  };

  /** 内容区尺寸改变事件 */
  const handleBodySizeChange = ({ width }) => {
    themeStore.setContentWidth(width ?? null);
    isFullscreen.value = checkFullscreen();
  };

  /** 全屏切换 */
  const toggleFullscreen = () => {
    if (isFullscreen.value) {
      exitFullscreen();
      isFullscreen.value = false;
      return;
    }
    try {
      requestFullscreen();
      isFullscreen.value = true;
    } catch (e) {
      console.error(e);
      EleMessage.error('您的浏览器不支持全屏模式');
    }
  };

  /** 页签右键菜单点击事件 */
  const handleTabContextMenu = (option) => {
    const { command, key, item, active } = option;
    if (command === 'reload') {
      reloadPageTab({ fullPath: item?.fullPath || key });
    } else if (command === 'close') {
      removePageTab({ key, active });
    } else if (command === 'left') {
      removeLeftPageTab({ key, active });
    } else if (command === 'right') {
      removeRightPageTab({ key, active });
    } else if (command === 'other') {
      removeOtherPageTab({ key, active });
    } else if (command === 'all') {
      removeAllPageTab({ key, active });
    }
  };

  /** 页签栏右侧下拉菜单点击事件 */
  const handleTabDropdownMenu = (command, active) => {
    if (command === 'reload') {
      reloadPageTab();
    } else if (command === 'fullscreen') {
      updateMaximized(!maximized.value);
    } else {
      handleTabContextMenu({ command, key: active, active });
    }
  };

  /** 菜单标题国际化 */
  const i18n = ({ menu, locale }) => {
    if (locale && menu?.meta?.lang && menu.meta.lang[locale]) {
      return menu.meta.lang[locale];
    }
    return menu?.component ? void 0 : menu?.meta?.title;
  };

  /** 打开主题设置抽屉 */
  const openSetting = () => {
    settingVisible.value = true;
  };
  const getBacklog = () => {
    if (userStore.initShow != 0) {
      return;
    }
    selectWaitJoinStatistic({}).then((res) => {
      userStore.backlogList = res.data;

      userStore.initShow++;
      if (userStore.backlogList.length == 0) {
        return;
      }
      // 定义查看详情要调用的方法
      function handleViewDetails() {
        params.value.keywords = '';
        getBacklogList();
        // ElNotification.closeAll();
        showBacklog.value = true;
        // 这里添加具体的业务逻辑
      }

      ElNotification({
        title: '待办事项',
        dangerouslyUseHTMLString: true,
        message: `
          <div class="custom_Notification_Content">
            <div>您有${userStore.backlogList.length}个项目存在待入组状态的志愿者，请及时处理</div>
            <div id="view-details" style="color: #5280FB;text-align: right;cursor: pointer;">查看详情</div>
          </div>
        `,
        duration: 0,
        type: 'warning',
        position: 'bottom-right',
        customClass: 'custom_Notification'
      });
      nextTick(() => {
        const viewDetailsElement = document.getElementById('view-details');
        if (viewDetailsElement) {
          viewDetailsElement.addEventListener('click', handleViewDetails);
        }
      });
      setTimeout(() => {
        // ElNotification.closeAll();
      }, 30000);
    });
  };
  const watermarkText = ref('');
  onMounted(() => {
    console.log('watermarkText', info.value);
    // watermarkText.value = `${info.value.tenantName} ${info.value.nickName} ${info.value.userName}`;
    watermarkText.value = [
      info.value.tenantName,
      info.value.nickName,
      info.value.userName
    ];
    getBacklog();
  });
</script>
<style lang="scss" scoped>
  .dark-switch {
    position: relative;

    :deep(.el-switch) {
      height: 22px;
      line-height: 22px;
      position: static;

      .el-switch__core {
        --el-switch-off-color: var(--el-border-color-extra-light);
        --el-switch-on-color: var(--el-border-color-extra-light);
        height: 22px;
        border-radius: 11px;
        border: none;
        background: rgba(255, 255, 255, 0.38);

        .el-switch__action {
          color: var(--el-text-color-regular);
          background: var(--el-bg-color);
          width: 18px;
          height: 18px;
          font-size: 12px;
          left: 2.65px;
        }
      }

      &.is-checked .el-switch__core .el-switch__action {
        left: calc(100% - 20px);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }
    }
  }
  .ele-menu > li.el-sub-menu > .el-sub-menu__title .el-icon {
    background: none !important;
    font-size: 20px !important;
  }
  .el-menu-item > .el-icon {
    background: none !important;
    font-size: 20px !important;
  }
  .el-menu-item > .el-menu-tooltip__trigger > .el-icon {
    background: none !important;
    font-size: 20px !important;
  }
  .showBell {
    width: 8px;
    height: 8px;
    background: red;
    border-radius: 50%;
    position: absolute;
    top: -2px;
    right: 0;
  }
</style>
<style>
  .custom_Notification {
    background: #fff7e8 !important;
    border: 1px solid #ffaf4d !important;
  }
  .custom_Notification .el-notification__title {
    font-family:
      Microsoft YaHei,
      Microsoft YaHei;
    font-weight: 400;
    font-size: 16px;
    color: #ffaf4d !important;
  }
  .custom_Notification .custom_Notification_Content {
    font-family:
      Microsoft YaHei,
      Microsoft YaHei;
    font-weight: 400;
    font-size: 14px;
    color: #ffaf4d;
  }
  .el-notification.right {
    z-index: 99 !important;
  }
  .el-notification__closeBtn {
    top: -13px !important;
    right: -10px !important;
  }
  .ele-admin-sidebar .el-menu .is-active .el-icon:first-child {
    color: #696ed8 !important;
  }

  .ele-admin-sidebar
    .el-menu
    .is-active
    .el-sub-menu__title
    .el-icon:first-child {
    color: #ffffff !important;
  }

  .ele-admin-sidebar .el-menu .is-active .el-icon:first-child {
    color: red !important;
    background: red;
  }
</style>
