<!-- eslint-disable vue/no-deprecated-slot-attribute -->
<template>
  <div v-if="props.items" class="moreBtn">
    <div v-if="props.items.length > props.maxLength" class="btn-box">
      <el-space>
        <el-link
          v-for="(el, i) in showBtn"
          :key="i"
          type="primary"
          :disabled="el.disabled"
          @click="routeEvent(props.operateItem, el)"
          style="margin-right: 10px; color: #507aff"
        >
          <span> {{ el.name }}</span>
        </el-link>

        <el-dropdown trigger="hover" @command="handleCommand">
          <el-link
            type="primary"
            style="
              color: #507aff;
              cursor: pointer;
              transform: translateY(0.5px);
              font-size: 16px;
            "
          >
            <span>更多</span>
          </el-link>

          <template #dropdown>
            <el-dropdown-menu
              slot="dropdown"
              class="table-opetation-more-dropdown"
            >
              <el-dropdown-item
                v-for="(item, index) in dropData"
                :key="index"
                :command="beforeHandleCommand(props.operateItem, item)"
              >
                <div
                  style="
                    color: #507aff;
                    cursor: pointer;
                    text-align: center;
                    width: 100%;
                  "
                >
                  {{ item.name }}</div
                >
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </el-space>
    </div>
    <div v-else style="display: flex">
      <el-space>
        <el-link
          type="primary"
          :disabled="item.disabled"
          @click="routeEvent(operateItem, item)"
          v-for="(item, index) in props.items"
          :key="index"
          style="margin-right: 10px; color: #507aff"
        >
          <span> {{ item.name }}</span>
        </el-link>
      </el-space>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  const props = defineProps({
    items: {
      type: Array,
      default: () => {
        return [];
      }
    },
    operateItem: {
      type: Object,
      default: () => {}
    },
    maxLength: {
      type: Number,
      default: () => 2
    }
  });

  const dropData = ref([]);
  const showBtn = ref([]);

  onMounted(() => {
    if (props.items.length > 0 && props.items.length > props.maxLength) {
      // 取除了第一个元素之外的其他元素
      showBtn.value = props.items.slice(0, props.maxLength);
      dropData.value = props.items.slice(props.maxLength);
    }
  });
  const emit = defineEmits(['routeEvent']);
  // 正常按钮点击事件
  const routeEvent = (data, name) => {
    emit('routeEvent', data, name);
  };
  const beforeHandleCommand = (data, item) => {
    return {
      data: data,
      btn: item
    };
  };
  // 下拉菜单点击事件
  const handleCommand = (command) => {
    routeEvent(command.data, command.btn);
  };
</script>
<style lang="scss" scoped>
  .btn-box {
    display: flex;
    flex-wrap: wrap;
    .el-button {
      min-width: 0px;
      margin-right: 12px;
    }
  }

  .el-dropdown-link {
    vertical-align: text-top;
  }

  .el-dropdown {
    vertical-align: middle;
  }
</style>
