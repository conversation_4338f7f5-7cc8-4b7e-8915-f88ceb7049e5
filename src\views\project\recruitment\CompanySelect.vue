<template>
  <el-select
    v-model="inputValue"
    v-bind="$attrs"
    filterable
    remote
    placeholder="请输入关键字搜索公司"
    :remote-method="throttledSearch"
    :loading="loading"
    style="width: 100%"
    value-key="companyId"
    @change="onChange"
  >
    <el-option
      v-for="item in options"
      :key="item?.companyId"
      :label="item?.companyName"
      :value="item"
    />
  </el-select>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { addCompany, queryCompanyList } from '@/api/project/index';
  import _ from 'lodash';
  const emit = defineEmits(['change', 'update:modelValue']);
  const props = defineProps({
    value: {
      type: Object,
      default() {
        return null;
      }
    },
    modelValue: [String, Object, Array]
  });
  const loading = ref(false);
  const options = ref([]);
  const inputValue = ref({});

  watch(
    () => props.modelValue,
    (val) => {
      inputValue.value = val;
    },
    { deep: true, immediate: true }
  );

  watch(
    inputValue,
    (newVal) => {
      if (!newVal || !newVal.companyId) {
        // console.error('无效的输入值:', newVal);
        return;
      }
      const index = options.value.findIndex(
        (t) => t.companyId === newVal.companyId
      );
      if (index === -1) {
        if (options.value.length === 0) {
          options.value.push(newVal);
          emit('update:modelValue', newVal);
        } else {
          console.warn('未找到对应的公司ID，无法更新值:', newVal.companyId);
        }
      } else {
        emit('update:modelValue', options.value[index]);
      }
    },
    {
      deep: true,
      immediate: true
    }
  );

  let throttledSearch = _.debounce(async (val) => {
    try {
      if (!val) return;
      const result = await queryCompanyList({
        companyName: val
      });
      const data = result ?? [];
      options.value = data.map((t) => {
        return {
          companyId: t.companyId ?? _.uniqueId('new_'),
          companyName: t.companyName
        };
      });
    } catch (error) {
      console.error('请求失败:', error);
      return [];
    }
  }, 1000);

  const onChange = async (val) => {
    const option = {
      ...val
    };
    if (_.startsWith(val.companyId, 'new_')) {
      const result = await addCompany({
        companyName: val.companyName
      });
      const data = result.data;
      option.companyId = data;
      const index = options.value.findIndex(
        (t) => t.companyName == val.companyName
      );
      options.value[index] = option;
      emit('update:modelValue', option);
      emit('change', option);
    } else {
      emit('update:modelValue', val);
      emit('change', val);
    }
  };
  onMounted(() => {
    if (props.value && options.value.length == 0) {
      if (props.value.companyName.length > 0) {
        options.value.push(props.value);
      }
    }
  });
</script>
