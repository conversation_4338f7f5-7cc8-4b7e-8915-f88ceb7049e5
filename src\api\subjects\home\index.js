import request from '@/utils/subjectsrequest';

// 首页信息
export async function homePageList() {
  const res = await request.get('/home/<USER>/list');
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(res.data.message);
}

// 天眼查搜索公司
export async function companySearch(data) {
  // const res = await request.get('/sys/sponsor/company/search', {
  //   params
  // });
  // if (res.data.code === 200) {
  //   return res.data.data;
  // }
  // return res.data;
  const res = await request.post(`/sys/sponsor/company/search`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return res.data;
}

// 医院端-首页统计
export async function homeStatistic(data) {
  const res = await request.get(
    `/sys/organization/statistic?startDate=${data.startDate}&&endDate=${data.endDate}`
  );
  if (res.data.code === 200) {
    return res.data.data;
  }
  return res.data;
}

// 医院端-筛选统计
export async function filterStatistic() {
  const res = await request.get('/sys/organization/filterStatistic');
  if (res.data.code === 200) {
    return res.data.data;
  }
  return Promise.reject(res.data.message);
}

// 筛查人次详情
export async function filterSubjectsList(data) {
  const res = await request.post(`/sys/organization/filterSubjectsList`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return res.data;
}

// 入组人次详情
export async function joinSubjectsList(data) {
  const res = await request.post(`/sys/organization/joinSubjectsList`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return res.data;
}
