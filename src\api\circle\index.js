import request from '@/utils/request-business';

/**
 * 圈子管理
 * @param {*} data
 * @returns
 */
export async function getManagerPageList(params) {
  const res = await request.get('/social/getManagerPageList', { params });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 帖子下线
 * @param {*} data id
 * @returns
 */
export async function postOffLine(data) {
  const res = await request.post(`/social/postOffLine`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 评论下线
 * @param {*} data id
 * @returns
 */
export async function commentOffLine(data) {
  const res = await request.post(`/social/commentOffLine`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 帖子初审
 * @param {*} data
 * @returns
 */
export async function firstAudit(data) {
  const res = await request.post(`/social/firstAudit`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 帖子复审
 * @param {*} data
 * @returns
 */
export async function secondAudit(data) {
  const res = await request.post(`/social/secondAudit`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(res.data.msg);
}

/**
 * 圈子管理
 * @param {*} data
 * @returns
 */
export async function getPostManagerDetail(params) {
  const res = await request.get('/social/getPostManagerDetail', { params });
  if (res.data.code === 200) {
    return res.data?.data;
  }
  return Promise.reject(new Error(res.data.msg));
}

/**
 * 查看评论
 * @param {*} data
 * @returns
 */
export async function queryCommentManagerPageList(params) {
  const res = await request.get('/social/queryCommentManagerPageList', {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return Promise.reject(new Error(res.data.msg));
}
