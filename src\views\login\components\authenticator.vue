<template>
  <div class="page-index">
    <div id="page-content">
      <div class="container">
        <div class="page-content">
          <div class="info-box">
            <div class="info-icon-box">
              <el-link type="primary" @click="goBackToLogin" underline="none"
                ><el-icon class="back-icon"> <ArrowLeft /> </el-icon
                >返回登录页面</el-link
              >
            </div>
            <div class="info-message-box">
              <div class="title">绑定身份验证器</div>
              <div id="content" class="content">
                <p class="step-name">第一步：安装身份验证器</p>
                <p
                  >请在你的手机上安装身份验证器App。<el-link
                    type="primary"
                    @click="openDialog"
                    >点击此处获取安装方式</el-link
                  ></p
                >

                <p class="step-name">第二步：检查服务器时间</p>
                <p
                  >请注意检查您的手机时间与服务器时间是否同步，如果两者<span
                    class="important"
                    >时间偏差超过两分钟则无法绑定</span
                  >，请及时通知系统管理员处理！</p
                >
                <div class="time-box">
                  <i class="fa fa-clock-o"></i> 服务器时间：<span
                    class="tp-time mono"
                    id="DCS-time"
                    >{{ formattedTime }}</span
                  >
                </div>
                <p class="step-name">第三步：认证并绑定</p>
                <div class="row" style="padding: 0 20px">
                  <div id="area-auth">
                    <div class="col-md-5">
                      <div class="input-group">
                        <span class="input-group-addon">账号：</span>
                        <el-input
                          type="text"
                          style="width: 300px"
                          placeholder="请输入登录账号"
                          v-model="form.username"
                          clearable
                        />
                      </div>
                      <div class="input-group" style="margin-top: 10px">
                        <span class="input-group-addon">密码：</span>
                        <el-input
                          v-model="form.password"
                          data-field="input-password"
                          type="password"
                          class="form-control mono"
                          placeholder="请输入登录密码"
                          data-toggle="popover"
                          data-trigger="manual"
                          data-placement="top"
                          autocomplete="off"
                          style="width: 300px"
                          clearable
                        />
                      </div>

                      <div style="margin: 20px 0">
                        <el-button
                          type="primary"
                          style="width: 360px; height: 40px"
                          @click="submit"
                        >
                          用户身份认证</el-button
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" title="身份验证器" width="1000">
      <div class="modal fade" id="dlg-oath-app" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h3 class="modal-title"
                ><i class="fas fa-shield-alt"></i> 身份验证器</h3
              >
            </div>
            <div class="modal-body">
              <div>选择您喜欢的身份验证器进行安装：</div>
              <div class="row-card">
                <div class="col-md-4">
                  <ul class="list">
                    <li>
                      微信小程序
                      <ul class="list">
                        <li
                          ><el-link
                            type="primary"
                            @click="openUrl('微信 · 小程序', 1)"
                            ><i class="fab fa-weixin fa-fw"></i
                            >TOTP(二次验证码)</el-link
                          >
                          <span class="label label-success">推荐</span></li
                        >
                      </ul>
                    </li>
                    <li>
                      谷歌身份验证器
                      <ul class="list">
                        <li
                          ><el-link
                            type="primary"
                            @click="openUrl('谷歌身份验证器', 2)"
                            ><i class="fab fa-apple fa-fw"></i> iOS（Apple
                            Store）</el-link
                          ></li
                        >
                        <li
                          ><el-link
                            type="primary"
                            @click="openUrl('谷歌身份验证器', 3)"
                            ><i class="fab fa-android fa-fw"></i>
                            Android（百度手机助手）</el-link
                          ></li
                        >
                        <li
                          ><el-link
                            type="primary"
                            @click="openUrl('谷歌身份验证器', 4)"
                            ><i class="fab fa-android fa-fw"></i>
                            Android（Google Play）</el-link
                          ></li
                        >
                      </ul>
                    </li>
                    <li>
                      小米安全令牌
                      <ul class="list">
                        <li
                          ><el-link
                            type="primary"
                            @click="openUrl('小米安全令牌', 5)"
                            ><i class="fab fa-apple fa-fw"></i> iOS（Apple
                            Store）</el-link
                          ></li
                        >
                        <li
                          ><el-link
                            type="primary"
                            @click="openUrl('小米安全令牌', 6)"
                            ><i class="fab fa-android fa-fw"></i>
                            Android（小米应用商店）</el-link
                          ></li
                        >
                      </ul>
                    </li>
                  </ul>
                </div>
                <div class="col-md-8">
                  <div id="area-qrcode">
                    <p data-field="name" class="qrcode-name">{{
                      urlParams.name
                    }}</p>
                    <img
                      class="qrcode-img"
                      data-field="qrcode"
                      :src="urlParams.url"
                    />
                    <p data-field="desc">{{ urlParams.desc }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div
          class="dialog-footer"
          style="display: flex; align-items: center; justify-content: center"
        >
          <el-button
            style="height: 38px; font-size: 16px; width: 80px"
            @click="dialogVisible = false"
            >关 闭</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible2" title="绑定身份验证器" width="600">
      <div class="modal fade2" id="dlg-bind-oath" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-body">
              <div
                >请在手机上打开身份验证器，点击增加账号按钮，然后选择“扫描条形码”并扫描下面的二维码来完成账号绑定。</div
              >
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                "
              >
                <!-- <img
                  data-field="oath-secret-qrcode"
                  :src="qrUrl"
                  style="border: 1px solid #b7b7b7; width: 180px"
                /> -->
                <div
                  style="
                    border: 1px solid #b7b7b7;
                    width: 200px;
                    padding: 30px;
                    width: 180px;
                    height: 180px;
                    margin: 20px 0;
                  "
                >
                  <qrcode-vue :value="qrUrl" :size="180" />
                </div>
              </div>
              <p
                >如果无法扫描二维码，则可以选择“手动添加”，设置账号(用户名)，与系统账号一致，其他信息默认(可选)，然后在“密钥”一项中输入下列密钥(无须加空格)：</p
              >
              <div style="text-align: center" class="oath-code"
                ><span data-field="tmp-oath-secret">
                  {{ getText(securityKey) }}
                </span></div
              >
              <p
                >然后请在下面的动态验证码输入框中输入身份验证器提供的6位数字：</p
              >
              <div class="shenfen">
                <div class="col-sm-4" style="text-align: right">
                  <span style="line-height: 34px">动态验证码：</span>
                </div>
                <div class="col-sm-4">
                  <el-input
                    type="text"
                    class="form-control"
                    maxlength="6"
                    data-field="oath-code"
                    data-toggle="popover"
                    data-trigger="manual"
                    data-placement="top"
                    placeholder="请输入动态验证码"
                    v-model="oathCode"
                    clearable
                  />
                </div>
              </div>
              <div
                data-field="message"
                class="alert alert-danger"
                style="margin-top: 10px"
              ></div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div
          class="dialog-footer"
          style="display: flex; align-items: center; justify-content: center"
        >
          <el-button
            type="primary"
            style="height: 38px; font-size: 16px; width: 160px"
            @click="submit2"
            >验证并完成绑定</el-button
          >
          <el-button
            style="height: 38px; font-size: 16px; width: 80px"
            @click="(dialogVisible2 = false), (oathCode = '')"
            >取消</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
  import { reactive, ref, onMounted, onUnmounted } from 'vue';
  import googleoathappstore from '@/assets/authenticator/google-oath-appstore.png';
  import googleoathbaidu from '@/assets/authenticator/google-oath-baidu.png';
  import googleoathgoogleplay from '@/assets/authenticator/google-oath-googleplay.png';
  import wechat from '@/assets/authenticator/wechat.png';
  import xiaomioathappstore from '@/assets/authenticator/xiaomi-oath-appstore.png';
  import xiaomioathxiaomi from '@/assets/authenticator/xiaomi-oath-xiaomi.png';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import {
    getServerTime,
    getSecurityKey,
    checkUser,
    bindAuthenticator
  } from '@/api/login';
  import { encrypt, decrypt } from '@/utils/jsencrypt';
  import QrcodeVue from 'qrcode.vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ArrowLeft } from '@element-plus/icons-vue';

  const route = useRoute();
  const router = useRouter();
  const form = reactive({
    username: '',
    password: ''
  });
  const urlParams = ref({
    name: '微信 · 小程序',
    type: 1,
    url: wechat,
    desc: '适用于 iOS/Android，在微信小程序中搜索“TOTP”即可，（推荐使用“腾讯身份验证器”、“TOTP动态验证码”）'
  });
  const dialogVisible = ref(false);
  const dialogVisible2 = ref(false);
  const timestamp = ref(null);
  const oathCode = ref('');

  const qrCodeBase64 = ref('');
  const qrUrl = ref('');
  const securityKey = ref('');
  const guid = ref('');
  const goBackToLogin = () => {
    router.push({
      path: '/login',
      query: {
        tabActive: 4
      }
    });
  };
  const submit2 = () => {
    if (!oathCode.value) {
      return ElMessage.error('请输入验证码');
    }
    let params = {
      guid: guid.value,
      securityKey: securityKey.value,
      oathCode: oathCode.value
    };
    bindAuthenticator(params).then((res) => {
      if (res.code == 200) {
        ElMessage.success('绑定成功');
        dialogVisible2.value = false;
        oathCode.value = '';
        router.push({
          path: '/login',
          query: {
            tabActive: 4
          }
        });
      } else {
        ElMessage.error(res.msg);
      }
    });
  };
  const getText = (str) => {
    let key = str;
    var result = '';
    // 每 4 个字符一组进行分割
    for (let i = 0; i < key.length; i += 4) {
      result += key.slice(i, i + 4);
      if (i + 4 < key.length) {
        result += ' ';
      }
    }
    return result;
  };
  const submit = () => {
    if (!form.username) {
      return ElMessage.error('请输入账号');
    }
    if (!form.password) {
      return ElMessage.error('请输入密码');
    }
    let params = {
      password: form.password,
      username: form.username
    };
    checkUser(params).then((res) => {
      if (res.code == 200) {
        console.log(res);
        dialogVisible2.value = true;
        guid.value = res.data.guid;
        getSecurityKey({
          username: form.username
        }).then((res) => {
          console.log(res);
          if (res.code == 200) {
            qrCodeBase64.value = res.data.qrCodeBase64;
            qrUrl.value = res.data.qrUrl;
            securityKey.value = res.data.securityKey;
          } else {
            ElMessage.error(res.msg);
          }
        });
      } else {
        ElMessage.error(res.msg);
      }
    });
  };
  const openDialog = () => {
    dialogVisible.value = true;
  };
  const openUrl = (name, type) => {
    urlParams.value.name = name;
    if (type === 1) {
      urlParams.value.url = wechat;
      urlParams.value.desc =
        '适用于 iOS/Android，在微信小程序中搜索“TOTP”即可，（推荐使用“腾讯身份验证器”、“TOTP动态验证码”）';
    }
    if (type === 2) {
      urlParams.value.url = googleoathappstore;
      urlParams.value.desc = '适用于 iOS，从 Apple Store 安装';
    }
    if (type === 3) {
      urlParams.value.url = googleoathbaidu;
      urlParams.value.desc = '适用于 Android，从百度手机助手安装';
    }
    if (type === 4) {
      urlParams.value.url = googleoathgoogleplay;
      urlParams.value.desc = '适用于 Android，从 Google Play 安装';
    }
    if (type === 5) {
      urlParams.value.url = xiaomioathappstore;
      urlParams.value.desc = '适用于 Android，从 Google Play 安装';
    }
    if (type === 6) {
      urlParams.value.url = xiaomioathxiaomi;
      urlParams.value.desc = '适用于 Android，从小米应用商店安装';
    }
  };
  const formatDate = (value, type) => {
    // 计算日期相关值
    let time = typeof value == 'number' ? new Date(value) : value;
    let Y = time.getFullYear();
    let M = time.getMonth() + 1;
    let D = time.getDate();
    let h = time.getHours();
    let m = time.getMinutes();
    let s = time.getSeconds();
    let week = time.getDay();
    // 如果传递了type的话
    if (type == undefined) {
      return (
        Y +
        '-' +
        (M < 10 ? '0' + M : M) +
        '-' +
        (D < 10 ? '0' + D : D) +
        ' ' +
        (h < 10 ? '0' + h : h) +
        ':' +
        (m < 10 ? '0' + m : m) +
        ':' +
        (s < 10 ? '0' + s : s)
      );
    } else if (type == 'week') {
      // 在quartz中 1为星期日
      return week + 1;
    }
  };
  // 定义响应式变量存储格式化后的时间
  const formattedTime = ref('');
  let intervalId;
  onMounted(() => {
    getServerTime().then((res) => {
      console.log(res);
      if (res.code == 200) {
        timestamp.value = res.data;
        // 初始转换时间
        formattedTime.value = formatDate(timestamp.value);
        // 每秒更新一次时间
        intervalId = setInterval(() => {
          timestamp.value += 1000;
          formattedTime.value = formatDate(timestamp.value);
        }, 1000);
      }
    });
  });
  onUnmounted(() => {
    // 组件卸载时清除定时器
    clearInterval(intervalId);
  });
</script>
<style lang="scss" scoped>
  .page-index {
    padding: 20px 400px;
  }
  .info-box {
    width: 100%;
    margin: 30px auto;
  }

  .info-box .info-message-box .title {
    font-size: 180%;
    margin: 15px 0;
  }

  .step-name {
    font-size: 18px;
    color: #e55a1d;
  }

  .input-addon-desc {
    text-align: right;
    font-size: 90%;
    color: #707070;
  }

  .time-box {
    display: inline-block;
    margin: 10px;
    padding: 10px;
    border: 1px solid #bcffbd;
    background-color: #d0ffcd;
    border-radius: 5px;
    color: #646464;
  }

  .tp-time {
    font-weight: bold;
    font-size: 16px;
    color: #063c06;
  }

  #area-qrcode {
    margin: 10px auto 0;
    text-align: center;
  }

  #area-qrcode .qrcode-name {
    font-size: 18px;
    color: #5483ff;
  }

  img.qrcode-img {
    margin: 5px;
    padding: 10px;
    border: 1px solid #bdbdbd;
  }

  img.qrcode-img.selected {
    border: 1px solid #bdbdbd;
  }

  .oath-code {
    font-family:
      'Courier New',
      Consolas,
      Lucida Console,
      Monaco,
      Courier,
      monospace;
    font-size: 24px;
    line-height: 26px;
    font-weight: bold;
    color: #559f47;
  }

  .op_box.op_error {
    background-color: #ffb8b5;
    border: 1px solid #d47e7b;
    color: #333;
  }

  .op_box.op_success {
    background: #acf1b2;
    border: 1px solid #82df82;
    color: #333;
  }
  .input-group {
    display: flex;
    align-items: center;
  }
  .list {
    list-style-type: none;
    padding-left: 0; /* 移除默认的左侧内边距 */
  }
  a {
    text-decoration: none;
  }
  .label {
    display: inline;
    padding: 0.2em 0.6em 0.3em;
    font-size: 75%;
    font-weight: bold;
    line-height: 1;
    color: #ffffff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25em;
    margin-left: 5px;
  }
  .label-success {
    background-color: #5cb85c;
  }
  .fade {
    height: 550px;
    overflow-y: auto;
  }
  .input-group-addon {
    width: 60px;
    text-align: left;
  }
  .shenfen {
    display: flex;
    align-items: center;
  }
  .row-card {
    display: flex;
    .col-md-4 {
      width: 200px;
    }
    .col-md-8 {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      margin-top: -140px;
    }
  }
</style>
