<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1150"
    :model-value="modelValue"
    :title="`人员核对历史记录`"
    @update:modelValue="updateModelValue"
  >
    <div class="modal-dialog">
      <div>
        <el-form inline :model="params">
          <el-form-item label="姓名：">
            <el-input
              v-model="params.name"
              placeholder="请输入姓名"
              clearable
              style="width: 200px"
              maxlength="99"
            />
          </el-form-item>
          <el-form-item label="核对时间：">
            <el-date-picker
              unlink-panels
              type="daterange"
              v-model="dateRange"
              range-separator="-"
              value-format="YYYY-MM-DD"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="ele-fluid"
              style="width: 240px"
              @change="changeDateRange"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="reload">搜索</el-button>
            <el-button type="info" @click="resetData()">重置</el-button>
            <el-button type="info" @click="exportData()">导出</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-table
        :data="tableData"
        height="500px"
        class="table-fixed"
        :header-cell-style="{
          backgroundColor: '#f3f6ff',
          color: '#131314'
        }"
        :row-style="tableRowStyle"
      >
        <template #empty>
          <el-empty description="暂无数据" />
        </template>
        <el-table-column type="index" label="序号" fixed="left" width="65" />
        <el-table-column
          prop="name"
          label="姓名"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="acronym"
          label="姓名缩写"
          width="100"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <span class="ellipsis" v-if="!row.edit">{{ row.acronym }}</span>
              <el-input v-model="row.acronym" style="width: 100%" v-else />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="filterNo"
          label="筛选号"
          width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="joinNo"
          label="入组号"
          width="120"
          show-overflow-tooltip
        />
        <!-- <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <span class="ellipsis" v-if="!row.edit">{{ row.filterno }}</span>
              <el-input v-model="row.filterno" style="width: 100%" v-else />
            </div>
          </template>
        </el-table-column> -->
        <el-table-column
          prop="telephone"
          label="联系电话"
          width="160"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <span class="ellipsis" v-if="!row.edit">{{
                row.showPhoneEye ? row.telephone : getPhone(row.telephone)
              }}</span>
              <img
                :src="row.showPhoneEye ? eyeOpen : eyeClose"
                style="
                  transform: scale(1.28);
                  cursor: pointer;
                  margin: 0 5px;
                  width: 14px;
                  height: 14px;
                "
                @click="row.showPhoneEye = !row.showPhoneEye"
                v-if="!row.edit"
              />

              <el-input
                v-model="row.telephone"
                style="width: 100%"
                v-if="row.edit"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="subjectIdCard"
          label="身份证号"
          width="220"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div style="display: flex; align-items: center">
              <span class="ellipsis">{{
                row.showIdcardEye
                  ? row.subjectIdCard
                  : getCard2(row.subjectIdCard)
              }}</span>
              <img
                :src="row.showIdcardEye ? eyeOpen : eyeClose"
                style="
                  transform: scale(1.28);
                  cursor: pointer;
                  margin: 0 5px;
                  width: 14px;
                  height: 14px;
                "
                @click="getShowEye(row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="similarity"
          label="核对相似度"
          width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{
              row.similarity ? Math.round(row.similarity) + '%' : ''
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="result"
          label="核对结果"
          width="100"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span style="color: #67c32a" v-if="row.result == 1">通过</span>
            <span style="color: #c72727" v-if="row.result != 1">不通过</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="checkedBy"
          label="核对人"
          width="100"
          show-overflow-tooltip
        />
        <el-table-column
          prop="date"
          label="核对时间"
          width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="remarks"
          label="备注"
          width="140"
          fixed="right"
          show-overflow-tooltip
        />
        <!-- <el-table-column label="操作" width="140" fixed="right">
          <template #default="{ row }">
            <el-space>
              <el-link type="primary" @click="row.edit = true" v-if="!row.edit"
                >编辑</el-link
              >
              <el-link type="primary" @click="save(row)" v-if="row.edit"
                >保存</el-link
              >
              <el-link
                type="primary"
                @click="greenLight(row)"
                v-if="
                  (row.status == 0 || row.status == 8) &&
                  (row.sysremarks || '').includes('上家机构建议')
                "
                >放行</el-link
              >
            </el-space>
          </template>
        </el-table-column> -->
      </el-table>
      <el-pagination
        class="flexCenter"
        v-model:current-page="params.pageNumber"
        v-model:page-size="params.pageSize"
        :page-sizes="[10, 20, 30, 40]"
        layout="total, sizes, prev, pager, next, jumper"
        :pager-count="4"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <template #footer>
      <!-- <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
      </div> -->
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref, watch } from 'vue';
  import {
    getCompleteInfo,
    selectPersonnelVerificationExport,
    selectPersonnelVerificationList
  } from '@/api/subjects/studyManage/index.js';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { ElMessageBox } from 'element-plus';
  import { Icon } from '@iconify/vue';
  import eyeOpen from '@/assets/subjects/eyeOpen.png';
  import eyeClose from '@/assets/subjects/eyeClose.png';
  import { name } from 'dayjs/locale/zh-cn';
  import { settings } from 'nprogress';
  const tableRowStyle = (row) => {
    return row.rowIndex % 2 === 0
      ? { background: '#ffffff' }
      : { background: '#ffffff' };
  };
  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 回显的数据
    data: Object
  });
  const dateRange = ref([]);
  const total = ref(0);
  const tableData = ref([]);
  const params = ref({
    name: '',
    trialsGuid: props.data.trialsGuid,
    organizationGuid: props.data.organizationGuid,
    startDate: '',
    endDate: '',
    pageNumber: 1,
    pageSize: 10,
    orderBy: '',
    orderItem: 'asc'
  });
  // const close = () => {
  //   emit('done', tableData.value);
  //   updateModelValue(false);
  // };
  const resetData = () => {
    dateRange.value = [];
    params.value = {
      name: '',
      trialsGuid: props.data.trialsGuid,
      organizationGuid: props.data.organizationGuid,
      startDate: '',
      endDate: '',
      pageNumber: 1,
      pageSize: 10,
      orderBy: '',
      orderItem: 'asc'
    };
    reload();
  };
  const handleSizeChange = (size) => {
    setTimeout(() => {
      params.value.pageSize = size;
      reload();
    }, 500);
  };
  const handleCurrentChange = (current) => {
    setTimeout(() => {
      params.value.pageNumber = current;
      reload();
    }, 500);
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  const changeDateRange = (e) => {
    console.log(e);
    if (e) {
      params.value.startDate = e[0];
      params.value.endDate = e[1];
    } else {
      params.value.startDate = '';
      params.value.endDate = '';
    }
    reload();
  };
  const getPhone = (text) => {
    try {
      if (text.length === 11) {
        return text.replace(/(\d{3})\d+(\d{4})/, '$1****$2');
      } else {
        let numLength = text.replace(/\D/g, '').length;
        let regex = new RegExp(`^(\\d{1})(\\d{${numLength - 2}})(\\d{1})$`);
        return text.replace(regex, '$1' + '*'.repeat(numLength - 2) + '$3');
      }
    } catch (error) {
      return '';
    }
  };

  const getCard2 = (text) => {
    try {
      return text.replace(/(\d{6})\d+(\d{4})/, '$1********$2');
    } catch (error) {
      return '';
    }
  };

  const getShowEye = (row) => {
    row.showIdcardEye = !row.showIdcardEye;
    getCompleteInfo({ guid: row.subjectsGuid, type: '1' }).then((res) => {
      row.idcard = res.msg;
      row.idcard = getCard(row);
    });
  };
  const getCard = (row) => {
    try {
      if (row.showIdcardEye) {
        return row.idcard;
      }
      return row.idcard.replace(/(\d{6})\d+(\d{4})/, '$1********$2');
    } catch (error) {
      console.log(error);
      return row.idcard;
    }
  };
  /* 搜索 */
  const reload = () => {
    selectPersonnelVerificationList(params.value).then((res) => {
      console.log(res);
      if (res.code == 200) {
        tableData.value = res.data.list;
        total.value = res.data.totalCount;
      }
    });
  };
  /* 导出 */
  const exportData = () => {
    selectPersonnelVerificationExport(params.value).then((res) => {
      if (res.code == 200) {
        window.open(res.data);
      }
    });
  };
  nextTick(async () => {
    params.value.trialsGuid = props.data.trialsGuid;
    params.value.organizationGuid = props.data.organizationGuid;
    reload();
  });
  // watch(
  //   () => props.modelValue,
  //   () => {
  //     params.value.trialsGuid = props.data.trialsGuid;
  //     params.value.organizationGuid = props.data.organizationGuid;
  //     reload();
  //   }
  // );
</script>
<style lang="scss" scoped>
  .ellipsis {
    width: 100%;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  :deep(.el-pager) {
    margin: 0 4px !important;
  }
  :deep(.el-pager li) {
    border: 1px solid #94aed9;
    box-sizing: border-box;
    border-radius: 4px !important;
    margin: 4px !important;
  }
</style>
