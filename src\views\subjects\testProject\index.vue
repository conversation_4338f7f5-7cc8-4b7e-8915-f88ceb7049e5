<template>
  <div class="index" :style="'height: calc(100vh - 90px - ' + height + ');'">
    <ele-card
      flex-table
      :body-style="{ padding: '4px 0 20px 0', overflow: 'hidden' }"
      style="height: 100%"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        v-model:selections="selections"
        :columns="columns"
        :datasource="datasource"
        :toolbar="{ theme: tableHeader }"
        highlight-current-row
        :bottom-line="tableFullHeight"
        cache-key="workAuditCheckTable19"
      >
        <!-- <smart-form
          v-model="searchExpand"
          ref="searchRef"
          :entityFileds="searchConfig"
          @search="reload"
        /> -->
        <!-- 表头左侧功能按钮 -->
        <template #toolbar>
          <div class="tableForm">
            <el-form inline :model="params">
              <!-- <el-form-item>
                <el-button
                  :icon="CirclePlus"
                  v-if="loginUser.role == 2"
                  type="primary" plain
                  @click="handleAddorEdit()"
                >
                  新增
                </el-button>
              </el-form-item> -->

              <!-- <el-button
                type="warning"
                class="ele-btn-icon"
                :icon="ArrowDown"
                plain
                @click="downLoad"
              >
                导出
              </el-button> -->
              <el-form-item label="试验名称：">
                <el-input
                  v-model="params.name"
                  placeholder="请输入试验名称"
                  clearable
                  style="width: 200px"
                  maxlength="99"
                />
              </el-form-item>
              <el-form-item label="试验编号：">
                <el-input
                  v-model="params.code"
                  placeholder="请输入试验编号"
                  clearable
                  style="width: 200px"
                  maxlength="50"
                />
              </el-form-item>
              <el-form-item label="试验状态：">
                <el-select
                  v-model="params.status"
                  placeholder="请选择试验状态"
                  clearable
                  style="width: 200px"
                >
                  <el-option label="进行中" value="1" />
                  <el-option label="已完成" value="2" />
                  <!-- <el-option label="中止" value="3" /> -->
                  <!-- <el-option label="终止" value="4" /> -->
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" plain @click="reload">搜索</el-button>
                <el-button type="primary" plain @click="showHighSearch = true">
                  高级检索</el-button
                >
                <el-button type="info" @click="(params = {}), reload()"
                  >重置</el-button
                >
                <el-button
                  type="primary"
                  @click="handleAdd"
                  v-if="userStore.authorities.includes('project:screen:add')"
                  v-permission="'project:screen:add'"
                >
                  新增
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </template>

        <!-- 表头工具按钮 -->
        <!-- <template #tools>
          <el-space size="default" :wrap="true">
            <el-link type="primary" @click="searchExpand = !searchExpand">
              {{ searchExpand ? '普通搜索' : '高级搜索' }}
            </el-link>
            <el-divider direction="vertical" style="margin: 0" />
          </el-space>
        </template> -->
        <template #tools>
          <!-- <div
            class="ele-tool-add"
            @click="handleAdd"
            v-if="loginUser.role == 2"
          >
            新增
          </div> -->

          <div class="ele-tool" @click="downLoad">
            <img src="@/assets/exportIcon.svg" alt="" style="width: 19px" />
          </div>
        </template>

        <template #subjectType="{ row }">
          <span v-if="row.subjectType == 1">健康受试者</span>
          <span v-if="row.subjectType == 2">患者受试者</span>
        </template>
        <template #status="{ row }">
          <span v-if="row.status == 1" style="color: #67c32a">进行中</span>
          <span v-if="row.status == 2" style="color: #999999">已完成</span>
          <!-- <span v-if="row.status == 3" style="color: #a69ea8">中止</span> -->
          <!-- <span v-if="row.status == 4" style="color: #656ad2">终止</span> -->
        </template>
        <template #action="{ row }">
          <!-- <tableBtn
            :key="row.uuid"
            :items="tableBtnList(row)"
            @routeEvent="routeEvent"
            :operateItem="row"
            :maxLength="1"
          /> -->
          <!-- 使用tableBtn组件渲染操作栏按钮 -->
          <el-space>
            <el-link
              type="primary"
              :underline="false"
              @click="handleDetail(row)"
              style="margin-right: 10px; color: #507aff"
            >
              详情
            </el-link>
            <!-- <el-link
              v-if="loginUser.role == 2 && row.status == 2"
              type="primary"
              :underline="false"
              @click="handleAddorEdit2(row)"
              style="margin-right: 10px"
            >
              复制新增
            </el-link>
            <el-link
              v-if="loginUser.role == 2 && row.status == 1"
              type="primary"
              :underline="false"
              @click="handleAddorEdit(row)"
              style="margin-right: 10px"
            >
              编辑
            </el-link> -->
            <el-dropdown
              @command="(key) => commandFun(key, row)"
              v-if="
                userStore.authorities.includes('project:screen:add') ||
                (row.status == 1 &&
                  userStore.authorities.includes('project:screen:edit')) ||
                (row.status == 1 &&
                  userStore.authorities.includes('project:screen:finish')) ||
                (row.status == 1 && userStore.roles[0] != 'superadmin')
              "
            >
              <el-link
                type="primary"
                style="
                  color: #507aff;
                  cursor: pointer;
                  transform: translateY(0.5px);
                  font-size: 16px;
                "
              >
                <span>更多</span>
              </el-link>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="userStore.authorities.includes('project:screen:add')"
                    style="color: #507aff; cursor: pointer"
                    command="copyAdd"
                    v-permission="'project:screen:add'"
                    >复制新增</el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="
                      row.status == 1 &&
                      userStore.authorities.includes('project:screen:edit')
                    "
                    style="color: #507aff; cursor: pointer"
                    command="copyEdit"
                    v-permission="'project:screen:edit'"
                    >编辑</el-dropdown-item
                  >
                  <el-dropdown-item
                    style="color: #507aff; cursor: pointer"
                    command="markFinish"
                    v-permission="'project:screen:finish'"
                    v-if="
                      row.status == 1 &&
                      userStore.authorities.includes('project:screen:finish')
                    "
                    ><span>标记完成</span></el-dropdown-item
                  >
                  <el-dropdown-item
                    v-if="row.status == 1 && userStore.roles[0] != 'superadmin'"
                    style="color: #507aff; cursor: pointer"
                    command="goLook"
                    ><span>去筛查</span></el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-space>
        </template>
      </ele-pro-table>
      <!-- 弹窗 -->
    </ele-card>
    <addProject
      v-model="showCreate"
      :data="current"
      @done="reload"
      :isCopyAdd="isCopyAdd"
    />
    <highSearchModal
      v-model="showHighSearch"
      :data="params"
      @done="highSearchLoad"
      v-if="showHighSearch"
    />
    <detailDialog v-model="showDetail" :data="current" />
  </div>
</template>
<script>
  export default {
    name: 'TestProject'
  };
</script>
<script setup>
  import {
    Plus,
    Delete,
    ArrowDown,
    Postcard,
    CirclePlus
  } from '@element-plus/icons-vue';
  import tableBtn from '@/views/subjects/components/tableBtn/index.vue';
  import { ref, computed, nextTick, onMounted, watch } from 'vue';
  import {
    trialsProjectList,
    exportProject,
    trialsMarkConclusion
  } from '@/api/subjects/testProject/index.js';
  import addProject from './components/addProject.vue';
  import detailDialog from './components/detailDialog.vue';
  import highSearchModal from './components/highSearchModal.vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { ElMessageBox } from 'element-plus/es';
  import { Icon } from '@iconify/vue';
  import { useRouter, useRoute } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';
  const userStore = useUserStore();
  /** 当前用户信息 */
  const loginUser = computed(() => userStore.info ?? {});
  console.log(
    '55555555555====>loginUser',
    userStore.authorities,
    userStore.roles
  );
  const router = useRouter();
  const route = useRoute();
  const props = defineProps({
    height: {
      type: String,
      default: '0px'
    }
  });
  const tableBtnList = (row) => {
    const list = [
      {
        name: '详情',
        fun: 'handleDetail',
        status: true
      },
      {
        name: '编辑',
        fun: 'handleAddorEdit',
        status: loginUser.value.role == 2 && row.status == 1
      },
      {
        name: '复制新增',
        fun: 'handleAddorEdit2',
        status: loginUser.value.role == 2
      },
      {
        name: '标记完成',
        fun: 'handleDelete',
        status: loginUser.value.role == 2 && row.status == 1
      },
      {
        name: '去筛查',
        fun: 'handleSeeLook',
        status: row.status == 1
      }
    ];
    return list.filter((val) => val.status == true);
  };
  const routeEvent = (row, item) => {
    console.log(row);
    console.log(item);
    if (item.name == '详情') {
      handleDetail(row);
      return;
    }
    if (item.name == '编辑') {
      handleAddorEdit(row);
      return;
    }
    if (item.name == '复制新增') {
      handleAddorEdit2(row);
      return;
    }
    if (item.name == '标记完成') {
      handleDelete(row);
      return;
    }
    if (item.name == '去筛查') {
      handleSeeLook(row);
      return;
    }
  };
  //高级检索弹框
  const showHighSearch = ref(false);
  const showDetail = ref(false);
  const params = ref({
    pageNumber: 1,
    pageSize: 15
  });
  // 表格列配置
  const columns = ref([
    // {
    //   type: 'selection',
    //   columnKey: 'selection',
    //   width: 48,
    //   align: 'center',
    //   fixed: 'left'
    // },
    {
      type: 'index',
      columnKey: 'index',
      label: '序号',
      minWidth: 65,
      align: 'center',
      showOverflowTooltip: true,
      fixed: 'left'
    },
    {
      prop: 'status',
      label: '试验状态',
      minWidth: 90,
      showOverflowTooltip: true,
      slot: 'status'
    },
    {
      prop: 'schemeCode',
      label: '方案编号',
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'code',
      label: '试验编号',
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'name',
      label: '试验名称',
      minWidth: 220,
      showOverflowTooltip: true
    },
    {
      prop: 'subjectType',
      label: '志愿者类别',
      minWidth: 100,
      showOverflowTooltip: true,
      slot: 'subjectType'
    },
    {
      prop: 'type',
      label: '试验类别',
      minWidth: 100,
      showOverflowTooltip: true
    },
    {
      prop: 'cycle',
      label: '试验周期',
      minWidth: 100,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'drawblood',
      label: '采血量分级',
      minWidth: 150,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'drugtype',
      label: '药品类型',
      minWidth: 120,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'route',
      label: '给药途径',
      minWidth: 120,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'interval',
      label: '试验间隔天数',
      minWidth: 150,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'halflife',
      label: '半衰期',
      minWidth: 100,
      showOverflowTooltip: true,
      hideInTable: true // 默认隐藏该列
    },
    {
      prop: 'professional',
      label: '专业',
      minWidth: 120,
      showOverflowTooltip: true
    },
    {
      prop: 'illness',
      label: '病种',
      minWidth: 200,
      showOverflowTooltip: true
    },
    // {
    //   prop: 'subjectCount',
    //   label: '受试者数量',
    //   minWidth: 120,
    //   showOverflowTooltip: true
    // },
    {
      columnKey: 'action',
      label: '操作',
      hideInSetting: true,
      width: loginUser.value.role == 2 ? 120 : 120,
      align: 'left',
      resizable: false,
      slot: 'action',
      fixed: 'right'
    }
  ]);
  // 表格选中数据
  const selections = ref([]);
  // 新增修改弹框控制
  const showCreate = ref(false);
  const isCopyAdd = ref(false);
  // 当前选中数据
  const current = ref({});
  // 表格实例
  const tableRef = ref(null);
  const datasource = ref([]);
  // 表格数据源
  const data22 = ({ page, limit, where }) => {
    //替换字符
    if (where.drawblood && where.drawblood.includes('<')) {
      where.drawblood = where.drawblood.replace(/</g, '&lt;');
    }
    return trialsProjectList({
      ...where,
      pageNumber: page,
      pageSize: limit
    });
  };
  /** 刷新表格 */
  const reload = () => {
    tableRef.value?.reload?.({ where: params.value });
    datasource.value = data22;
  };
  const highSearchLoad = (value) => {
    params.value = value;
    reload();
  };
  const handleSeeLook = (row) => {
    router.push({
      path: '/studyManage',
      query: {
        projectId: row.guid,
        projectName: row.name,
        code: row.code,
        status: row.status
      }
    });
  };
  const handleAdd = () => {
    isCopyAdd.value = false;
    showCreate.value = true;
    current.value = null;
  };
  const handleAddorEdit = (row = null) => {
    isCopyAdd.value = false;
    showCreate.value = true;
    current.value = row;
  };
  const handleDetail = (row) => {
    showDetail.value = true;
    current.value = row;
  };
  const handleAddorEdit2 = (row = null) => {
    isCopyAdd.value = true;
    showCreate.value = true;
    current.value = row;
  };
  /** 删除按钮操作 */
  const handleDelete = (row) => {
    ElMessageBox.confirm(`确定要标记该试验项目已完成吗？`, '提示', {
      confirmButtonText: '是',
      cancelButtonText: '否',
      center: true
    }).then(async () => {
      try {
        await trialsMarkConclusion(row.guid).then((res) => {
          if (res.code == 200) {
            EleMessage.success(`操作成功！`);
            reload();
          } else {
            EleMessage.error(res.msg);
          }
        });
      } catch (e) {
        EleMessage.error(e);
      }
    });
  };
  /* 导出数据 */
  const downLoad = () => {
    const loading = EleMessage.loading('请求中..');
    // tableRef.value?.fetch?.(({ where }) => {
    if (!params.value.pageNumber) {
      params.value.pageNumber = 1;
      params.value.pageSize = 15;
    }
    exportProject(params.value)
      .then(() => {
        loading.close();
      })
      .catch((e) => {
        loading.close();
        EleMessage.error(e.msg);
      });
    // });
  };
  const commandFun = (command, row) => {
    switch (command) {
      case 'markFinish': //标记完成
        handleDelete(row);
        break;
      case 'copyAdd': //复制新增
        handleAddorEdit2(row);
        break;
      case 'copyEdit': //编辑
        handleAddorEdit(row);
        break;
      case 'goLook':
        handleSeeLook(row);
        break;
      default:
        break;
    }
  };
  watch(
    () => route.query,
    () => {
      console.log('值变化了-----');
      initData();
    },
    { deep: true }
  );
  const initData = () => {
    params.value.status = '';
    params.value.startDate = '';
    params.value.endDate = '';
    if (route.query.status) {
      params.value.status = route.query.status;
    }
    if (route.query.startDate || route.query.endDate) {
      params.value.startDate = route.query.startDate;
      params.value.endDate = route.query.endDate;
    }
    reload();
  };
  nextTick(() => {
    initData();
  });
</script>
<style lang="scss" scoped>
  .index {
    // width: 100%
    background: #ffffff;
    border-radius: 8px;
    padding: 0 20px;
    display: flex;
    box-sizing: border-box;
    flex-direction: column;
    overflow-x: hidden;
  }
  .top-search2 {
    display: flex;
  }
  .tableForm2 {
    display: flex;
    flex-wrap: wrap;
  }
  :deep(.ele-toolbar .ele-toolbar-tools) {
    position: relative;
    // top: -7px !important;
  }
</style>
