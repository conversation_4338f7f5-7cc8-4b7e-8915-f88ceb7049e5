<!-- 编辑弹窗 -->
<template>
  <ele-modal
    form
    :width="1145"
    :model-value="modelValue"
    :title="type == 'out' ? '标记出组' : '批量编辑'"
    @update:modelValue="updateModelValue"
    align-center
  >
    <template #header>
      <div class="dialog-header" style="position: relative">
        <span>{{ type == 'out' ? '标记出组' : '批量编辑' }}</span>
        <!-- <span
          style="
            font-size: 14px;
            color: #faad14;
            font-weight: 500;
            position: absolute;
            bottom: 0;
            right: 332px;
          "
        >
          {{
            type == 'out' ? '仅支持标记“入组”的数据' : '仅支持编辑“入组”的数据'
          }}
        </span> -->
      </div>
    </template>
    <div class="modal-table-dialog error">
      <div style="display: flex; align-items: center; margin-bottom: 8px">
        <div style="font-size: 16px">批量填充</div>
        <el-date-picker
          v-model="lastdosedate"
          type="date"
          placeholder="请选择末次给药时间"
          :disabled-date="disableddate"
          clearable
          value-format="YYYY-MM-DD"
          style="flex: 1; margin: 0 10px"
        />
        <el-date-picker
          v-model="trialedate"
          type="date"
          placeholder="请选择末次访视时间"
          :disabled-date="disableddate"
          clearable
          value-format="YYYY-MM-DD"
          style="flex: 1; margin: 0 10px"
        />

        <el-select
          v-model="evaluate"
          placeholder="请选择依从性评价"
          clearable
          style="flex: 1; margin: 0 10px"
        >
          <el-option label="优" value="优" />
          <el-option label="良" value="良" />
          <el-option label="中" value="中" />
          <el-option label="差" value="差" />
          <el-option label="很差" value="很差" />
          <el-option label="空白" value="空白" />
        </el-select>

        <el-button type="primary" @click="save">确 定</el-button>
      </div>
      <el-form :model="ruleForm" ref="formRule">
        <el-table
          :data="ruleForm.tableData"
          :header-cell-style="{ background: '#eeeeee' }"
          height="50vh"
          @selection-change="handleSelectionChange"
          ref="tableRef"
        >
          <template #empty>
            <el-empty description="暂无数据" />
          </template>
          <el-table-column type="selection" width="65" />
          <el-table-column
            prop="name"
            label="（入组号）姓名"
            width="200"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <div
                style="
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
              >
                <span v-if="row.joinno">（{{ row.joinno }}）</span
                >{{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="joindate" label="入组时间" width="120">
            <!-- <template #header>
              <span style="color: #b90000; margin-right: 3px">*</span>入组时间
            </template> -->
          </el-table-column>
          <el-table-column prop="lastdosedate" label="末次给药时间" width="220">
            <template #header>
              <span style="color: #b90000; margin-right: 3px">*</span
              >末次给药时间
            </template>
            <template #default="{ row, $index }">
              <el-form-item
                :prop="`tableData.${$index}.lastdosedate`"
                :rules="[
                  {
                    required: true,
                    validator: validatorRule1,
                    trigger: ['blur', 'change']
                  }
                ]"
              >
                <el-date-picker
                  v-model="row.lastdosedate"
                  type="date"
                  placeholder="请选择末次给药时间"
                  clearable
                  value-format="YYYY-MM-DD"
                  :disabled-date="disableddate"
                  @change="lastdosedateChange(row)"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="trialedate" label="末次访视时间" width="220">
            <template #header>
              <span style="color: #b90000; margin-right: 3px">*</span
              >末次访视时间
            </template>
            <template #default="{ row, $index }">
              <el-form-item
                :prop="`tableData.${$index}.trialedate`"
                :rules="[
                  {
                    required: true,
                    validator: validatorRule2,
                    trigger: ['blur', 'change']
                  }
                ]"
              >
                <el-date-picker
                  v-model="row.trialedate"
                  type="date"
                  placeholder="请选择末次访视时间"
                  clearable
                  value-format="YYYY-MM-DD"
                  :disabled-date="disableddate"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column prop="evaluate" label="依从性评价" width="220">
            <template #default="{ row }">
              <el-form-item>
                <el-select
                  v-model="row.evaluate"
                  placeholder="请选择依从性评价"
                  clearable
                >
                  <el-option label="优" value="优" />
                  <el-option label="良" value="良" />
                  <el-option label="中" value="中" />
                  <el-option label="差" value="差" />
                  <el-option label="很差" value="很差" />
                  <el-option label="空白" value="空白" />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer flexCenter">
        <el-button type="info" @click="close()">取 消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="submit"
          v-if="type == 'out'"
          >确 定</el-button
        >
        <el-button
          type="primary"
          :loading="loading"
          @click="submit"
          v-if="type == 'edit'"
          >确 定</el-button
        >
      </div>
    </template>
  </ele-modal>
</template>

<script setup>
  import { nextTick, ref, computed } from 'vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import {
    batchUpdate,
    markOutGroup
  } from '@/api/subjects/studyManage/index.js';
  import { ElMessageBox } from 'element-plus';
  import dayjs from 'dayjs';
  import { throttle } from 'lodash';
  import { useUserStore } from '@/store/modules/user';
  import { Warning } from '@element-plus/icons-vue';

  const userStore = useUserStore();
  const userInfo = computed(() => userStore.info ?? {});
  const emit = defineEmits(['done', 'update:modelValue']);
  const lastdosedate = ref('');
  const trialedate = ref('');
  const evaluate = ref('');
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Array,
    type: String
  });
  const lastdosedateChange = (row) => {
    if (row.trialedate) {
      const lastdosedate = dayjs(JSON.parse(JSON.stringify(row.lastdosedate)));
      const trialedate = dayjs(JSON.parse(JSON.stringify(row.trialedate)));
      if (trialedate < lastdosedate) {
        row.trialedate = '';
        batch();
      }
    }
  };
  const batch = throttle(() => {
    EleMessage.warning('末次访视时间不能大于末次给药时间！');
  }, 1000);
  const disableddate = (time) => {
    const nowDate = dayjs(time);
    const currentDate = dayjs(userInfo.value.currentTime);
    return !(currentDate >= nowDate);
  };

  const loading = ref(false);
  const close = () => {
    updateModelValue(false);
  };
  const formRule = ref(null);
  const validatorRule0 = (rule, value, callback) => {
    const parts = rule.field.split('.');
    const index = parts[1];
    if (
      !value &&
      selectionsList.value.some(
        (val) => val.guid == ruleForm.value.tableData[index].guid
      )
    ) {
      callback(new Error('请选择入组时间'));
      return;
    }
    callback();
  };
  const validatorRule1 = (rule, value, callback) => {
    const parts = rule.field.split('.');
    const index = parts[1];
    if (
      !value &&
      selectionsList.value.some(
        (val) => val.guid == ruleForm.value.tableData[index].guid
      )
    ) {
      callback(new Error('请选择末次给药时间'));
      return;
    }
    callback();
  };
  const validatorRule2 = (rule, value, callback) => {
    const parts = rule.field.split('.');
    const index = parts[1];
    if (
      !value &&
      selectionsList.value.some(
        (val) => val.guid == ruleForm.value.tableData[index].guid
      )
    ) {
      callback(new Error('请选择末次访视时间'));
      return;
    }
    callback();
  };
  const submit = () => {
    if (selectionsList.value.length == 0) {
      EleMessage.warning('请至少勾选一条数据再确认');
      return;
    }
    formRule.value.validate((valid) => {
      if (valid) {
        const data = selectionsList.value.map((val) => {
          return {
            guid: val.guid,
            joinno: ruleForm.value.tableData.find(
              (item) => item.guid == val.guid
            ).joinno,
            lastdosedate: ruleForm.value.tableData.find(
              (item) => item.guid == val.guid
            ).lastdosedate,
            trialedate: ruleForm.value.tableData.find(
              (item) => item.guid == val.guid
            ).trialedate,
            evaluate: ruleForm.value.tableData.find(
              (item) => item.guid == val.guid
            ).evaluate
          };
        });
        const msg =
          props.type == 'edit'
            ? `是否确认编辑【${selectionsList.value
                .map((val) => val.name)
                .join()}】？`
            : `是否确定将【${selectionsList.value
                .map((val) => val.name)
                .join()}】标记为出组?`;
        ElMessageBox.confirm(msg, '提示', {
          confirmButtonText: '确 定',
          cancelButtonText: '取 消',
          center: true
        }).then(() => {
          loading.value = true;
          const api = props.type == 'edit' ? batchUpdate : markOutGroup;
          api(data).then((res) => {
            loading.value = false;
            if (res.code == 200) {
              EleMessage.success(
                props.type == 'edit' ? '编辑成功' : '标记成功'
              );
              emit('done');
              updateModelValue(false);
              return;
            }
            EleMessage.error(res.msg);
          });
        });

        return;
      }
    });
  };
  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const ruleForm = ref({ tableData: [] });
  const selectionsList = ref([]);
  const tableRef = ref(null);
  nextTick(async () => {
    ruleForm.value.tableData = JSON.parse(JSON.stringify(props.data));
    ruleForm.value.tableData.forEach((item) => {
      setTimeout(() => {
        tableRef.value.toggleRowSelection(item, true);
      });
    });
  });
  const handleSelectionChange = (val) => {
    selectionsList.value = val;
    ruleForm.value.tableData.forEach((item, index) => {
      if (!selectionsList.value.some((val) => val.guid == item.guid)) {
        formRule.value.validateField(`tableData.${index}.lastdosedate`);
        formRule.value.validateField(`tableData.${index}.trialedate`);
      }
    });
  };
  const save = () => {
    ruleForm.value.tableData.forEach((item, index) => {
      if (selectionsList.value.some((val) => val.guid == item.guid)) {
        item.lastdosedate = lastdosedate.value;
        item.trialedate = trialedate.value;
        item.evaluate = evaluate.value;
      }
      if (selectionsList.value.some((val) => val.guid == item.guid)) {
        formRule.value.validateField(`tableData.${index}.lastdosedate`);
        formRule.value.validateField(`tableData.${index}.trialedate`);
      }
      lastdosedateChange(item);
    });
  };
</script>
<style>
  .modal-dialog .el-table .cell {
    overflow: initial !important;
  }
</style>
