<template>
  <ele-modal
    form
    :width="props.data.faceFailureFlag ? 600 : 900"
    :model-value="modelValue"
    title="筛 选 结 果"
    @update:modelValue="updateModelValue"
    :close-on-click-modal="false"
    :show-close="false"
    align-center
  >
    <div class="filter-wapper">
      <div style="margin-bottom: 10px; display: flex; flex-direction: column">
        <div
          style="margin-bottom: 10px; font-size: 16px"
          v-if="props.data.status == 1 || props.data.status == 7"
        >
          <el-icon class="result-icon" style="margin-right: 2px">
            <Check style="color: #ffffff" />
          </el-icon>
          <span style="color:#00B050;margin-left:5px;vertical-align:text-bottom">{{
            props.data.isFirst && props.data.type == '1'
              ? '首次筛选'
              : props.data.type == '2'
                ? '入组前筛选'
                : '筛选'
          }}合格</span>
          <div v-html="props.data.sysRemarks"></div>
        </div>
        <div
          style="margin-bottom: 5px; font-size: 16px"
          v-if="props.data.status == 0 || props.data.status == 8"
        >
          <el-icon class="result-icon2" style="margin-right: 2px">
            <Close style="color: #ffffff" />
          </el-icon>
          <span style="color:#FF4D4F;margin-left:5px;vertical-align:text-bottom">{{
            props.data.isFirst && props.data.type == '1'
              ? '首次筛选'
              : props.data.type == '2'
                ? '入组前筛选'
                : '筛选'
          }}不合格</span>
          <div v-html="props.data.sysRemarks"></div>
        </div>
      </div>
      <el-table :data="tableData" style="width: 100%" v-if="!props.data.faceFailureFlag">
        <el-table-column prop="index" label="序号" width="80" />
        <el-table-column prop="item" label="筛查项目">
          <template #default="scope">
            <span>{{scope.row.item}}</span>
            <span v-if="scope.row.message" style="opacity: 0.6;">（{{scope.row.message?.split('^')?.map((i,index)=>{return `${index+1}.${i}`}).join('、')}}）</span>
          </template>
        </el-table-column>
        <el-table-column label="筛查结果" align="center" width="100">
          <template #default="scope">
            <el-icon v-if="scope.row.result === 0" class="result-icon">
              <Check style="color: #ffffff" />
            </el-icon>
            <el-icon v-else-if="scope.row.result === 1 && scope.row.icon !== 'warning'" class="result-icon2">
              <Close style="color: #ffffff" />
            </el-icon>
            <el-icon v-else-if="scope.row.result === 1 && scope.row.icon === 'warning'" class="result-icon3">
              <WarningFilled style="color: #FAAD14; font-size:26px;"/>
            </el-icon>
          </template>
        </el-table-column>
      </el-table>
      <p v-if="!props.data.faceFailureFlag" style="font-size: 16px">说明：{{ props.data.type == '1' ? '7/8/9' : '4/5/6' }}三项属于提示信息，请自行判断{{ props.data.type == '1' ? '；第6项在上家建议安全期内可手动放行' : '' }}</p>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <!--<el-button
          type="primary"
          plain
          v-if="props.data.unqualifiedFlag"
          @click="markFail"
        >
          标记不合格
        </el-button>-->
        <el-button
          type="primary"
          plain
          v-if="props.data.releaseFlag"
          @click="releaseFlagClick"
        >
          手动放行
        </el-button>
        <el-button type="primary" @click="goToNext"> 筛选下一位 </el-button>
      </div>
    </template>
  </ele-modal>
</template>
<script setup>
  import {
    markUnqualified,
    release
  } from '@/api/subjects/studyManage/index.js';
  import { ref, nextTick, watch } from 'vue';
  import { Check, Close, WarningFilled } from '@element-plus/icons-vue';
  import { EleMessage } from '@hnjing/zxzy-admin-plus/es';
  import { ElMessageBox } from 'element-plus/es';
  const emit = defineEmits(['done', 'update:modelValue']);
  // eslint-disable-next-line no-unused-vars
  const props = defineProps({
    // 弹窗是否打开
    modelValue: Boolean,
    // 修改回显的数据
    data: Array
  });
  const tableData = ref([]);

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        console.log('modelValue', modelValue);
        if (props.data) {
          // 追加原因
          if (props.data.sysRemarks) {
            props.data.sysRemarks = '原因：' + props.data.sysRemarks;
          }
          if (props.data.type == '1') {
            tableData.value = [
              { index: 1, item: '是否重复试验', result: props.data.isResult1 },
              { index: 2, item: '是否待入组', result: props.data.isResult2 },
              { index: 3, item: '是否已入组', result: props.data.isResult3 },
              {
                index: 4,
                item: '是否在筛选间隔期内',
                result: props.data.isResult4
              },
              {
                index: 5,
                item: '是否在安全期内（距末次给药/访视天数）',
                result: props.data.isResult5
              },
              {
                index: 6,
                item: '是否在上家建议安全期内（距末次给药/访视天数）',
                result: props.data.isResult6
              },
              {
                index: 7,
                item: '是否为不适宜人群',
                result: props.data.isResult7,
                icon: 'warning',
                //message: props.data.blacklistExplain
              },
              {
                index: 8,
                item: '是否被设置备忘录',
                result: props.data.isResult8,
                icon: 'warning',
                message: props.data.memoMessage
              },
              {
                index: 9,
                item: '参与试验频率过高（12个月入组超过3次）',
                result: props.data.isResult9,
                icon: 'warning'
              }
            ];
          } else {
            tableData.value = [
              {
                index: 1,
                item: '是否已参与筛选',
                result: props.data.isJoinResult1
              },
              {
                index: 2,
                item: '是否待入组',
                result: props.data.isJoinResult2
              },
              {
                index: 3,
                item: '是否已入组',
                result: props.data.isJoinResult3
              },
              {
                index: 4,
                item: '是否为不适宜人群',
                result: props.data.isJoinResult4,
                icon: 'warning',
                //message: props.data.blacklistExplain
              },
              {
                index: 5,
                item: '是否被设置忘备录',
                icon: 'warning',
                result: props.data.isJoinResult5,
                message: props.data.memoMessage
              },
              {
                index: 6,
                item: '参与试验频率过高（12个月入组超过3次）',
                icon: 'warning',
                result: props.data.isJoinResult6
              }
            ];
          }
          console.log(
            'props.data',
            props.data,
            props.data.isResult1,
            tableData.value
          );
        }
        // 允许放行，弹窗二次确认
        /*if (props.data.releaseFlag) {
          ElMessageBox.confirm('当前志愿者在上家建议安全期内，是否确定放行？', '温馨提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            release({
              guid: props.data.guid
            }).then((res) => {
              if (res.code == 200) {
                EleMessage.success('放行成功');
                emit('done');
                updateModelValue(false);
                return;
              }
              EleMessage.error(res.msg);
            });
          }).catch(() => {
            EleMessage.info('已取消');
          });
        }*/
      }
    },
    { immediate: true } // 立即执行一次回调
  );

  /* 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  const goToNext = () => {
    emit('done');
    updateModelValue(false);
  };
  const markFail = () => {
    ElMessageBox.prompt(`是否确定标记不合格？`, '提示', {
      inputPlaceholder: '请输入原因',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputErrorMessage: '原因不能为空',
      inputValidator: (value) => {
        if (!value) {
          return '原因不能为空';
        }
      }
    }).then(async ({ value }) => {
      try {
        await markUnqualified({
          type: props.data.type,
          guid: props.data.guid,
          reason: value
        }).then((res) => {
          console.log(res);
          EleMessage.success(`标记成功！`);
          emit('done');
          updateModelValue(false);
        });
      } catch (e) {
        EleMessage.error(e);
      }
    });
  };
  const releaseFlagClick = () => {
    release({
      guid: props.data.guid
    }).then((res) => {
      if (res.code == 200) {
        EleMessage.success('放行成功');
        emit('done');
        updateModelValue(false);
        return;
      }
      EleMessage.error(res.msg);
    });
  };
</script>

<style lang="scss" scoped>
  .result-icon {
    background: #00B050;
    width: 22px;
    height: 22px;
    border-radius: 50%;
  }
  .result-icon2 {
    background: #FF4D4F;
    width: 22px;
    height: 22px;
    border-radius: 50%;
  }
  .result-icon3 {
    width: 26px;
    height: 26px;
    border-radius: 50%;
  }
  .filter-wapper {
    // max-height: 600px;
    // overflow-y: auto;
  }
  :deep(.el-table td.el-table__cell) {
    height: 26px !important;
  }
  :deep(.el-table--large .el-table__cell) {
    padding: 3px 0 !important;
  }
</style>
