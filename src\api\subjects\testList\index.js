import request from '@/utils/subjectsrequest';
import { download } from '@/utils/common';
import dayjs from 'dayjs';
let unique = 0;

function uuid(prefix) {
  const time = Date.now();
  const random = Math.floor(Math.random() * 1000000000);
  unique++;
  return prefix + '_' + random + unique + String(time);
}
/**
 * 志愿者数据维护列表
 */
export async function joinSubjectList(data) {
  const res = await request.post(`/sys/subjecttrialdata/joinSubjectList`, data);
  if (res.data.code === 200) {
    res.data.data.list.forEach((item) => {
      item.uuid = uuid('uid');
    });
    return res.data.data;
  }
  return res.data;
}

/**
 * 志愿者数据维护列表导出
 */
export async function joinExport(data) {
  const res = await request({
    url: '/sys/subjecttrialdata/joinExport',
    method: 'POST',
    data: data,
    responseType: 'blob'
  });
  download(
    res.data,
    `志愿者信息_${dayjs(new Date()).format('YYYYMMDDHHMMssSSS')}.xlsx`
  );
  return 'success';
}

/**
 * 机构试验数据维护列表
 */
export async function subjecttrialdataList(data) {
  const res = await request.post(`/sys/subjecttrialdata/list`, data);
  if (res.data.code === 200) {
    res.data.data.list.forEach((item) => {
      item.uuid = uuid('uid');
    });
    return res.data.data;
  }
  return res.data;
}

/**
 * 机构试验数据维护列表导出
 */
export async function subjecttrialdataExport(data) {
  const res = await request({
    url: '/sys/subjecttrialdata/export',
    method: 'POST',
    data: data,
    responseType: 'blob'
  });
  download(
    res.data,
    `志愿者信息_${dayjs(new Date()).format('YYYYMMDDHHMMssSSS')}.xlsx`
  );
  return 'success';
}
/**
 * 受试者照片信息
 */
export async function pictureUrl(params) {
  const res = await request.get(`/sys/subjecttrialdata/details/pictureUrl`, {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 机构试验数据维护列表-详情
 */
export async function subjecttrialdataInfo(data) {
  const res = await request.get(`/sys/subjecttrialdata/info/${data.guid}`);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 查询试验信息列表
 */
export async function trialsList(data) {
  const res = await request.post(`/sys/trials/list`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return res.data;
}
/**
 * 查询机构信息列表
 */
export async function organizationList(data) {
  const res = await request.post(`/sys/organization/list`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return res.data;
}
/**
 * 保存机构信息
 */
export async function organizationSave(data) {
  const res = await request.post(`/sys/organization/save`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 修改机构信息
 */
export async function organizationUpdate(data) {
  const res = await request.post(`/sys/organization/update`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 查询机构信息信息
 */
export async function organizationInfo(data) {
  const res = await request.get(`/sys/organization/info/${data.guid}`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 机构试验数据维护列表-批量修改
 */
export async function batchUpdate(data) {
  const res = await request.post(`/sys/subjecttrialdata/batchUpdate`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 机构试验数据维护列表-批量修改（新版）
 */
export async function batchUpdateData(data) {
  const res = await request.post(`/sys/subjecttrialdata/batchUpdateData`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 删除受试者受试信息
 */
export async function deleteSubjectTrial(params) {
  const res = await request.get(`/sys/subjecttrialdata/deleteSubjectTrial`, {
    params
  });
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
/**
 * 试验筛查报告单预览/导出-新
 */
export async function subjecttrialdataReporting(trialId) {
  const res = await request({
    url: `/sys/subjecttrialdata/reporting/${trialId}`,
    method: 'POST',
    responseType: 'blob'
  });
  download(
    res.data,
    `试验筛查报告单_${dayjs(new Date()).format('YYYYMMDDHHMMssSSS')}.docx`
  );
  return 'success';
}

/**
 * 招募列表
 */
export async function recruitList(data) {
  const res = await request.post(`/sys/subject/recruitList`, data);
  if (res.data.code === 200) {
    return res.data.data;
  }
  return res.data;
}

/**
 * 发送短信
 */
export async function smsRecruit(data) {
  const res = await request.post(`/sys/subject/smsRecruit`, data);
  if (res.data.code === 200) {
    return res.data;
  }
  return res.data;
}
